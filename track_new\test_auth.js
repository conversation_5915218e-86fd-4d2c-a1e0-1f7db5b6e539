/**
 * Test Authentication System
 */

const BASE_URL = 'http://localhost:8080';

async function testAuth() {
  console.log('🧪 Testing Authentication System...');
  
  try {
    // Test 1: Check if auth endpoint exists
    console.log('\n1. Testing auth endpoint availability...');
    const authResponse = await fetch(`${BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'admin123'
      })
    });
    
    console.log('Auth endpoint status:', authResponse.status);
    const authText = await authResponse.text();
    console.log('Auth response:', authText.substring(0, 300));
    
    // Test 2: Try different login credentials
    console.log('\n2. Testing different login credentials...');
    const credentials = [
      { email: '<EMAIL>', password: 'admin123' },
      { username: 'admin', password: 'admin123' },
      { email: 'admin', password: 'admin123' },
      { email: '<EMAIL>', password: 'test123' }
    ];
    
    for (const cred of credentials) {
      const response = await fetch(`${BASE_URL}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(cred)
      });
      
      console.log(`Credentials ${JSON.stringify(cred)}: Status ${response.status}`);
      const text = await response.text();
      
      if (response.ok) {
        try {
          const data = JSON.parse(text);
          if (data.token) {
            console.log('✅ Got valid token:', data.token.substring(0, 50) + '...');
            
            // Test the token
            const testResponse = await fetch(`${BASE_URL}/api/customer-categories`, {
              headers: {
                'Authorization': `Bearer ${data.token}`,
                'Content-Type': 'application/json'
              }
            });
            
            console.log('Token test status:', testResponse.status);
            const testText = await testResponse.text();
            console.log('Token test response:', testText.substring(0, 200));
            
            return data.token; // Return valid token
          }
        } catch (e) {
          console.log('Could not parse response as JSON');
        }
      } else {
        console.log('Response:', text.substring(0, 100));
      }
    }
    
    // Test 3: Check if we need to create a user first
    console.log('\n3. Testing user registration...');
    const registerResponse = await fetch(`${BASE_URL}/api/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        name: 'Test Admin',
        email: '<EMAIL>',
        password: 'admin123',
        role: 'admin'
      })
    });
    
    console.log('Register status:', registerResponse.status);
    const registerText = await registerResponse.text();
    console.log('Register response:', registerText.substring(0, 200));
    
    // Test 4: Check database directly
    console.log('\n4. Testing direct database access...');
    const dbResponse = await fetch(`${BASE_URL}/health`);
    console.log('Health check status:', dbResponse.status);
    const healthText = await dbResponse.text();
    console.log('Health response:', healthText);
    
  } catch (error) {
    console.error('❌ Auth test failed:', error.message);
  }
}

testAuth();
