/**
 * Test script for Category Stats endpoints
 */

import axios from 'axios';

const BASE_URL = 'http://localhost:8080/api';

async function testStatsEndpoints() {
  console.log('🧪 Testing Category Stats Endpoints...\n');

  try {
    // Step 1: Login to get token
    console.log('1. Testing authentication...');
    const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    });

    if (loginResponse.status === 200 && loginResponse.data.data.token) {
      console.log('✅ Authentication successful');
      const token = loginResponse.data.data.token;
      const headers = { Authorization: `Bearer ${token}` };

      // Step 2: Test Service Categories Stats
      console.log('2. Testing Service Categories Stats...');
      try {
        const serviceStatsResponse = await axios.get(`${BASE_URL}/service_categories/stats`, { headers });
        console.log('✅ Service Categories Stats successful');
        console.log('   Service Stats:', JSON.stringify(serviceStatsResponse.data.data, null, 2));
      } catch (error) {
        console.log('❌ Service Categories Stats failed:', error.response?.status, error.response?.data?.message || error.message);
      }

      // Step 3: Test Customer Categories Stats
      console.log('3. Testing Customer Categories Stats...');
      try {
        const customerStatsResponse = await axios.get(`${BASE_URL}/customer-categories/stats`, { headers });
        console.log('✅ Customer Categories Stats successful');
        console.log('   Customer Stats:', JSON.stringify(customerStatsResponse.data.data, null, 2));
      } catch (error) {
        console.log('❌ Customer Categories Stats failed:', error.response?.status, error.response?.data?.message || error.message);
      }

      // Step 4: Test Service Categories List (for comparison)
      console.log('4. Testing Service Categories List...');
      try {
        const serviceCategoriesResponse = await axios.get(`${BASE_URL}/service_categories?limit=5`, { headers });
        console.log('✅ Service Categories List successful');
        console.log(`   Found ${serviceCategoriesResponse.data.data.categories.length} service categories`);
      } catch (error) {
        console.log('❌ Service Categories List failed:', error.response?.status, error.response?.data?.message || error.message);
      }

      // Step 5: Test Customer Categories List (for comparison)
      console.log('5. Testing Customer Categories List...');
      try {
        const customerCategoriesResponse = await axios.get(`${BASE_URL}/customer-categories?limit=5`, { headers });
        console.log('✅ Customer Categories List successful');
        console.log(`   Found ${customerCategoriesResponse.data.data.categories.length} customer categories`);
      } catch (error) {
        console.log('❌ Customer Categories List failed:', error.response?.status, error.response?.data?.message || error.message);
      }

    } else {
      console.log('❌ Authentication failed');
      return;
    }

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }

  console.log('\n🎯 Category Stats Endpoints Test Complete!');
}

testStatsEndpoints();
