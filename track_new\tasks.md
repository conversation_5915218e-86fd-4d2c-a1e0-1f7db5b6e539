# TrackNew Tasks

task 1  now we want to migrate service functionlaity from old project to new project i already implemented some thing please verefy and start working on that 

task 1.1  check old project service models how many variable are there in service i need that all the variable i will give below some varible if i can miss some thing please check and that all model from service from old project follow end to end from old project to new project 
Created At
Updated At
Service Id
Problem Title
Customer
Category
Expected Date
Assign To
Service Type
Status
Created At
Updated At
Service Id
Problem Title
Customer
Category
Expected Date
Assign To
Service Type
Invoice Id
Status
Created By
Updated By
Amc
Actions

task 1.2 based on the Service Categories i need to create service , please follow the end to end functionality from old project to new project , how is service creation flow in old project follow that same in new project 

task 1.3 in add new service button if this click i need to show all the service Categories using that i need to create service ,in this if you have any doubt please check old project service creation flow

task 1.4 


task 2 Service Categories i need this Service Categories from old project to new project migrated end to end functionlaity 

task 2.1 in Service Categories top i need to show overal Service Categories counts , i need to show real data from db check old project how i show there 

task 2.2 in Service Categories  page there is one more function called Customer Categories in that also we need to show Customer Categories counts , i need to show real data from db check old project how i show there 

task 2.3 in Categories page i need to show both Service Categories and Customer Categories  button if i choose service categories it will show service categories related data same for customer categories 

task 2.4  in service Categories check how many variable follow in old project i need that same varible to new project 

task 2.5  in Customer Categories check how many variable follow in old project i need that same varible to new project

task 2.6  in Service Categories page i need to show all the record from real  db ,

task 2.7  in Customer Categories page i need to show all the record from real  db ,

task 2.8  in Service Categories CRUD functionlaity need to implement same as old project Service Categories , and all the API responce should 200 status compalsery 

task 2.9  in Customer Categories CRUD functionlaity need to implement same as old project Customer Categories , and all the API responce should 200 status compalsery 

task 2.10 in Service Categories UI and integration properly without any issue 

task 2.11 in Customer Categories UI and integration properly without any issue 

task 2.12 in Service Categories  all the API should 200 status compalsery 

task 2.13 in Customer Categories  all the API should 200 status compalsery 

task 2.14 in Service Categories there is form functionality need to implement same as old project Service Categories 

task 2.15 in Service Categorie in form there is loot of functionality in that i need all the validation same as old project Service Categories 

task 2.16 in Service Categories form we can customize the form fields like we can hide some fields and show some fields based on the user role and permission, please check old project how i do that there 

task 2.17 in Service Categories i need exact functionlaity from old project to new project dont miss any thing there is loot of this in this Service Categorie functionlity in old project i need all the thing to new project 

task 2.18 in Customer Categories i need exact functionlaity from old project to new project dont miss any thing there is loot of this in this Customer Categories functionlity in old project i need all the thing to new project 

task 2.19 while implementing each functionlaoty check old project how i do that there and implement same thing in new project 