<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create PWA Icon</title>
</head>
<body>
    <canvas id="canvas" width="192" height="192" style="border: 1px solid #ccc;"></canvas>
    <br><br>
    <button onclick="downloadIcon()">Download PWA Icon</button>

    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');

        // Create a professional TrackNew icon
        function createIcon() {
            // Background gradient (teal to orange)
            const gradient = ctx.createLinearGradient(0, 0, 192, 192);
            gradient.addColorStop(0, '#14b8a6'); // teal-500
            gradient.addColorStop(1, '#f97316'); // orange-500
            
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, 192, 192);

            // Add rounded corners effect
            ctx.globalCompositeOperation = 'destination-in';
            ctx.beginPath();
            ctx.roundRect(0, 0, 192, 192, 24);
            ctx.fill();
            ctx.globalCompositeOperation = 'source-over';

            // Add "TN" text in white
            ctx.fillStyle = '#ffffff';
            ctx.font = 'bold 72px Arial, sans-serif';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('TN', 96, 96);

            // Add subtle shadow to text
            ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
            ctx.fillText('TN', 98, 98);
            
            // Re-add white text on top
            ctx.fillStyle = '#ffffff';
            ctx.fillText('TN', 96, 96);
        }

        // Create the icon when page loads
        createIcon();

        function downloadIcon() {
            const link = document.createElement('a');
            link.download = 'pwa-192x192.png';
            link.href = canvas.toDataURL('image/png');
            link.click();
        }
    </script>
</body>
</html>
