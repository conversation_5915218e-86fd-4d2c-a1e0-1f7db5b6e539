/**
 * Test CustomerCategory model directly
 */

import CustomerCategory from './backend/src/models/CustomerCategory.js';

async function testCustomerCategoryModel() {
  console.log('🧪 Testing CustomerCategory Model...\n');

  try {
    console.log('1. Testing model import...');
    console.log('CustomerCategory model:', CustomerCategory ? '✅ Imported' : '❌ Failed to import');

    console.log('2. Testing database connection...');
    
    // Test basic count without where clause
    const totalCount = await CustomerCategory.count();
    console.log('✅ Total customer categories in database:', totalCount);

    // Test with company filter
    const companyId = '8722167d-d2ae-498f-95a3-c18afcf427ae'; // Default company ID
    const companyCount = await CustomerCategory.count({ where: { companyId } });
    console.log('✅ Customer categories for company:', companyCount);

    // Test active count
    const activeCount = await CustomerCategory.count({ 
      where: { companyId, isActive: true } 
    });
    console.log('✅ Active customer categories:', activeCount);

    // Test findAll to see actual data
    const categories = await CustomerCategory.findAll({ 
      where: { companyId },
      limit: 3
    });
    console.log('✅ Sample categories:', categories.map(c => ({
      id: c.id,
      categoryName: c.categoryName,
      isActive: c.isActive,
      companyId: c.companyId
    })));

  } catch (error) {
    console.error('❌ Error testing CustomerCategory model:', error);
    console.error('Error details:', error.message);
    console.error('Stack trace:', error.stack);
  }

  console.log('\n🎯 CustomerCategory Model Test Complete!');
  process.exit(0);
}

testCustomerCategoryModel();
