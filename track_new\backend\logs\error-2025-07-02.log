{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m{\u001b[39m\n\u001b[31m  \"message\": \"Invalid token.\",\u001b[39m\n\u001b[31m  \"stack\": \"Error: Invalid token.\\n    at file:///D:/New_TrackNew/track_new/backend/src/middleware/auth.js:93:13\\n    at file:///D:/New_TrackNew/track_new/backend/src/utils/asyncHandler.js:7:21\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\\n    at trim_prefix (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:328:13)\\n    at D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:286:9\\n    at Function.process_params (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:346:12)\\n    at next (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:280:10)\\n    at Function.handle (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:175:3)\\n    at router (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:47:12)\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\",\u001b[39m\n\u001b[31m  \"statusCode\": 401,\u001b[39m\n\u001b[31m  \"timestamp\": \"2025-07-02T05:47:56.916Z\",\u001b[39m\n\u001b[31m  \"request\": {\u001b[39m\n\u001b[31m    \"method\": \"GET\",\u001b[39m\n\u001b[31m    \"url\": \"/api/service-categories?page=1&limit=12\",\u001b[39m\n\u001b[31m    \"ip\": \"::1\",\u001b[39m\n\u001b[31m    \"userAgent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"\u001b[39m\n\u001b[31m  }\u001b[39m\n\u001b[31m}\u001b[39m","timestamp":"2025-07-02 11:17:56:1756"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m{\u001b[39m\n\u001b[31m  \"message\": \"Invalid token.\",\u001b[39m\n\u001b[31m  \"stack\": \"Error: Invalid token.\\n    at file:///D:/New_TrackNew/track_new/backend/src/middleware/auth.js:93:13\\n    at file:///D:/New_TrackNew/track_new/backend/src/utils/asyncHandler.js:7:21\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\\n    at trim_prefix (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:328:13)\\n    at D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:286:9\\n    at Function.process_params (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:346:12)\\n    at next (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:280:10)\\n    at Function.handle (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:175:3)\\n    at router (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:47:12)\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\",\u001b[39m\n\u001b[31m  \"statusCode\": 401,\u001b[39m\n\u001b[31m  \"timestamp\": \"2025-07-02T05:47:56.971Z\",\u001b[39m\n\u001b[31m  \"request\": {\u001b[39m\n\u001b[31m    \"method\": \"GET\",\u001b[39m\n\u001b[31m    \"url\": \"/api/auth/profile\",\u001b[39m\n\u001b[31m    \"ip\": \"::1\",\u001b[39m\n\u001b[31m    \"userAgent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"\u001b[39m\n\u001b[31m  }\u001b[39m\n\u001b[31m}\u001b[39m","timestamp":"2025-07-02 11:17:57:1757"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m{\u001b[39m\n\u001b[31m  \"message\": \"Invalid token.\",\u001b[39m\n\u001b[31m  \"stack\": \"Error: Invalid token.\\n    at file:///D:/New_TrackNew/track_new/backend/src/middleware/auth.js:93:13\\n    at file:///D:/New_TrackNew/track_new/backend/src/utils/asyncHandler.js:7:21\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\\n    at trim_prefix (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:328:13)\\n    at D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:286:9\\n    at Function.process_params (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:346:12)\\n    at next (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:280:10)\\n    at Function.handle (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:175:3)\\n    at router (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:47:12)\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\",\u001b[39m\n\u001b[31m  \"statusCode\": 401,\u001b[39m\n\u001b[31m  \"timestamp\": \"2025-07-02T05:49:36.505Z\",\u001b[39m\n\u001b[31m  \"request\": {\u001b[39m\n\u001b[31m    \"method\": \"GET\",\u001b[39m\n\u001b[31m    \"url\": \"/api/service-categories?limit=1\",\u001b[39m\n\u001b[31m    \"ip\": \"::1\",\u001b[39m\n\u001b[31m    \"userAgent\": \"axios/1.10.0\"\u001b[39m\n\u001b[31m  }\u001b[39m\n\u001b[31m}\u001b[39m","timestamp":"2025-07-02 11:19:36:1936"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m{\u001b[39m\n\u001b[31m  \"message\": \"Invalid token.\",\u001b[39m\n\u001b[31m  \"stack\": \"Error: Invalid token.\\n    at file:///D:/New_TrackNew/track_new/backend/src/middleware/auth.js:93:13\\n    at file:///D:/New_TrackNew/track_new/backend/src/utils/asyncHandler.js:7:21\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\\n    at trim_prefix (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:328:13)\\n    at D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:286:9\\n    at Function.process_params (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:346:12)\\n    at next (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:280:10)\\n    at Function.handle (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:175:3)\\n    at router (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:47:12)\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\",\u001b[39m\n\u001b[31m  \"statusCode\": 401,\u001b[39m\n\u001b[31m  \"timestamp\": \"2025-07-02T05:49:36.527Z\",\u001b[39m\n\u001b[31m  \"request\": {\u001b[39m\n\u001b[31m    \"method\": \"GET\",\u001b[39m\n\u001b[31m    \"url\": \"/api/service-categories\",\u001b[39m\n\u001b[31m    \"ip\": \"::1\",\u001b[39m\n\u001b[31m    \"userAgent\": \"axios/1.10.0\"\u001b[39m\n\u001b[31m  }\u001b[39m\n\u001b[31m}\u001b[39m","timestamp":"2025-07-02 11:19:36:1936"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m{\u001b[39m\n\u001b[31m  \"message\": \"Invalid token.\",\u001b[39m\n\u001b[31m  \"stack\": \"Error: Invalid token.\\n    at file:///D:/New_TrackNew/track_new/backend/src/middleware/auth.js:93:13\\n    at file:///D:/New_TrackNew/track_new/backend/src/utils/asyncHandler.js:7:21\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\\n    at trim_prefix (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:328:13)\\n    at D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:286:9\\n    at Function.process_params (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:346:12)\\n    at next (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:280:10)\\n    at Function.handle (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:175:3)\\n    at router (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:47:12)\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\",\u001b[39m\n\u001b[31m  \"statusCode\": 401,\u001b[39m\n\u001b[31m  \"timestamp\": \"2025-07-02T05:49:36.554Z\",\u001b[39m\n\u001b[31m  \"request\": {\u001b[39m\n\u001b[31m    \"method\": \"GET\",\u001b[39m\n\u001b[31m    \"url\": \"/api/customer-categories\",\u001b[39m\n\u001b[31m    \"ip\": \"::1\",\u001b[39m\n\u001b[31m    \"userAgent\": \"axios/1.10.0\"\u001b[39m\n\u001b[31m  }\u001b[39m\n\u001b[31m}\u001b[39m","timestamp":"2025-07-02 11:19:36:1936"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m{\u001b[39m\n\u001b[31m  \"message\": \"Invalid token.\",\u001b[39m\n\u001b[31m  \"stack\": \"Error: Invalid token.\\n    at file:///D:/New_TrackNew/track_new/backend/src/middleware/auth.js:93:13\\n    at file:///D:/New_TrackNew/track_new/backend/src/utils/asyncHandler.js:7:21\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\\n    at trim_prefix (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:328:13)\\n    at D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:286:9\\n    at Function.process_params (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:346:12)\\n    at next (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:280:10)\\n    at Function.handle (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:175:3)\\n    at router (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:47:12)\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\",\u001b[39m\n\u001b[31m  \"statusCode\": 401,\u001b[39m\n\u001b[31m  \"timestamp\": \"2025-07-02T05:49:36.573Z\",\u001b[39m\n\u001b[31m  \"request\": {\u001b[39m\n\u001b[31m    \"method\": \"GET\",\u001b[39m\n\u001b[31m    \"url\": \"/api/service-categories?search=test\",\u001b[39m\n\u001b[31m    \"ip\": \"::1\",\u001b[39m\n\u001b[31m    \"userAgent\": \"axios/1.10.0\"\u001b[39m\n\u001b[31m  }\u001b[39m\n\u001b[31m}\u001b[39m","timestamp":"2025-07-02 11:19:36:1936"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m{\u001b[39m\n\u001b[31m  \"message\": \"Access denied. No token provided.\",\u001b[39m\n\u001b[31m  \"stack\": \"Error: Access denied. No token provided.\\n    at file:///D:/New_TrackNew/track_new/backend/src/middleware/auth.js:43:11\\n    at file:///D:/New_TrackNew/track_new/backend/src/utils/asyncHandler.js:7:21\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\\n    at trim_prefix (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:328:13)\\n    at D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:286:9\\n    at Function.process_params (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:346:12)\\n    at next (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:280:10)\\n    at Function.handle (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:175:3)\\n    at router (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:47:12)\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\",\u001b[39m\n\u001b[31m  \"statusCode\": 401,\u001b[39m\n\u001b[31m  \"timestamp\": \"2025-07-02T05:49:57.058Z\",\u001b[39m\n\u001b[31m  \"request\": {\u001b[39m\n\u001b[31m    \"method\": \"GET\",\u001b[39m\n\u001b[31m    \"url\": \"/api/service-categories?limit=5\",\u001b[39m\n\u001b[31m    \"ip\": \"::1\",\u001b[39m\n\u001b[31m    \"userAgent\": \"curl/8.12.1\"\u001b[39m\n\u001b[31m  }\u001b[39m\n\u001b[31m}\u001b[39m","timestamp":"2025-07-02 11:19:57:1957"}
