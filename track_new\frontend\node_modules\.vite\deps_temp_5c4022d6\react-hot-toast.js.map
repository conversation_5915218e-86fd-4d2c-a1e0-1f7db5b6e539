{"version": 3, "sources": ["../../react-hot-toast/src/core/types.ts", "../../react-hot-toast/src/core/utils.ts", "../../react-hot-toast/src/core/store.ts", "../../react-hot-toast/src/core/toast.ts", "../../react-hot-toast/src/core/use-toaster.ts", "../../react-hot-toast/src/components/toast-bar.tsx", "../../react-hot-toast/src/components/toast-icon.tsx", "../../react-hot-toast/src/components/error.tsx", "../../react-hot-toast/src/components/loader.tsx", "../../react-hot-toast/src/components/checkmark.tsx", "../../react-hot-toast/src/components/toaster.tsx", "../../react-hot-toast/src/index.ts", "../../goober/dist/goober.modern.js"], "sourcesContent": ["import { CSSProperties } from 'react';\n\nexport type ToastType = 'success' | 'error' | 'loading' | 'blank' | 'custom';\nexport type ToastPosition =\n  | 'top-left'\n  | 'top-center'\n  | 'top-right'\n  | 'bottom-left'\n  | 'bottom-center'\n  | 'bottom-right';\n\nexport type Renderable = React.ReactElement | string | null;\n\nexport interface IconTheme {\n  primary: string;\n  secondary: string;\n}\n\nexport type ValueFunction<TValue, TArg> = (arg: TArg) => TValue;\nexport type ValueOrFunction<TValue, TArg> =\n  | TValue\n  | ValueFunction<TValue, TArg>;\n\nconst isFunction = <TValue, TArg>(\n  valOrFunction: ValueOrFunction<TValue, TArg>\n): valOrFunction is ValueFunction<TValue, TArg> =>\n  typeof valOrFunction === 'function';\n\nexport const resolveValue = <TValue, TArg>(\n  valOrFunction: ValueOrFunction<TValue, TArg>,\n  arg: TArg\n): TValue => (isFunction(valOrFunction) ? valOrFunction(arg) : valOrFunction);\n\nexport interface Toast {\n  type: ToastType;\n  id: string;\n  message: ValueOrFunction<Renderable, Toast>;\n  icon?: Renderable;\n  duration?: number;\n  pauseDuration: number;\n  position?: ToastPosition;\n  removeDelay?: number;\n\n  ariaProps: {\n    role: 'status' | 'alert';\n    'aria-live': 'assertive' | 'off' | 'polite';\n  };\n\n  style?: CSSProperties;\n  className?: string;\n  iconTheme?: IconTheme;\n\n  createdAt: number;\n  visible: boolean;\n  dismissed: boolean;\n  height?: number;\n}\n\nexport type ToastOptions = Partial<\n  Pick<\n    Toast,\n    | 'id'\n    | 'icon'\n    | 'duration'\n    | 'ariaProps'\n    | 'className'\n    | 'style'\n    | 'position'\n    | 'iconTheme'\n    | 'removeDelay'\n  >\n>;\n\nexport type DefaultToastOptions = ToastOptions & {\n  [key in ToastType]?: ToastOptions;\n};\n\nexport interface ToasterProps {\n  position?: ToastPosition;\n  toastOptions?: DefaultToastOptions;\n  reverseOrder?: boolean;\n  gutter?: number;\n  containerStyle?: React.CSSProperties;\n  containerClassName?: string;\n  children?: (toast: Toast) => React.ReactElement;\n}\n\nexport interface ToastWrapperProps {\n  id: string;\n  className?: string;\n  style?: React.CSSProperties;\n  onHeightUpdate: (id: string, height: number) => void;\n  children?: React.ReactNode;\n}\n", "export const genId = (() => {\n  let count = 0;\n  return () => {\n    return (++count).toString();\n  };\n})();\n\nexport const prefersReducedMotion = (() => {\n  // Cache result\n  let shouldReduceMotion: boolean | undefined = undefined;\n\n  return () => {\n    if (shouldReduceMotion === undefined && typeof window !== 'undefined') {\n      const mediaQuery = matchMedia('(prefers-reduced-motion: reduce)');\n      shouldReduceMotion = !mediaQuery || mediaQuery.matches;\n    }\n    return shouldReduceMotion;\n  };\n})();\n", "import { useEffect, useState, useRef } from 'react';\nimport { DefaultToastOptions, Toast, ToastType } from './types';\n\nconst TOAST_LIMIT = 20;\n\nexport enum ActionType {\n  ADD_TOAST,\n  UPDATE_TOAST,\n  UPSERT_TOAST,\n  DISMISS_TOAST,\n  REMOVE_TOAST,\n  START_PAUSE,\n  END_PAUSE,\n}\n\ntype Action =\n  | {\n      type: ActionType.ADD_TOAST;\n      toast: Toast;\n    }\n  | {\n      type: ActionType.UPSERT_TOAST;\n      toast: Toast;\n    }\n  | {\n      type: ActionType.UPDATE_TOAST;\n      toast: Partial<Toast>;\n    }\n  | {\n      type: ActionType.DISMISS_TOAST;\n      toastId?: string;\n    }\n  | {\n      type: ActionType.REMOVE_TOAST;\n      toastId?: string;\n    }\n  | {\n      type: ActionType.START_PAUSE;\n      time: number;\n    }\n  | {\n      type: ActionType.END_PAUSE;\n      time: number;\n    };\n\ninterface State {\n  toasts: Toast[];\n  pausedAt: number | undefined;\n}\n\nexport const reducer = (state: State, action: Action): State => {\n  switch (action.type) {\n    case ActionType.ADD_TOAST:\n      return {\n        ...state,\n        toasts: [action.toast, ...state.toasts].slice(0, TOAST_LIMIT),\n      };\n\n    case ActionType.UPDATE_TOAST:\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === action.toast.id ? { ...t, ...action.toast } : t\n        ),\n      };\n\n    case ActionType.UPSERT_TOAST:\n      const { toast } = action;\n      return reducer(state, {\n        type: state.toasts.find((t) => t.id === toast.id)\n          ? ActionType.UPDATE_TOAST\n          : ActionType.ADD_TOAST,\n        toast,\n      });\n\n    case ActionType.DISMISS_TOAST:\n      const { toastId } = action;\n\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === toastId || toastId === undefined\n            ? {\n                ...t,\n                dismissed: true,\n                visible: false,\n              }\n            : t\n        ),\n      };\n    case ActionType.REMOVE_TOAST:\n      if (action.toastId === undefined) {\n        return {\n          ...state,\n          toasts: [],\n        };\n      }\n      return {\n        ...state,\n        toasts: state.toasts.filter((t) => t.id !== action.toastId),\n      };\n\n    case ActionType.START_PAUSE:\n      return {\n        ...state,\n        pausedAt: action.time,\n      };\n\n    case ActionType.END_PAUSE:\n      const diff = action.time - (state.pausedAt || 0);\n\n      return {\n        ...state,\n        pausedAt: undefined,\n        toasts: state.toasts.map((t) => ({\n          ...t,\n          pauseDuration: t.pauseDuration + diff,\n        })),\n      };\n  }\n};\n\nconst listeners: Array<(state: State) => void> = [];\n\nlet memoryState: State = { toasts: [], pausedAt: undefined };\n\nexport const dispatch = (action: Action) => {\n  memoryState = reducer(memoryState, action);\n  listeners.forEach((listener) => {\n    listener(memoryState);\n  });\n};\n\nexport const defaultTimeouts: {\n  [key in ToastType]: number;\n} = {\n  blank: 4000,\n  error: 4000,\n  success: 2000,\n  loading: Infinity,\n  custom: 4000,\n};\n\nexport const useStore = (toastOptions: DefaultToastOptions = {}): State => {\n  const [state, setState] = useState<State>(memoryState);\n  const initial = useRef(memoryState);\n\n  // TODO: Switch to useSyncExternalStore when targeting React 18+\n  useEffect(() => {\n    if (initial.current !== memoryState) {\n      setState(memoryState);\n    }\n    listeners.push(setState);\n    return () => {\n      const index = listeners.indexOf(setState);\n      if (index > -1) {\n        listeners.splice(index, 1);\n      }\n    };\n  }, []);\n\n  const mergedToasts = state.toasts.map((t) => ({\n    ...toastOptions,\n    ...toastOptions[t.type],\n    ...t,\n    removeDelay:\n      t.removeDelay ||\n      toastOptions[t.type]?.removeDelay ||\n      toastOptions?.removeDelay,\n    duration:\n      t.duration ||\n      toastOptions[t.type]?.duration ||\n      toastOptions?.duration ||\n      defaultTimeouts[t.type],\n    style: {\n      ...toastOptions.style,\n      ...toastOptions[t.type]?.style,\n      ...t.style,\n    },\n  }));\n\n  return {\n    ...state,\n    toasts: mergedToasts,\n  };\n};\n", "import {\n  Renderable,\n  Toast,\n  ToastOptions,\n  ToastType,\n  DefaultToastOptions,\n  ValueOrFunction,\n  resolveValue,\n} from './types';\nimport { genId } from './utils';\nimport { dispatch, ActionType } from './store';\n\ntype Message = ValueOrFunction<Renderable, Toast>;\n\ntype ToastHandler = (message: Message, options?: ToastOptions) => string;\n\nconst createToast = (\n  message: Message,\n  type: ToastType = 'blank',\n  opts?: ToastOptions\n): Toast => ({\n  createdAt: Date.now(),\n  visible: true,\n  dismissed: false,\n  type,\n  ariaProps: {\n    role: 'status',\n    'aria-live': 'polite',\n  },\n  message,\n  pauseDuration: 0,\n  ...opts,\n  id: opts?.id || genId(),\n});\n\nconst createHandler =\n  (type?: ToastType): ToastHandler =>\n  (message, options) => {\n    const toast = createToast(message, type, options);\n    dispatch({ type: ActionType.UPSERT_TOAST, toast });\n    return toast.id;\n  };\n\nconst toast = (message: Message, opts?: ToastOptions) =>\n  createHandler('blank')(message, opts);\n\ntoast.error = createHandler('error');\ntoast.success = createHandler('success');\ntoast.loading = createHandler('loading');\ntoast.custom = createHandler('custom');\n\ntoast.dismiss = (toastId?: string) => {\n  dispatch({\n    type: ActionType.DISMISS_TOAST,\n    toastId,\n  });\n};\n\ntoast.remove = (toastId?: string) =>\n  dispatch({ type: ActionType.REMOVE_TOAST, toastId });\n\ntoast.promise = <T>(\n  promise: Promise<T> | (() => Promise<T>),\n  msgs: {\n    loading: Renderable;\n    success?: ValueOrFunction<Renderable, T>;\n    error?: ValueOrFunction<Renderable, any>;\n  },\n  opts?: DefaultToastOptions\n) => {\n  const id = toast.loading(msgs.loading, { ...opts, ...opts?.loading });\n\n  if (typeof promise === 'function') {\n    promise = promise();\n  }\n\n  promise\n    .then((p) => {\n      const successMessage = msgs.success\n        ? resolveValue(msgs.success, p)\n        : undefined;\n\n      if (successMessage) {\n        toast.success(successMessage, {\n          id,\n          ...opts,\n          ...opts?.success,\n        });\n      } else {\n        toast.dismiss(id);\n      }\n      return p;\n    })\n    .catch((e) => {\n      const errorMessage = msgs.error ? resolveValue(msgs.error, e) : undefined;\n\n      if (errorMessage) {\n        toast.error(errorMessage, {\n          id,\n          ...opts,\n          ...opts?.error,\n        });\n      } else {\n        toast.dismiss(id);\n      }\n    });\n\n  return promise;\n};\n\nexport { toast };\n", "import { useEffect, useCallback } from 'react';\nimport { dispatch, ActionType, useStore } from './store';\nimport { toast } from './toast';\nimport { DefaultToastOptions, Toast, ToastPosition } from './types';\n\nconst updateHeight = (toastId: string, height: number) => {\n  dispatch({\n    type: ActionType.UPDATE_TOAST,\n    toast: { id: toastId, height },\n  });\n};\nconst startPause = () => {\n  dispatch({\n    type: ActionType.START_PAUSE,\n    time: Date.now(),\n  });\n};\n\nconst toastTimeouts = new Map<Toast['id'], ReturnType<typeof setTimeout>>();\n\nexport const REMOVE_DELAY = 1000;\n\nconst addToRemoveQueue = (toastId: string, removeDelay = REMOVE_DELAY) => {\n  if (toastTimeouts.has(toastId)) {\n    return;\n  }\n\n  const timeout = setTimeout(() => {\n    toastTimeouts.delete(toastId);\n    dispatch({\n      type: ActionType.REMOVE_TOAST,\n      toastId: toastId,\n    });\n  }, removeDelay);\n\n  toastTimeouts.set(toastId, timeout);\n};\n\nexport const useToaster = (toastOptions?: DefaultToastOptions) => {\n  const { toasts, pausedAt } = useStore(toastOptions);\n\n  useEffect(() => {\n    if (pausedAt) {\n      return;\n    }\n\n    const now = Date.now();\n    const timeouts = toasts.map((t) => {\n      if (t.duration === Infinity) {\n        return;\n      }\n\n      const durationLeft =\n        (t.duration || 0) + t.pauseDuration - (now - t.createdAt);\n\n      if (durationLeft < 0) {\n        if (t.visible) {\n          toast.dismiss(t.id);\n        }\n        return;\n      }\n      return setTimeout(() => toast.dismiss(t.id), durationLeft);\n    });\n\n    return () => {\n      timeouts.forEach((timeout) => timeout && clearTimeout(timeout));\n    };\n  }, [toasts, pausedAt]);\n\n  const endPause = useCallback(() => {\n    if (pausedAt) {\n      dispatch({ type: ActionType.END_PAUSE, time: Date.now() });\n    }\n  }, [pausedAt]);\n\n  const calculateOffset = useCallback(\n    (\n      toast: Toast,\n      opts?: {\n        reverseOrder?: boolean;\n        gutter?: number;\n        defaultPosition?: ToastPosition;\n      }\n    ) => {\n      const { reverseOrder = false, gutter = 8, defaultPosition } = opts || {};\n\n      const relevantToasts = toasts.filter(\n        (t) =>\n          (t.position || defaultPosition) ===\n            (toast.position || defaultPosition) && t.height\n      );\n      const toastIndex = relevantToasts.findIndex((t) => t.id === toast.id);\n      const toastsBefore = relevantToasts.filter(\n        (toast, i) => i < toastIndex && toast.visible\n      ).length;\n\n      const offset = relevantToasts\n        .filter((t) => t.visible)\n        .slice(...(reverseOrder ? [toastsBefore + 1] : [0, toastsBefore]))\n        .reduce((acc, t) => acc + (t.height || 0) + gutter, 0);\n\n      return offset;\n    },\n    [toasts]\n  );\n\n  useEffect(() => {\n    // Add dismissed toasts to remove queue\n    toasts.forEach((toast) => {\n      if (toast.dismissed) {\n        addToRemoveQueue(toast.id, toast.removeDelay);\n      } else {\n        // If toast becomes visible again, remove it from the queue\n        const timeout = toastTimeouts.get(toast.id);\n        if (timeout) {\n          clearTimeout(timeout);\n          toastTimeouts.delete(toast.id);\n        }\n      }\n    });\n  }, [toasts]);\n\n  return {\n    toasts,\n    handlers: {\n      updateHeight,\n      startPause,\n      endPause,\n      calculateOffset,\n    },\n  };\n};\n", "import * as React from 'react';\nimport { styled, keyframes } from 'goober';\n\nimport { Toast, ToastPosition, resolveValue, Renderable } from '../core/types';\nimport { ToastIcon } from './toast-icon';\nimport { prefersReducedMotion } from '../core/utils';\n\nconst enterAnimation = (factor: number) => `\n0% {transform: translate3d(0,${factor * -200}%,0) scale(.6); opacity:.5;}\n100% {transform: translate3d(0,0,0) scale(1); opacity:1;}\n`;\n\nconst exitAnimation = (factor: number) => `\n0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}\n100% {transform: translate3d(0,${factor * -150}%,-1px) scale(.6); opacity:0;}\n`;\n\nconst fadeInAnimation = `0%{opacity:0;} 100%{opacity:1;}`;\nconst fadeOutAnimation = `0%{opacity:1;} 100%{opacity:0;}`;\n\nconst ToastBarBase = styled('div')`\n  display: flex;\n  align-items: center;\n  background: #fff;\n  color: #363636;\n  line-height: 1.3;\n  will-change: transform;\n  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);\n  max-width: 350px;\n  pointer-events: auto;\n  padding: 8px 10px;\n  border-radius: 8px;\n`;\n\nconst Message = styled('div')`\n  display: flex;\n  justify-content: center;\n  margin: 4px 10px;\n  color: inherit;\n  flex: 1 1 auto;\n  white-space: pre-line;\n`;\n\ninterface ToastBarProps {\n  toast: Toast;\n  position?: ToastPosition;\n  style?: React.CSSProperties;\n  children?: (components: {\n    icon: Renderable;\n    message: Renderable;\n  }) => Renderable;\n}\n\nconst getAnimationStyle = (\n  position: ToastPosition,\n  visible: boolean\n): React.CSSProperties => {\n  const top = position.includes('top');\n  const factor = top ? 1 : -1;\n\n  const [enter, exit] = prefersReducedMotion()\n    ? [fadeInAnimation, fadeOutAnimation]\n    : [enterAnimation(factor), exitAnimation(factor)];\n\n  return {\n    animation: visible\n      ? `${keyframes(enter)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`\n      : `${keyframes(exit)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`,\n  };\n};\n\nexport const ToastBar: React.FC<ToastBarProps> = React.memo(\n  ({ toast, position, style, children }) => {\n    const animationStyle: React.CSSProperties = toast.height\n      ? getAnimationStyle(\n          toast.position || position || 'top-center',\n          toast.visible\n        )\n      : { opacity: 0 };\n\n    const icon = <ToastIcon toast={toast} />;\n    const message = (\n      <Message {...toast.ariaProps}>\n        {resolveValue(toast.message, toast)}\n      </Message>\n    );\n\n    return (\n      <ToastBarBase\n        className={toast.className}\n        style={{\n          ...animationStyle,\n          ...style,\n          ...toast.style,\n        }}\n      >\n        {typeof children === 'function' ? (\n          children({\n            icon,\n            message,\n          })\n        ) : (\n          <>\n            {icon}\n            {message}\n          </>\n        )}\n      </ToastBarBase>\n    );\n  }\n);\n", "import * as React from 'react';\nimport { styled, keyframes } from 'goober';\n\nimport { Toast } from '../core/types';\nimport { ErrorIcon, ErrorTheme } from './error';\nimport { LoaderIcon, LoaderTheme } from './loader';\nimport { CheckmarkIcon, CheckmarkTheme } from './checkmark';\n\nconst StatusWrapper = styled('div')`\n  position: absolute;\n`;\n\nconst IndicatorWrapper = styled('div')`\n  position: relative;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-width: 20px;\n  min-height: 20px;\n`;\n\nconst enter = keyframes`\nfrom {\n  transform: scale(0.6);\n  opacity: 0.4;\n}\nto {\n  transform: scale(1);\n  opacity: 1;\n}`;\n\nexport const AnimatedIconWrapper = styled('div')`\n  position: relative;\n  transform: scale(0.6);\n  opacity: 0.4;\n  min-width: 20px;\n  animation: ${enter} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)\n    forwards;\n`;\n\nexport type IconThemes = Partial<{\n  success: CheckmarkTheme;\n  error: ErrorTheme;\n  loading: LoaderTheme;\n}>;\n\nexport const ToastIcon: React.FC<{\n  toast: Toast;\n}> = ({ toast }) => {\n  const { icon, type, iconTheme } = toast;\n  if (icon !== undefined) {\n    if (typeof icon === 'string') {\n      return <AnimatedIconWrapper>{icon}</AnimatedIconWrapper>;\n    } else {\n      return icon;\n    }\n  }\n\n  if (type === 'blank') {\n    return null;\n  }\n\n  return (\n    <IndicatorWrapper>\n      <LoaderIcon {...iconTheme} />\n      {type !== 'loading' && (\n        <StatusWrapper>\n          {type === 'error' ? (\n            <ErrorIcon {...iconTheme} />\n          ) : (\n            <CheckmarkIcon {...iconTheme} />\n          )}\n        </StatusWrapper>\n      )}\n    </IndicatorWrapper>\n  );\n};\n", "import { styled, keyframes } from 'goober';\n\nconst circleAnimation = keyframes`\nfrom {\n  transform: scale(0) rotate(45deg);\n\topacity: 0;\n}\nto {\n transform: scale(1) rotate(45deg);\n  opacity: 1;\n}`;\n\nconst firstLineAnimation = keyframes`\nfrom {\n  transform: scale(0);\n  opacity: 0;\n}\nto {\n  transform: scale(1);\n  opacity: 1;\n}`;\n\nconst secondLineAnimation = keyframes`\nfrom {\n  transform: scale(0) rotate(90deg);\n\topacity: 0;\n}\nto {\n  transform: scale(1) rotate(90deg);\n\topacity: 1;\n}`;\n\nexport interface ErrorTheme {\n  primary?: string;\n  secondary?: string;\n}\n\nexport const ErrorIcon = styled('div')<ErrorTheme>`\n  width: 20px;\n  opacity: 0;\n  height: 20px;\n  border-radius: 10px;\n  background: ${(p) => p.primary || '#ff4b4b'};\n  position: relative;\n  transform: rotate(45deg);\n\n  animation: ${circleAnimation} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)\n    forwards;\n  animation-delay: 100ms;\n\n  &:after,\n  &:before {\n    content: '';\n    animation: ${firstLineAnimation} 0.15s ease-out forwards;\n    animation-delay: 150ms;\n    position: absolute;\n    border-radius: 3px;\n    opacity: 0;\n    background: ${(p) => p.secondary || '#fff'};\n    bottom: 9px;\n    left: 4px;\n    height: 2px;\n    width: 12px;\n  }\n\n  &:before {\n    animation: ${secondLineAnimation} 0.15s ease-out forwards;\n    animation-delay: 180ms;\n    transform: rotate(90deg);\n  }\n`;\n", "import { styled, keyframes } from 'goober';\n\nconst rotate = keyframes`\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n`;\n\nexport interface LoaderTheme {\n  primary?: string;\n  secondary?: string;\n}\n\nexport const LoaderIcon = styled('div')<LoaderTheme>`\n  width: 12px;\n  height: 12px;\n  box-sizing: border-box;\n  border: 2px solid;\n  border-radius: 100%;\n  border-color: ${(p) => p.secondary || '#e0e0e0'};\n  border-right-color: ${(p) => p.primary || '#616161'};\n  animation: ${rotate} 1s linear infinite;\n`;\n", "import { styled, keyframes } from 'goober';\n\nconst circleAnimation = keyframes`\nfrom {\n  transform: scale(0) rotate(45deg);\n\topacity: 0;\n}\nto {\n  transform: scale(1) rotate(45deg);\n\topacity: 1;\n}`;\n\nconst checkmarkAnimation = keyframes`\n0% {\n\theight: 0;\n\twidth: 0;\n\topacity: 0;\n}\n40% {\n  height: 0;\n\twidth: 6px;\n\topacity: 1;\n}\n100% {\n  opacity: 1;\n  height: 10px;\n}`;\n\nexport interface CheckmarkTheme {\n  primary?: string;\n  secondary?: string;\n}\n\nexport const CheckmarkIcon = styled('div')<CheckmarkTheme>`\n  width: 20px;\n  opacity: 0;\n  height: 20px;\n  border-radius: 10px;\n  background: ${(p) => p.primary || '#61d345'};\n  position: relative;\n  transform: rotate(45deg);\n\n  animation: ${circleAnimation} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)\n    forwards;\n  animation-delay: 100ms;\n  &:after {\n    content: '';\n    box-sizing: border-box;\n    animation: ${checkmarkAnimation} 0.2s ease-out forwards;\n    opacity: 0;\n    animation-delay: 200ms;\n    position: absolute;\n    border-right: 2px solid;\n    border-bottom: 2px solid;\n    border-color: ${(p) => p.secondary || '#fff'};\n    bottom: 6px;\n    left: 6px;\n    height: 10px;\n    width: 6px;\n  }\n`;\n", "import { css, setup } from 'goober';\nimport * as React from 'react';\nimport {\n  resolveValue,\n  ToasterProps,\n  ToastPosition,\n  ToastWrapperProps,\n} from '../core/types';\nimport { useToaster } from '../core/use-toaster';\nimport { prefersReducedMotion } from '../core/utils';\nimport { ToastBar } from './toast-bar';\n\nsetup(React.createElement);\n\nconst ToastWrapper = ({\n  id,\n  className,\n  style,\n  onHeightUpdate,\n  children,\n}: ToastWrapperProps) => {\n  const ref = React.useCallback(\n    (el: HTMLElement | null) => {\n      if (el) {\n        const updateHeight = () => {\n          const height = el.getBoundingClientRect().height;\n          onHeightUpdate(id, height);\n        };\n        updateHeight();\n        new MutationObserver(updateHeight).observe(el, {\n          subtree: true,\n          childList: true,\n          characterData: true,\n        });\n      }\n    },\n    [id, onHeightUpdate]\n  );\n\n  return (\n    <div ref={ref} className={className} style={style}>\n      {children}\n    </div>\n  );\n};\n\nconst getPositionStyle = (\n  position: ToastPosition,\n  offset: number\n): React.CSSProperties => {\n  const top = position.includes('top');\n  const verticalStyle: React.CSSProperties = top ? { top: 0 } : { bottom: 0 };\n  const horizontalStyle: React.CSSProperties = position.includes('center')\n    ? {\n        justifyContent: 'center',\n      }\n    : position.includes('right')\n    ? {\n        justifyContent: 'flex-end',\n      }\n    : {};\n  return {\n    left: 0,\n    right: 0,\n    display: 'flex',\n    position: 'absolute',\n    transition: prefersReducedMotion()\n      ? undefined\n      : `all 230ms cubic-bezier(.21,1.02,.73,1)`,\n    transform: `translateY(${offset * (top ? 1 : -1)}px)`,\n    ...verticalStyle,\n    ...horizontalStyle,\n  };\n};\n\nconst activeClass = css`\n  z-index: 9999;\n  > * {\n    pointer-events: auto;\n  }\n`;\n\nconst DEFAULT_OFFSET = 16;\n\nexport const Toaster: React.FC<ToasterProps> = ({\n  reverseOrder,\n  position = 'top-center',\n  toastOptions,\n  gutter,\n  children,\n  containerStyle,\n  containerClassName,\n}) => {\n  const { toasts, handlers } = useToaster(toastOptions);\n\n  return (\n    <div\n      id=\"_rht_toaster\"\n      style={{\n        position: 'fixed',\n        zIndex: 9999,\n        top: DEFAULT_OFFSET,\n        left: DEFAULT_OFFSET,\n        right: DEFAULT_OFFSET,\n        bottom: DEFAULT_OFFSET,\n        pointerEvents: 'none',\n        ...containerStyle,\n      }}\n      className={containerClassName}\n      onMouseEnter={handlers.startPause}\n      onMouseLeave={handlers.endPause}\n    >\n      {toasts.map((t) => {\n        const toastPosition = t.position || position;\n        const offset = handlers.calculateOffset(t, {\n          reverseOrder,\n          gutter,\n          defaultPosition: position,\n        });\n        const positionStyle = getPositionStyle(toastPosition, offset);\n\n        return (\n          <ToastWrapper\n            id={t.id}\n            key={t.id}\n            onHeightUpdate={handlers.updateHeight}\n            className={t.visible ? activeClass : ''}\n            style={positionStyle}\n          >\n            {t.type === 'custom' ? (\n              resolveValue(t.message, t)\n            ) : children ? (\n              children(t)\n            ) : (\n              <ToastBar toast={t} position={toastPosition} />\n            )}\n          </ToastWrapper>\n        );\n      })}\n    </div>\n  );\n};\n", "import { toast } from './core/toast';\n\nexport * from './headless';\n\nexport { ToastBar } from './components/toast-bar';\nexport { ToastIcon } from './components/toast-icon';\nexport { Toaster } from './components/toaster';\nexport { CheckmarkIcon } from './components/checkmark';\nexport { ErrorIcon } from './components/error';\nexport { LoaderIcon } from './components/loader';\n\nexport { toast };\nexport default toast;\n", "let e={data:\"\"},t=t=>\"object\"==typeof window?((t?t.querySelector(\"#_goober\"):window._goober)||Object.assign((t||document.head).appendChild(document.createElement(\"style\")),{innerHTML:\" \",id:\"_goober\"})).firstChild:t||e,r=e=>{let r=t(e),l=r.data;return r.data=\"\",l},l=/(?:([\\u0080-\\uFFFF\\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\\s*)/g,a=/\\/\\*[^]*?\\*\\/|  +/g,n=/\\n+/g,o=(e,t)=>{let r=\"\",l=\"\",a=\"\";for(let n in e){let c=e[n];\"@\"==n[0]?\"i\"==n[1]?r=n+\" \"+c+\";\":l+=\"f\"==n[1]?o(c,n):n+\"{\"+o(c,\"k\"==n[1]?\"\":t)+\"}\":\"object\"==typeof c?l+=o(c,t?t.replace(/([^,])+/g,e=>n.replace(/([^,]*:\\S+\\([^)]*\\))|([^,])+/g,t=>/&/.test(t)?t.replace(/&/g,e):e?e+\" \"+t:t)):n):null!=c&&(n=/^--/.test(n)?n:n.replace(/[A-Z]/g,\"-$&\").toLowerCase(),a+=o.p?o.p(n,c):n+\":\"+c+\";\")}return r+(t&&a?t+\"{\"+a+\"}\":a)+l},c={},s=e=>{if(\"object\"==typeof e){let t=\"\";for(let r in e)t+=r+s(e[r]);return t}return e},i=(e,t,r,i,p)=>{let u=s(e),d=c[u]||(c[u]=(e=>{let t=0,r=11;for(;t<e.length;)r=101*r+e.charCodeAt(t++)>>>0;return\"go\"+r})(u));if(!c[d]){let t=u!==e?e:(e=>{let t,r,o=[{}];for(;t=l.exec(e.replace(a,\"\"));)t[4]?o.shift():t[3]?(r=t[3].replace(n,\" \").trim(),o.unshift(o[0][r]=o[0][r]||{})):o[0][t[1]]=t[2].replace(n,\" \").trim();return o[0]})(e);c[d]=o(p?{[\"@keyframes \"+d]:t}:t,r?\"\":\".\"+d)}let f=r&&c.g?c.g:null;return r&&(c.g=c[d]),((e,t,r,l)=>{l?t.data=t.data.replace(l,e):-1===t.data.indexOf(e)&&(t.data=r?e+t.data:t.data+e)})(c[d],t,i,f),d},p=(e,t,r)=>e.reduce((e,l,a)=>{let n=t[a];if(n&&n.call){let e=n(r),t=e&&e.props&&e.props.className||/^go/.test(e)&&e;n=t?\".\"+t:e&&\"object\"==typeof e?e.props?\"\":o(e,\"\"):!1===e?\"\":e}return e+l+(null==n?\"\":n)},\"\");function u(e){let r=this||{},l=e.call?e(r.p):e;return i(l.unshift?l.raw?p(l,[].slice.call(arguments,1),r.p):l.reduce((e,t)=>Object.assign(e,t&&t.call?t(r.p):t),{}):l,t(r.target),r.g,r.o,r.k)}let d,f,g,b=u.bind({g:1}),h=u.bind({k:1});function m(e,t,r,l){o.p=t,d=e,f=r,g=l}function j(e,t){let r=this||{};return function(){let l=arguments;function a(n,o){let c=Object.assign({},n),s=c.className||a.className;r.p=Object.assign({theme:f&&f()},c),r.o=/ *go\\d+/.test(s),c.className=u.apply(r,l)+(s?\" \"+s:\"\"),t&&(c.ref=o);let i=e;return e[0]&&(i=c.as||e,delete c.as),g&&i[0]&&g(c),d(i,c)}return t?t(a):a}}export{u as css,r as extractCss,b as glob,h as keyframes,m as setup,j as styled};\n"], "mappings": ";;;;;;;;;AEAA,mBAA4C;AEA5C,IAAAA,gBAAuC;ACAvC,IAAAC,KAAuB;;;AOAvB,IAAI,IAAE,EAAC,MAAK,GAAE;AAAd,IAAgB,IAAE,CAAAC,OAAG,YAAU,OAAO,WAASA,KAAEA,GAAE,cAAc,UAAU,IAAE,OAAO,YAAU,OAAO,QAAQA,MAAG,SAAS,MAAM,YAAY,SAAS,cAAc,OAAO,CAAC,GAAE,EAAC,WAAU,KAAI,IAAG,UAAS,CAAC,GAAG,aAAWA,MAAG;AAAzN,IAAyQ,IAAE;AAA3Q,IAA+U,IAAE;AAAjV,IAAsW,IAAE;AAAxW,IAA+W,IAAE,CAACC,IAAEC,OAAI;AAAC,MAAI,IAAE,IAAGC,KAAE,IAAGC,KAAE;AAAG,WAAQC,MAAKJ,IAAE;AAAC,QAAIK,KAAEL,GAAEI,EAAC;AAAE,WAAKA,GAAE,CAAC,IAAE,OAAKA,GAAE,CAAC,IAAE,IAAEA,KAAE,MAAIC,KAAE,MAAIH,MAAG,OAAKE,GAAE,CAAC,IAAE,EAAEC,IAAED,EAAC,IAAEA,KAAE,MAAI,EAAEC,IAAE,OAAKD,GAAE,CAAC,IAAE,KAAGH,EAAC,IAAE,MAAI,YAAU,OAAOI,KAAEH,MAAG,EAAEG,IAAEJ,KAAEA,GAAE,QAAQ,YAAW,CAAAD,OAAGI,GAAE,QAAQ,iCAAgC,CAAAH,OAAG,IAAI,KAAKA,EAAC,IAAEA,GAAE,QAAQ,MAAKD,EAAC,IAAEA,KAAEA,KAAE,MAAIC,KAAEA,EAAC,CAAC,IAAEG,EAAC,IAAE,QAAMC,OAAID,KAAE,MAAM,KAAKA,EAAC,IAAEA,KAAEA,GAAE,QAAQ,UAAS,KAAK,EAAE,YAAY,GAAED,MAAG,EAAE,IAAE,EAAE,EAAEC,IAAEC,EAAC,IAAED,KAAE,MAAIC,KAAE;AAAA,EAAI;AAAC,SAAO,KAAGJ,MAAGE,KAAEF,KAAE,MAAIE,KAAE,MAAIA,MAAGD;AAAC;AAA3wB,IAA6wB,IAAE,CAAC;AAAhxB,IAAkxB,IAAE,CAAAF,OAAG;AAAC,MAAG,YAAU,OAAOA,IAAE;AAAC,QAAIC,KAAE;AAAG,aAAQ,KAAKD,GAAE,CAAAC,MAAG,IAAE,EAAED,GAAE,CAAC,CAAC;AAAE,WAAOC;AAAA,EAAC;AAAC,SAAOD;AAAC;AAAr2B,IAAu2B,IAAE,CAACA,IAAEC,IAAE,GAAEK,IAAEC,OAAI;AAAC,MAAIC,KAAE,EAAER,EAAC,GAAES,KAAE,EAAED,EAAC,MAAI,EAAEA,EAAC,KAAG,CAAAR,OAAG;AAAC,QAAIC,KAAE,GAAES,KAAE;AAAG,WAAKT,KAAED,GAAE,SAAQ,CAAAU,KAAE,MAAIA,KAAEV,GAAE,WAAWC,IAAG,MAAI;AAAE,WAAM,OAAKS;AAAA,EAAC,GAAGF,EAAC;AAAG,MAAG,CAAC,EAAEC,EAAC,GAAE;AAAC,QAAIR,KAAEO,OAAIR,KAAEA,MAAG,CAAAA,OAAG;AAAC,UAAIC,IAAES,IAAEC,KAAE,CAAC,CAAC,CAAC;AAAE,aAAKV,KAAE,EAAE,KAAKD,GAAE,QAAQ,GAAE,EAAE,CAAC,IAAG,CAAAC,GAAE,CAAC,IAAEU,GAAE,MAAM,IAAEV,GAAE,CAAC,KAAGS,KAAET,GAAE,CAAC,EAAE,QAAQ,GAAE,GAAG,EAAE,KAAK,GAAEU,GAAE,QAAQA,GAAE,CAAC,EAAED,EAAC,IAAEC,GAAE,CAAC,EAAED,EAAC,KAAG,CAAC,CAAC,KAAGC,GAAE,CAAC,EAAEV,GAAE,CAAC,CAAC,IAAEA,GAAE,CAAC,EAAE,QAAQ,GAAE,GAAG,EAAE,KAAK;AAAE,aAAOU,GAAE,CAAC;AAAA,IAAC,GAAGX,EAAC;AAAE,MAAES,EAAC,IAAE,EAAEF,KAAE,EAAC,CAAC,gBAAcE,EAAC,GAAER,GAAC,IAAEA,IAAE,IAAE,KAAG,MAAIQ,EAAC;AAAA,EAAC;AAAC,MAAIG,KAAE,KAAG,EAAE,IAAE,EAAE,IAAE;AAAK,SAAO,MAAI,EAAE,IAAE,EAAEH,EAAC,KAAI,CAACT,IAAEC,IAAES,IAAER,OAAI;AAAC,IAAAA,KAAED,GAAE,OAAKA,GAAE,KAAK,QAAQC,IAAEF,EAAC,IAAE,OAAKC,GAAE,KAAK,QAAQD,EAAC,MAAIC,GAAE,OAAKS,KAAEV,KAAEC,GAAE,OAAKA,GAAE,OAAKD;AAAA,EAAE,GAAG,EAAES,EAAC,GAAER,IAAEK,IAAEM,EAAC,GAAEH;AAAC;AAA/3C,IAAi4C,IAAE,CAACT,IAAEC,IAAE,MAAID,GAAE,OAAO,CAACA,IAAEE,IAAEC,OAAI;AAAC,MAAIC,KAAEH,GAAEE,EAAC;AAAE,MAAGC,MAAGA,GAAE,MAAK;AAAC,QAAIJ,KAAEI,GAAE,CAAC,GAAEH,KAAED,MAAGA,GAAE,SAAOA,GAAE,MAAM,aAAW,MAAM,KAAKA,EAAC,KAAGA;AAAE,IAAAI,KAAEH,KAAE,MAAIA,KAAED,MAAG,YAAU,OAAOA,KAAEA,GAAE,QAAM,KAAG,EAAEA,IAAE,EAAE,IAAE,UAAKA,KAAE,KAAGA;AAAA,EAAC;AAAC,SAAOA,KAAEE,MAAG,QAAME,KAAE,KAAGA;AAAE,GAAE,EAAE;AAAE,SAAS,EAAEJ,IAAE;AAAC,MAAI,IAAE,QAAM,CAAC,GAAEE,KAAEF,GAAE,OAAKA,GAAE,EAAE,CAAC,IAAEA;AAAE,SAAO,EAAEE,GAAE,UAAQA,GAAE,MAAI,EAAEA,IAAE,CAAC,EAAE,MAAM,KAAK,WAAU,CAAC,GAAE,EAAE,CAAC,IAAEA,GAAE,OAAO,CAACF,IAAEC,OAAI,OAAO,OAAOD,IAAEC,MAAGA,GAAE,OAAKA,GAAE,EAAE,CAAC,IAAEA,EAAC,GAAE,CAAC,CAAC,IAAEC,IAAE,EAAE,EAAE,MAAM,GAAE,EAAE,GAAE,EAAE,GAAE,EAAE,CAAC;AAAC;AAAC,IAAI;AAAJ,IAAM;AAAN,IAAQ;AAAR,IAAU,IAAE,EAAE,KAAK,EAAC,GAAE,EAAC,CAAC;AAAxB,IAA0B,IAAE,EAAE,KAAK,EAAC,GAAE,EAAC,CAAC;AAAE,SAAS,EAAEF,IAAEC,IAAE,GAAEC,IAAE;AAAC,IAAE,IAAED,IAAE,IAAED,IAAE,IAAE,GAAE,IAAEE;AAAC;AAAC,SAAS,EAAEF,IAAEC,IAAE;AAAC,MAAI,IAAE,QAAM,CAAC;AAAE,SAAO,WAAU;AAAC,QAAIC,KAAE;AAAU,aAASC,GAAEC,IAAEO,IAAE;AAAC,UAAIN,KAAE,OAAO,OAAO,CAAC,GAAED,EAAC,GAAES,KAAER,GAAE,aAAWF,GAAE;AAAU,QAAE,IAAE,OAAO,OAAO,EAAC,OAAM,KAAG,EAAE,EAAC,GAAEE,EAAC,GAAE,EAAE,IAAE,UAAU,KAAKQ,EAAC,GAAER,GAAE,YAAU,EAAE,MAAM,GAAEH,EAAC,KAAGW,KAAE,MAAIA,KAAE,KAAIZ,OAAII,GAAE,MAAIM;AAAG,UAAIL,KAAEN;AAAE,aAAOA,GAAE,CAAC,MAAIM,KAAED,GAAE,MAAIL,IAAE,OAAOK,GAAE,KAAI,KAAGC,GAAE,CAAC,KAAG,EAAED,EAAC,GAAE,EAAEC,IAAED,EAAC;AAAA,IAAC;AAAC,WAAOJ,KAAEA,GAAEE,EAAC,IAAEA;AAAA,EAAC;AAAC;;;ANAvqE,IAAAW,KAAuB;AICvB,QAAuB;AVsBvB,IAAMC,IACJC,CAAAA,OAEA,OAAOA,MAAkB;AAH3B,IAKaC,KAAe,CAC1BD,IACAE,OACYH,EAAWC,EAAa,IAAIA,GAAcE,EAAG,IAAIF;AC/BxD,IAAMG,IAAS,uBAAM;AAC1B,MAAIC,KAAQ;AACZ,SAAO,OACG,EAAEA,IAAO,SAAS;AAE9B,GAAG;AALI,IAOMC,IAAwB,uBAAM;AAEzC,MAAIC;AAEJ,SAAO,MAAM;AACX,QAAIA,OAAuB,UAAa,OAAO,SAAW,KAAa;AACrE,UAAMC,KAAa,WAAW,kCAAkC;AAChED,MAAAA,KAAqB,CAACC,MAAcA,GAAW;IAAA;AAEjD,WAAOD;EACT;AACF,GAAG;ACfH,IAAME,IAAc;AA+Cb,IAAMC,IAAU,CAACC,IAAcC,OAA0B;AAC9D,UAAQA,GAAO,MAAM;IACnB,KAAK;AACH,aAAO,EACL,GAAGD,IACH,QAAQ,CAACC,GAAO,OAAO,GAAGD,GAAM,MAAM,EAAE,MAAM,GAAGF,CAAW,EAC9D;IAEF,KAAK;AACH,aAAO,EACL,GAAGE,IACH,QAAQA,GAAM,OAAO,IAAKE,CAAAA,OACxBA,GAAE,OAAOD,GAAO,MAAM,KAAK,EAAE,GAAGC,IAAG,GAAGD,GAAO,MAAM,IAAIC,EACzD,EACF;IAEF,KAAK;AACH,UAAM,EAAE,OAAAC,EAAM,IAAIF;AAClB,aAAOF,EAAQC,IAAO,EACpB,MAAMA,GAAM,OAAO,KAAME,CAAAA,OAAMA,GAAE,OAAOC,EAAM,EAAE,IAC5C,IACA,GACJ,OAAAA,EACF,CAAC;IAEH,KAAK;AACH,UAAM,EAAE,SAAAC,GAAQ,IAAIH;AAEpB,aAAO,EACL,GAAGD,IACH,QAAQA,GAAM,OAAO,IAAKE,CAAAA,OACxBA,GAAE,OAAOE,MAAWA,OAAY,SAC5B,EACE,GAAGF,IACH,WAAW,MACX,SAAS,MACX,IACAA,EACN,EACF;IACF,KAAK;AACH,aAAID,GAAO,YAAY,SACd,EACL,GAAGD,IACH,QAAQ,CAAC,EACX,IAEK,EACL,GAAGA,IACH,QAAQA,GAAM,OAAO,OAAQE,CAAAA,OAAMA,GAAE,OAAOD,GAAO,OAAO,EAC5D;IAEF,KAAK;AACH,aAAO,EACL,GAAGD,IACH,UAAUC,GAAO,KACnB;IAEF,KAAK;AACH,UAAMI,KAAOJ,GAAO,QAAQD,GAAM,YAAY;AAE9C,aAAO,EACL,GAAGA,IACH,UAAU,QACV,QAAQA,GAAM,OAAO,IAAKE,CAAAA,QAAO,EAC/B,GAAGA,IACH,eAAeA,GAAE,gBAAgBG,GACnC,EAAE,EACJ;EACJ;AACF;AAtEO,IAwEDC,IAA2C,CAAC;AAxE3C,IA0EHC,IAAqB,EAAE,QAAQ,CAAC,GAAG,UAAU,OAAU;AA1EpD,IA4EMC,KAAYP,CAAAA,OAAmB;AAC1CM,MAAcR,EAAQQ,GAAaN,EAAM,GACzCK,EAAU,QAASG,CAAAA,OAAa;AAC9BA,IAAAA,GAASF,CAAW;EACtB,CAAC;AACH;AAjFO,IAmFMG,IAET,EACF,OAAO,KACP,OAAO,KACP,SAAS,KACT,SAAS,IAAA,GACT,QAAQ,IACV;AA3FO,IA6FMC,IAAW,CAACC,KAAoC,CAAC,MAAa;AACzE,MAAM,CAACZ,IAAOa,CAAQ,QAAIC,aAAAA,UAAgBP,CAAW,GAC/CQ,SAAUC,aAAAA,QAAOT,CAAW;AAGlCU,mBAAAA,WAAU,OACJF,GAAQ,YAAYR,KACtBM,EAASN,CAAW,GAEtBD,EAAU,KAAKO,CAAQ,GAChB,MAAM;AACX,QAAMK,KAAQZ,EAAU,QAAQO,CAAQ;AACpCK,IAAAA,KAAQ,MACVZ,EAAU,OAAOY,IAAO,CAAC;EAE7B,IACC,CAAC,CAAC;AAEL,MAAMC,KAAenB,GAAM,OAAO,IAAKE,CAAAA,OAAG;AAjK5C,QAAAkB,IAAAC,IAAAC;AAiKgD,WAAA,EAC5C,GAAGV,IACH,GAAGA,GAAaV,GAAE,IAAI,GACtB,GAAGA,IACH,aACEA,GAAE,iBACFkB,KAAAR,GAAaV,GAAE,IAAI,MAAnB,OAAA,SAAAkB,GAAsB,iBACtBR,MAAA,OAAA,SAAAA,GAAc,cAChB,UACEV,GAAE,cACFmB,KAAAT,GAAaV,GAAE,IAAI,MAAnB,OAAA,SAAAmB,GAAsB,cACtBT,MAAA,OAAA,SAAAA,GAAc,aACdF,EAAgBR,GAAE,IAAI,GACxB,OAAO,EACL,GAAGU,GAAa,OAChB,IAAGU,KAAAV,GAAaV,GAAE,IAAI,MAAnB,OAAA,SAAAoB,GAAsB,OACzB,GAAGpB,GAAE,MACP,EACF;EAAA,CAAE;AAEF,SAAO,EACL,GAAGF,IACH,QAAQmB,GACV;AACF;ACzKA,IAAMI,IAAc,CAClBC,IACAC,KAAkB,SAClBC,OACW,EACX,WAAW,KAAK,IAAI,GACpB,SAAS,MACT,WAAW,OACX,MAAAD,IACA,WAAW,EACT,MAAM,UACN,aAAa,SACf,GACA,SAAAD,IACA,eAAe,GACf,GAAGE,GACH,KAAIA,KAAA,OAAA,SAAAA,EAAM,OAAMjC,EAAM,EACxB;AAjBA,IAmBMkC,IACHF,CAAAA,OACD,CAACD,IAASI,MAAY;AACpB,MAAMzB,KAAQoB,EAAYC,IAASC,IAAMG,CAAO;AAChD,SAAApB,GAAS,EAAE,MAAA,GAA+B,OAAAL,GAAM,CAAC,GAC1CA,GAAM;AACf;AAzBF,IA2BMA,KAAQ,CAACqB,IAAkBE,OAC/BC,EAAc,OAAO,EAAEH,IAASE,EAAI;AAEtCvB,GAAM,QAAQwB,EAAc,OAAO;AACnCxB,GAAM,UAAUwB,EAAc,SAAS;AACvCxB,GAAM,UAAUwB,EAAc,SAAS;AACvCxB,GAAM,SAASwB,EAAc,QAAQ;AAErCxB,GAAM,UAAWC,CAAAA,OAAqB;AACpCI,EAAAA,GAAS,EACP,MAAA,GACA,SAAAJ,GACF,CAAC;AACH;AAEAD,GAAM,SAAUC,CAAAA,OACdI,GAAS,EAAE,MAAA,GAA+B,SAAAJ,GAAQ,CAAC;AAErDD,GAAM,UAAU,CACd0B,IACAC,IAKAJ,MACG;AACH,MAAMK,KAAK5B,GAAM,QAAQ2B,GAAK,SAAS,EAAE,GAAGJ,GAAM,GAAGA,KAAA,OAAA,SAAAA,EAAM,QAAQ,CAAC;AAEpE,SAAI,OAAOG,MAAY,eACrBA,KAAUA,GAAQ,IAGpBA,GACG,KAAMG,CAAAA,OAAM;AACX,QAAMC,KAAiBH,GAAK,UACxBvC,GAAauC,GAAK,SAASE,EAAC,IAC5B;AAEJ,WAAIC,KACF9B,GAAM,QAAQ8B,IAAgB,EAC5B,IAAAF,IACA,GAAGL,GACH,GAAGA,KAAA,OAAA,SAAAA,EAAM,QACX,CAAC,IAEDvB,GAAM,QAAQ4B,EAAE,GAEXC;EACT,CAAC,EACA,MAAOE,CAAAA,OAAM;AACZ,QAAMC,KAAeL,GAAK,QAAQvC,GAAauC,GAAK,OAAOI,EAAC,IAAI;AAE5DC,IAAAA,KACFhC,GAAM,MAAMgC,IAAc,EACxB,IAAAJ,IACA,GAAGL,GACH,GAAGA,KAAA,OAAA,SAAAA,EAAM,MACX,CAAC,IAEDvB,GAAM,QAAQ4B,EAAE;EAEpB,CAAC,GAEIF;AACT;ACvGA,IAAMO,IAAe,CAAChC,IAAiBiC,OAAmB;AACxD7B,EAAAA,GAAS,EACP,MAAA,GACA,OAAO,EAAE,IAAIJ,IAAS,QAAAiC,GAAO,EAC/B,CAAC;AACH;AALA,IAMMC,IAAa,MAAM;AACvB9B,EAAAA,GAAS,EACP,MAAA,GACA,MAAM,KAAK,IAAI,EACjB,CAAC;AACH;AAXA,IAaM+B,KAAgB,oBAAI;AAb1B,IAeaC,IAAe;AAf5B,IAiBMC,KAAmB,CAACrC,IAAiBsC,KAAcF,MAAiB;AACxE,MAAID,GAAc,IAAInC,EAAO,EAC3B;AAGF,MAAMuC,IAAU,WAAW,MAAM;AAC/BJ,IAAAA,GAAc,OAAOnC,EAAO,GAC5BI,GAAS,EACP,MAAA,GACA,SAASJ,GACX,CAAC;EACH,GAAGsC,EAAW;AAEdH,EAAAA,GAAc,IAAInC,IAASuC,CAAO;AACpC;AA/BA,IAiCaC,IAAchC,CAAAA,OAAuC;AAChE,MAAM,EAAE,QAAAiC,IAAQ,UAAAC,EAAS,IAAInC,EAASC,EAAY;AAElDK,oBAAAA,WAAU,MAAM;AACd,QAAI6B,EACF;AAGF,QAAMC,KAAM,KAAK,IAAI,GACfC,KAAWH,GAAO,IAAK3C,CAAAA,OAAM;AACjC,UAAIA,GAAE,aAAa,IAAA,EACjB;AAGF,UAAM+C,MACH/C,GAAE,YAAY,KAAKA,GAAE,iBAAiB6C,KAAM7C,GAAE;AAEjD,UAAI+C,KAAe,GAAG;AAChB/C,QAAAA,GAAE,WACJC,GAAM,QAAQD,GAAE,EAAE;AAEpB;MAAA;AAEF,aAAO,WAAW,MAAMC,GAAM,QAAQD,GAAE,EAAE,GAAG+C,EAAY;IAC3D,CAAC;AAED,WAAO,MAAM;AACXD,MAAAA,GAAS,QAASL,CAAAA,OAAYA,MAAW,aAAaA,EAAO,CAAC;IAChE;EACF,GAAG,CAACE,IAAQC,CAAQ,CAAC;AAErB,MAAMI,SAAWC,cAAAA,aAAY,MAAM;AAC7BL,SACFtC,GAAS,EAAE,MAAA,GAA4B,MAAM,KAAK,IAAI,EAAE,CAAC;EAE7D,GAAG,CAACsC,CAAQ,CAAC,GAEPM,SAAkBD,cAAAA,aACtB,CACEhD,IACAuB,OAKG;AACH,QAAM,EAAE,cAAA2B,KAAe,OAAO,QAAAC,KAAS,GAAG,iBAAAC,GAAgB,IAAI7B,MAAQ,CAAC,GAEjE8B,KAAiBX,GAAO,OAC3B3C,CAAAA,QACEA,GAAE,YAAYqD,SACZpD,GAAM,YAAYoD,OAAoBrD,GAAE,MAC/C,GACMuD,IAAaD,GAAe,UAAWtD,CAAAA,OAAMA,GAAE,OAAOC,GAAM,EAAE,GAC9DuD,IAAeF,GAAe,OAClC,CAACrD,IAAOwD,MAAMA,IAAIF,KAActD,GAAM,OACxC,EAAE;AAOF,WALeqD,GACZ,OAAQtD,CAAAA,OAAMA,GAAE,OAAO,EACvB,MAAM,GAAImD,KAAe,CAACK,IAAe,CAAC,IAAI,CAAC,GAAGA,CAAY,CAAE,EAChE,OAAO,CAACE,IAAK1D,MAAM0D,MAAO1D,EAAE,UAAU,KAAKoD,IAAQ,CAAC;EAGzD,GACA,CAACT,EAAM,CACT;AAEA,aAAA5B,cAAAA,WAAU,MAAM;AAEd4B,IAAAA,GAAO,QAAS1C,CAAAA,OAAU;AACxB,UAAIA,GAAM,UACRsC,IAAiBtC,GAAM,IAAIA,GAAM,WAAW;WACvC;AAEL,YAAMwC,KAAUJ,GAAc,IAAIpC,GAAM,EAAE;AACtCwC,QAAAA,OACF,aAAaA,EAAO,GACpBJ,GAAc,OAAOpC,GAAM,EAAE;MAAA;IAGnC,CAAC;EACH,GAAG,CAAC0C,EAAM,CAAC,GAEJ,EACL,QAAAA,IACA,UAAU,EACR,cAAAT,GACA,YAAAE,GACA,UAAAY,IACA,iBAAAE,GACF,EACF;AACF;AGjIA,IAAMS,KAAkBC;;;;;;;;;AAAxB,IAUMC,KAAqBD;;;;;;;;;AAV3B,IAoBME,KAAsBF;;;;;;;;;AApB5B,IAmCaG,IAAYC,EAAO,KAAK;;;;;gBAKpBlC,CAAAA,OAAMA,GAAE,WAAW,SAAA;;;;eAIrB6B,EAAAA;;;;;;;iBAOEE,EAAAA;;;;;kBAKE/B,CAAAA,OAAMA,GAAE,aAAa,MAAA;;;;;;;;iBAQvBgC,EAAAA;;;;;AChEjB,IAAMG,KAASL;;;;;;;;AAAf,IAcaM,IAAaF,EAAO,KAAK;;;;;;kBAMnBlC,CAAAA,OAAMA,GAAE,aAAa,SAAA;wBACfA,CAAAA,OAAMA,GAAE,WAAW,SAAA;eAC7BmC,EAAAA;;ACtBf,IAAMN,KAAkBC;;;;;;;;;AAAxB,IAUMO,KAAqBP;;;;;;;;;;;;;;;AAV3B,IA+BaQ,IAAgBJ,EAAO,KAAK;;;;;gBAKxBlC,CAAAA,OAAMA,GAAE,WAAW,SAAA;;;;eAIrB6B,EAAAA;;;;;;iBAMEQ,EAAAA;;;;;;oBAMIrC,CAAAA,OAAMA,GAAE,aAAa,MAAA;;;;;;;AH9C1C,IAAMuC,KAAgBL,EAAO,KAAK;;;AAAlC,IAIMM,KAAmBN,EAAO,KAAK;;;;;;;;AAJrC,IAaMO,KAAQX;;;;;;;;;AAbd,IAuBaY,KAAsBR,EAAO,KAAK;;;;;eAKhCO,EAAAA;;;AA5Bf,IAsCaE,IAER,CAAC,EAAE,OAAAxE,GAAM,MAAM;AAClB,MAAM,EAAE,MAAAyE,IAAM,MAAAnD,GAAM,WAAAoD,GAAU,IAAI1E;AAClC,SAAIyE,OAAS,SACP,OAAOA,MAAS,WACX,iBAACF,IAAA,MAAqBE,EAAK,IAE3BA,KAIPnD,MAAS,UACJ,OAIP,iBAAC+C,IAAA,MACC,iBAACJ,GAAA,EAAY,GAAGS,GAAAA,CAAW,GAC1BpD,MAAS,aACR,iBAAC8C,IAAA,MACE9C,MAAS,UACR,iBAACwC,GAAA,EAAW,GAAGY,GAAAA,CAAW,IAE1B,iBAACP,GAAA,EAAe,GAAGO,GAAAA,CAAW,CAElC,CAEJ;AAEJ;ADrEA,IAAMC,KAAkBC,CAAAA,OAAmB;+BACZA,KAAS,IAAA;;;AADxC,IAKMC,KAAiBD,CAAAA,OAAmB;;iCAETA,KAAS,IAAA;;AAP1C,IAUME,KAAkB;AAVxB,IAWMC,KAAmB;AAXzB,IAaMC,KAAejB,EAAO,KAAK;;;;;;;;;;;;;AAbjC,IA2BMkB,KAAUlB,EAAO,KAAK;;;;;;;;AA3B5B,IA8CMmB,KAAoB,CACxBC,IACAC,OACwB;AAExB,MAAMR,KADMO,GAAS,SAAS,KAAK,IACd,IAAI,IAEnB,CAACb,IAAOe,EAAI,IAAI7F,EAAqB,IACvC,CAACsF,IAAiBC,EAAgB,IAClC,CAACJ,GAAeC,EAAM,GAAGC,GAAcD,EAAM,CAAC;AAElD,SAAO,EACL,WAAWQ,KACP,GAAGzB,EAAUW,EAAK,CAAA,iDAClB,GAAGX,EAAU0B,EAAI,CAAA,6CACvB;AACF;AA9DA,IAgEaC,IAA0C,QACrD,CAAC,EAAE,OAAAtF,IAAO,UAAAmF,IAAU,OAAAI,GAAO,UAAAC,GAAS,MAAM;AACxC,MAAMC,KAAsCzF,GAAM,SAC9CkF,GACElF,GAAM,YAAYmF,MAAY,cAC9BnF,GAAM,OACR,IACA,EAAE,SAAS,EAAE,GAEXyE,KAAO,iBAACD,GAAA,EAAU,OAAOxE,GAAAA,CAAO,GAChCqB,KACJ,iBAAC4D,IAAA,EAAS,GAAGjF,GAAM,UAAA,GAChBZ,GAAaY,GAAM,SAASA,EAAK,CACpC;AAGF,SACE,iBAACgF,IAAA,EACC,WAAWhF,GAAM,WACjB,OAAO,EACL,GAAGyF,IACH,GAAGF,GACH,GAAGvF,GAAM,MACX,EAAA,GAEC,OAAOwF,MAAa,aACnBA,GAAS,EACP,MAAAf,IACA,SAAApD,GACF,CAAC,IAED,iBAAA,aAAA,MACGoD,IACApD,EACH,CAEJ;AAEJ,CACF;AKlGAqE,EAAY,eAAa;AAEzB,IAAMC,KAAe,CAAC,EACpB,IAAA/D,IACA,WAAAgE,IACA,OAAAL,GACA,gBAAAM,IACA,UAAAL,GACF,MAAyB;AACvB,MAAMM,KAAY,cACfC,CAAAA,OAA2B;AAC1B,QAAIA,IAAI;AACN,UAAM9D,KAAe,MAAM;AACzB,YAAMC,KAAS6D,GAAG,sBAAsB,EAAE;AAC1CF,QAAAA,GAAejE,IAAIM,EAAM;MAC3B;AACAD,MAAAA,GAAa,GACb,IAAI,iBAAiBA,EAAY,EAAE,QAAQ8D,IAAI,EAC7C,SAAS,MACT,WAAW,MACX,eAAe,KACjB,CAAC;IAAA;EAEL,GACA,CAACnE,IAAIiE,EAAc,CACrB;AAEA,SACE,gBAAC,OAAA,EAAI,KAAKC,IAAK,WAAWF,IAAW,OAAOL,EAAAA,GACzCC,EACH;AAEJ;AA9BA,IAgCMQ,KAAmB,CACvBb,IACAc,OACwB;AACxB,MAAMC,IAAMf,GAAS,SAAS,KAAK,GAC7BgB,KAAqCD,IAAM,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,GACpEE,KAAuCjB,GAAS,SAAS,QAAQ,IACnE,EACE,gBAAgB,SAClB,IACAA,GAAS,SAAS,OAAO,IACzB,EACE,gBAAgB,WAClB,IACA,CAAC;AACL,SAAO,EACL,MAAM,GACN,OAAO,GACP,SAAS,QACT,UAAU,YACV,YAAY3F,EAAqB,IAC7B,SACA,0CACJ,WAAW,cAAcyG,MAAUC,IAAM,IAAI,GAAA,OAC7C,GAAGC,IACH,GAAGC,GACL;AACF;AA3DA,IA6DMC,KAAcC;;;;;;AA7DpB,IAoEMC,IAAiB;AApEvB,IAsEaC,KAAkC,CAAC,EAC9C,cAAAtD,IACA,UAAAiC,KAAW,cACX,cAAA1E,GACA,QAAA0C,IACA,UAAAqC,IACA,gBAAAiB,IACA,oBAAAC,GACF,MAAM;AACJ,MAAM,EAAE,QAAAhE,IAAQ,UAAAiE,GAAS,IAAIlE,EAAWhC,CAAY;AAEpD,SACE,gBAAC,OAAA,EACC,IAAG,gBACH,OAAO,EACL,UAAU,SACV,QAAQ,MACR,KAAK8F,GACL,MAAMA,GACN,OAAOA,GACP,QAAQA,GACR,eAAe,QACf,GAAGE,GACL,GACA,WAAWC,IACX,cAAcC,GAAS,YACvB,cAAcA,GAAS,SAAA,GAEtBjE,GAAO,IAAK3C,CAAAA,OAAM;AACjB,QAAM6G,KAAgB7G,GAAE,YAAYoF,IAC9Bc,IAASU,GAAS,gBAAgB5G,IAAG,EACzC,cAAAmD,IACA,QAAAC,IACA,iBAAiBgC,GACnB,CAAC,GACK0B,IAAgBb,GAAiBY,IAAeX,CAAM;AAE5D,WACE,gBAACN,IAAA,EACC,IAAI5F,GAAE,IACN,KAAKA,GAAE,IACP,gBAAgB4G,GAAS,cACzB,WAAW5G,GAAE,UAAUsG,KAAc,IACrC,OAAOQ,EAAAA,GAEN9G,GAAE,SAAS,WACVX,GAAaW,GAAE,SAASA,EAAC,IACvByF,KACFA,GAASzF,EAAC,IAEV,gBAACuF,GAAA,EAAS,OAAOvF,IAAG,UAAU6G,GAAAA,CAAe,CAEjD;EAEJ,CAAC,CACH;AAEJ;ACjIA,IAAOE,KAAQ9G;", "names": ["import_react", "l", "t", "e", "t", "l", "a", "n", "c", "i", "p", "u", "d", "r", "o", "f", "s", "g", "isFunction", "valOrFunction", "resolveValue", "arg", "genId", "count", "prefersReducedMotion", "shouldReduceMotion", "mediaQuery", "TOAST_LIMIT", "reducer", "state", "action", "t", "toast", "toastId", "diff", "listeners", "memoryState", "dispatch", "listener", "defaultTimeouts", "useStore", "toastOptions", "setState", "useState", "initial", "useRef", "useEffect", "index", "mergedToasts", "_a", "_b", "_c", "createToast", "message", "type", "opts", "createHandler", "options", "promise", "msgs", "id", "p", "successMessage", "e", "errorMessage", "updateHeight", "height", "startPause", "toastTimeouts", "REMOVE_DELAY", "addToRemoveQueue", "<PERSON><PERSON><PERSON><PERSON>", "timeout", "useToaster", "toasts", "pausedAt", "now", "timeouts", "durationLeft", "endPause", "useCallback", "calculateOffset", "reverseOrder", "gutter", "defaultPosition", "relevantToasts", "toastIndex", "toastsBefore", "i", "acc", "circleAnimation", "keyframes", "firstLineAnimation", "secondLineAnimation", "ErrorIcon", "styled", "rotate", "LoaderIcon", "checkmarkAnimation", "CheckmarkIcon", "StatusWrapper", "IndicatorWrapper", "enter", "AnimatedIconWrapper", "ToastIcon", "icon", "iconTheme", "enterAnimation", "factor", "exitAnimation", "fadeInAnimation", "fadeOutAnimation", "ToastBarBase", "Message", "getAnimationStyle", "position", "visible", "exit", "ToastBar", "style", "children", "animationStyle", "setup", "ToastWrapper", "className", "onHeightUpdate", "ref", "el", "getPositionStyle", "offset", "top", "verticalStyle", "horizontalStyle", "activeClass", "css", "DEFAULT_OFFSET", "Toaster", "containerStyle", "containerClassName", "handlers", "toastPosition", "positionStyle", "src_default"]}