import express from 'express';
import cors from 'cors';
import cookieParser from 'cookie-parser';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';
import { spawn } from 'child_process';
import { createProxyMiddleware } from 'http-proxy-middleware';
import pkg from 'pg';
import XLSX from 'xlsx';
import multer from 'multer';
import { v4 as uuidv4 } from 'uuid';
const { Pool } = pkg;

// Load environment variables
dotenv.config();

const app = express();
const PORT = 8080; // Fixed port for user access
const VITE_PORT = 3001; // Fixed port for Vite dev server (changed from 3000 to avoid conflict)

// Get current directory for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🚀 Starting TrackNew Development Server with Hot Reload...');
console.log(`📍 User Access: http://localhost:${PORT}`);
console.log(`🔧 Vite Dev Server: http://localhost:${VITE_PORT} (internal)`);

// Database connection - Support both PostgreSQL and SQLite
let pool = null;

async function initializeDatabase() {
  const databaseUrl = process.env.DATABASE_URL || 'sqlite:./database/tracknew.db';

  if (databaseUrl.startsWith('sqlite:')) {
    console.log('🔧 Using SQLite database');
    // For SQLite, we'll use a different approach in the API routes
    console.log('✅ SQLite database configured');
  } else {
    console.log('🔧 Using PostgreSQL database');
    try {
      pool = new Pool({
        connectionString: databaseUrl,
        ssl: false
      });

      // Test database connection
      await pool.connect();
      console.log('✅ PostgreSQL database connected successfully');
    } catch (err) {
      console.error('❌ Database connection failed:', err.message);
      pool = null;
    }
  }
}

// Initialize database
initializeDatabase();

// CORS configuration - Allow both ports
app.use(cors({
  origin: ['http://localhost:8080', 'http://localhost:8081', 'http://localhost:3000'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use(cookieParser());

// Multer configuration for file uploads
const storage = multer.memoryStorage();
const upload = multer({
  storage: storage,
  limits: { fileSize: 10 * 1024 * 1024 } // 10MB limit
});

// Cache-busting headers for development
app.use((req, res, next) => {
  // Only apply cache-busting to non-API routes
  if (!req.path.startsWith('/api')) {
    res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');
    res.setHeader('ETag', Date.now().toString()); // Force unique ETag
  }
  next();
});

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    message: 'TrackNew Development Server with Hot Reload',
    ports: {
      user: PORT,
      vite: VITE_PORT
    }
  });
});

// Import and register routes
import authRoutes from './backend/src/routes/auth.js';
import customerCategoriesRoutes from './backend/src/routes/customerCategories.js';

app.use('/api/auth', authRoutes);
app.use('/api/customer-categories', customerCategoriesRoutes);

// Mock users for authentication - using proper UUIDs to match database schema
const mockUsers = [
  {
    id: 'fce8b418-d90b-4f18-a0e0-40347e7c62d6',
    name: 'Admin User',
    email: '<EMAIL>',
    phone: '******-0101',
    userType: 'admin',
    status: 'active'
  },
  {
    id: '69eb4c0c-df84-4206-ad05-f48c798a9b31',
    name: 'Service Manager',
    email: '<EMAIL>',
    phone: '******-0102',
    userType: 'service_manager',
    status: 'active'
  },
  {
    id: '418eee88-dc2a-4c53-91db-1204e2ae5dc9',
    name: 'Sales Manager',
    email: '<EMAIL>',
    phone: '******-0103',
    userType: 'sales_manager',
    status: 'active'
  },
  {
    id: '045ab8eb-ca6e-4a70-9667-3d8478c78a26',
    name: 'Service Engineer',
    email: '<EMAIL>',
    phone: '******-0104',
    userType: 'service_engineer',
    status: 'active'
  }
];

// Authentication endpoints
app.post('/api/auth/login', (req, res) => {
  console.log('🔐 Login request received:', req.body);

  const { email, password } = req.body;

  // Find user by email
  const user = mockUsers.find(u => u.email === email);

  if (!user) {
    return res.status(401).json({
      status: 'error',
      message: 'Invalid email or password'
    });
  }

  // Simple password check
  const validPasswords = {
    '<EMAIL>': 'admin123',
    '<EMAIL>': 'service123',
    '<EMAIL>': 'sales123',
    '<EMAIL>': 'engineer123'
  };

  if (validPasswords[email] !== password) {
    return res.status(401).json({
      status: 'error',
      message: 'Invalid email or password'
    });
  }

  // Generate mock JWT token
  const header = btoa(JSON.stringify({ alg: 'HS256', typ: 'JWT' }));
  const payload = btoa(JSON.stringify({
    id: user.id,
    email: user.email,
    userType: user.userType,
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + (7 * 24 * 60 * 60) // 7 days
  }));
  const signature = btoa(`mock-signature-${user.id}-${Date.now()}`);
  const token = `${header}.${payload}.${signature}`;

  res.json({
    status: 'success',
    message: 'Login successful',
    data: {
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        userType: user.userType,
        status: user.status
      },
      company: {
        id: '1',
        name: 'TrackNew Demo Company',
        status: 'active'
      },
      token: token,
      refreshToken: `refresh-${token}`
    }
  });
});

// Profile endpoint
app.get('/api/auth/profile', (req, res) => {
  const authHeader = req.headers.authorization;

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({
      status: 'error',
      message: 'No token provided'
    });
  }

  const token = authHeader.substring(7);

  try {
    // Decode the JWT token to get user info
    const payload = JSON.parse(atob(token.split('.')[1]));

    // Find user by ID from token
    const user = mockUsers.find(u => u.id === payload.id);

    if (!user) {
      return res.status(401).json({
        status: 'error',
        message: 'User not found'
      });
    }

    res.json({
      status: 'success',
      data: {
        user: {
          id: user.id,
          name: user.name,
          email: user.email,
          userType: user.userType,
          status: user.status
        },
        company: {
          id: '1',
          name: 'TrackNew Demo Company',
          status: 'active'
        }
      }
    });
  } catch (error) {
    return res.status(401).json({
      status: 'error',
      message: 'Invalid token'
    });
  }
});

// Start Vite dev server
let viteProcess;
function startViteServer() {
  console.log('🔧 Starting Vite dev server...');
  
  viteProcess = spawn('npx', ['vite', '--port', VITE_PORT.toString(), '--host', '--strictPort'], {
    cwd: path.join(__dirname, 'frontend'),
    stdio: 'pipe',
    shell: true
  });

  viteProcess.stdout.on('data', (data) => {
    const output = data.toString();
    if (output.includes('ready in') || output.includes('Local:')) {
      console.log('✅ Vite dev server ready');
    }
    // Only log important Vite messages, not all output
    if (output.includes('ready in') || output.includes('error') || output.includes('warning')) {
      console.log('📦 Vite:', output.trim());
    }
  });

  viteProcess.stderr.on('data', (data) => {
    console.error('❌ Vite error:', data.toString());
  });

  viteProcess.on('close', (code) => {
    console.log(`🔧 Vite process exited with code ${code}`);
  });
}

// Serve static files from frontend/public (including PWA icons)
app.use(express.static(path.join(__dirname, 'frontend/public')));

// Serve uploaded files
app.use('/uploads', express.static(path.join(__dirname, 'backend/uploads')));

// Proxy to Vite dev server for frontend assets with enhanced MIME type handling
const viteProxy = createProxyMiddleware({
  target: `http://localhost:${VITE_PORT}`,
  changeOrigin: true,
  ws: true, // Enable WebSocket proxying for HMR
  logLevel: 'silent', // Reduce proxy logging
  onProxyReq: (proxyReq, req, res) => {
    // Ensure proper headers for module scripts
    if (req.url.includes('.js') || req.url.includes('/@vite/') || req.url.includes('/@fs/') || req.url.includes('/src/')) {
      proxyReq.setHeader('Accept', 'application/javascript, text/javascript, */*');
    }
  },
  onProxyRes: (proxyRes, req, res) => {
    // Fix MIME types for JavaScript modules
    if (req.url.includes('.js') || req.url.includes('/@vite/') || req.url.includes('/@fs/') || req.url.includes('/src/')) {
      proxyRes.headers['content-type'] = 'application/javascript; charset=utf-8';
    }
    // Enable CORS for all responses
    proxyRes.headers['Access-Control-Allow-Origin'] = '*';
    proxyRes.headers['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS';
    proxyRes.headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization';
  },
  onError: (err, req, res) => {
    console.error('❌ Vite proxy error:', err.message);
    if (!res.headersSent) {
      res.status(503).json({
        error: 'Vite dev server unavailable',
        message: 'Please wait for Vite to start...'
      });
    }
  }
});

// Dashboard API - Real Database Data
app.get('/api/dashboard', async (req, res) => {
  try {
    console.log('📊 Dashboard API called - fetching real data');

    const companyId = '8722167d-d2ae-498f-95a3-c18afcf427ae'; // Real company ID

    // Get services overview
    const servicesResult = await pool.query(`
      SELECT
        COUNT(*) as total,
        COUNT(CASE WHEN status IN ('completed', 'delivered') THEN 1 END) as complete,
        COUNT(CASE WHEN status NOT IN ('completed', 'delivered') THEN 1 END) as progress
      FROM services WHERE company_id = $1
    `, [companyId]);

    // Get customers count
    const customersResult = await pool.query('SELECT COUNT(*) as total FROM customers WHERE company_id = $1', [companyId]);

    // Get leads count
    const leadsResult = await pool.query('SELECT COUNT(*) as total FROM leads WHERE company_id = $1', [companyId]);

    // Get AMC count and status
    let amcResult, amcStatusResult;
    try {
      amcResult = await pool.query('SELECT COUNT(*) as total FROM amcs WHERE company_id = $1', [companyId]);
      amcStatusResult = await pool.query(`
        SELECT
          COUNT(CASE WHEN status IN ('active') THEN 1 END) as active,
          COUNT(CASE WHEN status IN ('expired', 'cancelled') THEN 1 END) as expired
        FROM amcs WHERE company_id = $1
      `, [companyId]);
    } catch (error) {
      console.log('⚠️  AMC table not found, using default count');
      amcResult = { rows: [{ total: '0' }] };
      amcStatusResult = { rows: [{ active: '0', expired: '0' }] };
    }

    // Get RMA count and status
    let rmaResult, rmaStatusResult;
    try {
      rmaResult = await pool.query('SELECT COUNT(*) as total FROM rmas WHERE company_id = $1', [companyId]);
      rmaStatusResult = await pool.query(`
        SELECT
          COUNT(CASE WHEN rma_status IN ('completed', 'shipped', 'closed') THEN 1 END) as completed,
          COUNT(CASE WHEN rma_status NOT IN ('completed', 'shipped', 'closed') THEN 1 END) as pending
        FROM rmas WHERE company_id = $1
      `, [companyId]);
    } catch (error) {
      console.log('⚠️  RMA table not found, using default count');
      rmaResult = { rows: [{ total: '0' }] };
      rmaStatusResult = { rows: [{ completed: '0', pending: '0' }] };
    }

    // Get Sales count and amounts
    let salesResult, salesAmountResult;
    try {
      salesResult = await pool.query('SELECT COUNT(*) as total FROM sales WHERE company_id = $1', [companyId]);
      salesAmountResult = await pool.query(`
        SELECT
          COALESCE(SUM(CASE WHEN payment_status = 'paid' THEN grand_total ELSE 0 END), 0) as paid,
          COALESCE(SUM(CASE WHEN payment_status != 'paid' THEN grand_total ELSE 0 END), 0) as unpaid
        FROM sales WHERE company_id = $1
      `, [companyId]);
    } catch (error) {
      console.log('⚠️  Sales table not found, using default count');
      salesResult = { rows: [{ total: '0' }] };
      salesAmountResult = { rows: [{ paid: '0', unpaid: '0' }] };
    }

    // Get Proformas count and status
    let proformaResult, proformaStatusResult;
    try {
      proformaResult = await pool.query('SELECT COUNT(*) as total FROM proformas WHERE company_id = $1', [companyId]);
      proformaStatusResult = await pool.query(`
        SELECT
          COUNT(CASE WHEN status IN ('approved', 'converted') THEN 1 END) as completed,
          COUNT(CASE WHEN status NOT IN ('approved', 'converted') THEN 1 END) as pending
        FROM proformas WHERE company_id = $1
      `, [companyId]);
    } catch (error) {
      console.log('⚠️  Proforma table not found, using default count');
      proformaResult = { rows: [{ total: '0' }] };
      proformaStatusResult = { rows: [{ completed: '0', pending: '0' }] };
    }

    // Get Estimations count and status
    let estimationResult, estimationStatusResult;
    try {
      estimationResult = await pool.query('SELECT COUNT(*) as total FROM estimations WHERE company_id = $1', [companyId]);
      estimationStatusResult = await pool.query(`
        SELECT
          COUNT(CASE WHEN status IN ('approved', 'converted') THEN 1 END) as completed,
          COUNT(CASE WHEN status NOT IN ('approved', 'converted') THEN 1 END) as pending
        FROM estimations WHERE company_id = $1
      `, [companyId]);
    } catch (error) {
      console.log('⚠️  Estimation table not found, using default count');
      estimationResult = { rows: [{ total: '0' }] };
      estimationStatusResult = { rows: [{ completed: '0', pending: '0' }] };
    }

    // Get Expenses count and total amount
    let expenseResult, expenseAmountResult;
    try {
      expenseResult = await pool.query('SELECT COUNT(*) as total FROM expenses WHERE company_id = $1', [companyId]);
      expenseAmountResult = await pool.query(`
        SELECT COALESCE(SUM(amount), 0) as total_amount
        FROM expenses WHERE company_id = $1
      `, [companyId]);
    } catch (error) {
      console.log('⚠️  Expense table not found, using default count');
      expenseResult = { rows: [{ total: '0' }] };
      expenseAmountResult = { rows: [{ total_amount: '0' }] };
    }

    // Get recent services
    const recentServicesResult = await pool.query(`
      SELECT s.*, c.first_name, c.last_name
      FROM services s
      LEFT JOIN customers c ON s.customer_id = c.id
      WHERE s.company_id = $1
      ORDER BY s.created_at DESC
      LIMIT 5
    `, [companyId]);

    // Structure data to match frontend expectations
    const overview = [
      {
        id: 'Services',
        total: parseInt(servicesResult.rows[0].total),
        complete: parseInt(servicesResult.rows[0].complete),
        progress: parseInt(servicesResult.rows[0].progress)
      },
      {
        id: 'Customers',
        total: parseInt(customersResult.rows[0].total),
        complete: 0,
        progress: 0
      },
      {
        id: 'Leads',
        total: parseInt(leadsResult.rows[0].total),
        complete: 0,
        progress: parseInt(leadsResult.rows[0].total)
      },
      {
        id: 'Amc',
        total: parseInt(amcResult.rows[0].total),
        complete: parseInt(amcStatusResult.rows[0].active),
        progress: parseInt(amcStatusResult.rows[0].expired)
      },
      {
        id: 'Rma\'s',
        title: 'RMA',
        total: parseInt(rmaResult.rows[0].total),
        complete: parseInt(rmaStatusResult.rows[0].completed),
        progress: parseInt(rmaStatusResult.rows[0].pending)
      },
      {
        id: 'Sales',
        total: parseInt(salesResult.rows[0].total),
        complete: 0, // We'll use paid/unpaid amounts instead
        progress: 0,
        paid: parseFloat(salesAmountResult.rows[0].paid),
        unpaid: parseFloat(salesAmountResult.rows[0].unpaid)
      },
      {
        id: 'Proforma\'s',
        title: 'Proformas',
        total: parseInt(proformaResult.rows[0].total),
        complete: parseInt(proformaStatusResult.rows[0].completed),
        progress: parseInt(proformaStatusResult.rows[0].pending)
      },
      {
        id: 'Estimations',
        total: parseInt(estimationResult.rows[0].total),
        complete: parseInt(estimationStatusResult.rows[0].completed),
        progress: parseInt(estimationStatusResult.rows[0].pending)
      },
      {
        id: 'Expenses',
        total: parseInt(expenseResult.rows[0].total),
        complete: 0,
        progress: 0,
        totalAmount: parseFloat(expenseAmountResult.rows[0].total_amount)
      }
    ];

    const dashboardData = {
      overview: overview,
      assign_service_list: [
        { name: 'John Smith', role: 'Senior Technician', assigned_services: 12, completed_services: 8 },
        { name: 'Sarah Johnson', role: 'Service Engineer', assigned_services: 8, completed_services: 6 },
        { name: 'Mike Wilson', role: 'Technical Lead', assigned_services: 15, completed_services: 12 }
      ],
      service_statics: [
        { name: 'in-progress', count: parseInt(servicesResult.rows[0].progress) },
        { name: 'completed', count: parseInt(servicesResult.rows[0].complete) },
        { name: 'pending', count: Math.max(0, parseInt(servicesResult.rows[0].total) - parseInt(servicesResult.rows[0].complete)) },
        { name: 'cancelled', count: 0 }
      ],
      recent_services: recentServicesResult.rows.map(row => ({
        id: row.id,
        serviceCode: row.service_code,
        title: row.title,
        status: row.status,
        customerName: `${row.first_name || ''} ${row.last_name || ''}`.trim(),
        createdAt: row.created_at
      })),
      recent_leads: [],
      recent_amcs: [],
      recent_rmas: [],
      recent_sales: [],
      recent_proforma: [],
      recent_estimations: [],
      recent_expenses: []
    };

    console.log('✅ Dashboard data retrieved:', dashboardData);

    // Return data in the format expected by TrackNewDashboard component
    res.json(dashboardData);

  } catch (error) {
    console.error('❌ Dashboard API error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch dashboard data',
      error: error.message
    });
  }
});

// Services API endpoint
app.get('/api/services', async (req, res) => {
  try {
    console.log('📋 Services API called');

    const companyId = '8722167d-d2ae-498f-95a3-c18afcf427ae';
    const limit = req.query.limit || 50;
    const offset = req.query.offset || 0;

    const servicesResult = await pool.query(`
      SELECT s.*, c.first_name, c.last_name, c.email, c.phone
      FROM services s
      LEFT JOIN customers c ON s.customer_id = c.id
      WHERE s.company_id = $1
      ORDER BY s.created_at DESC
      LIMIT $2 OFFSET $3
    `, [companyId, limit, offset]);

    const totalResult = await pool.query('SELECT COUNT(*) as total FROM services WHERE company_id = $1', [companyId]);

    const services = servicesResult.rows.map(row => ({
      id: row.id,
      service_code: row.service_code,
      service_number: row.service_code, // Alias for compatibility
      title: row.title,
      description: row.description,
      status: row.status,
      priority: row.priority,
      customer_id: row.customer_id,
      customer_name: `${row.first_name || ''} ${row.last_name || ''}`.trim(),
      customer_email: row.email,
      customer_phone: row.phone,
      device_type: row.device_type || 'N/A',
      assigned_to: row.assigned_to || 'Unassigned',
      created_at: row.created_at,
      updated_at: row.updated_at
    }));

    res.json({
      status: 'success',
      data: services,
      pagination: {
        total: parseInt(totalResult.rows[0].total),
        limit: parseInt(limit),
        offset: parseInt(offset),
        pages: Math.ceil(totalResult.rows[0].total / limit)
      }
    });

  } catch (error) {
    console.error('❌ Services API error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch services',
      error: error.message
    });
  }
});

// Leads API endpoint
app.get('/api/leads', async (req, res) => {
  try {
    console.log('🎯 Leads API called');

    const companyId = '8722167d-d2ae-498f-95a3-c18afcf427ae';
    const limit = req.query.limit || 50;
    const offset = req.query.offset || 0;

    const leadsResult = await pool.query(`
      SELECT l.*, c.first_name, c.last_name, c.email, c.phone
      FROM leads l
      LEFT JOIN customers c ON l.customer_id = c.id
      WHERE l.company_id = $1
      ORDER BY l.created_at DESC
      LIMIT $2 OFFSET $3
    `, [companyId, limit, offset]);

    const totalResult = await pool.query('SELECT COUNT(*) as total FROM leads WHERE company_id = $1', [companyId]);

    const leads = leadsResult.rows.map(row => ({
      id: row.id,
      lead_code: row.lead_code,
      title: row.title,
      description: row.description,
      status: row.status,
      priority: row.priority,
      customer_id: row.customer_id,
      customer_name: `${row.first_name || ''} ${row.last_name || ''}`.trim(),
      customer_email: row.email,
      customer_phone: row.phone,
      source: row.source,
      assigned_to: row.assigned_to,
      created_at: row.created_at,
      updated_at: row.updated_at
    }));

    res.json({
      status: 'success',
      data: leads,
      pagination: {
        total: parseInt(totalResult.rows[0].total),
        limit: parseInt(limit),
        offset: parseInt(offset),
        pages: Math.ceil(totalResult.rows[0].total / limit)
      }
    });

  } catch (error) {
    console.error('❌ Leads API error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch leads',
      error: error.message
    });
  }
});

// Lead Stats API endpoint
app.get('/api/leads/stats', async (req, res) => {
  try {
    console.log('📊 Lead Stats API called');

    const companyId = '8722167d-d2ae-498f-95a3-c18afcf427ae';

    const stats = await Promise.all([
      pool.query('SELECT COUNT(*) as total FROM leads WHERE company_id = $1', [companyId]),
      pool.query('SELECT COUNT(*) as open FROM leads WHERE company_id = $1 AND status = $2', [companyId, 'open']),
      pool.query('SELECT COUNT(*) as qualified FROM leads WHERE company_id = $1 AND status = $2', [companyId, 'qualified']),
      pool.query('SELECT COUNT(*) as converted FROM leads WHERE company_id = $1 AND status = $2', [companyId, 'converted']),
      pool.query('SELECT COUNT(*) as high FROM leads WHERE company_id = $1 AND priority = $2', [companyId, 'high']),
      pool.query('SELECT COUNT(*) as urgent FROM leads WHERE company_id = $1 AND priority = $2', [companyId, 'urgent'])
    ]);

    const totalLeads = parseInt(stats[0].rows[0].total);
    const openLeads = parseInt(stats[1].rows[0].open);
    const qualifiedLeads = parseInt(stats[2].rows[0].qualified);
    const convertedLeads = parseInt(stats[3].rows[0].converted);
    const highPriorityLeads = parseInt(stats[4].rows[0].high);
    const urgentLeads = parseInt(stats[5].rows[0].urgent);

    const conversionRate = totalLeads > 0 ? ((convertedLeads / totalLeads) * 100).toFixed(2) : 0;

    res.json({
      status: 'success',
      data: {
        totalLeads,
        openLeads,
        qualifiedLeads,
        convertedLeads,
        highPriorityLeads,
        urgentLeads,
        conversionRate: parseFloat(conversionRate)
      }
    });

  } catch (error) {
    console.error('❌ Lead Stats API error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch lead statistics',
      error: error.message
    });
  }
});

// Individual Lead API endpoint
app.get('/api/leads/:id', async (req, res) => {
  try {
    console.log('👁️ Individual Lead API called:', req.params.id);

    const { id } = req.params;
    const companyId = '8722167d-d2ae-498f-95a3-c18afcf427ae';

    const leadResult = await pool.query(`
      SELECT l.*, c.first_name, c.last_name, c.email, c.phone
      FROM leads l
      LEFT JOIN customers c ON l.customer_id = c.id
      WHERE l.id = $1 AND l.company_id = $2
    `, [id, companyId]);

    if (leadResult.rows.length === 0) {
      return res.status(404).json({
        status: 'error',
        message: 'Lead not found'
      });
    }

    const row = leadResult.rows[0];
    const lead = {
      id: row.id,
      lead_code: row.lead_code,
      title: row.title,
      description: row.description,
      status: row.status,
      priority: row.priority,
      customer_id: row.customer_id,
      customer: {
        id: row.customer_id,
        name: `${row.first_name || ''} ${row.last_name || ''}`.trim(),
        email: row.email,
        phone: row.phone
      },
      source: row.source,
      assigned_to: row.assigned_to,
      estimated_value: row.estimated_value,
      notes: row.notes,
      created_at: row.created_at,
      updated_at: row.updated_at
    };

    res.json({
      status: 'success',
      message: 'Lead fetched successfully',
      data: lead
    });

  } catch (error) {
    console.error('❌ Individual Lead API error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch lead',
      error: error.message
    });
  }
});

// Create Lead API endpoint
app.post('/api/leads', async (req, res) => {
  try {
    console.log('➕ Create Lead API called:', req.body);

    const {
      title,
      description,
      customerId,
      priority = 'medium',
      status = 'open',
      source = 'website',
      estimatedValue,
      notes
    } = req.body;

    const companyId = '8722167d-d2ae-498f-95a3-c18afcf427ae';
    const userId = 'fce8b418-d90b-4f18-a0e0-40347e7c62d6'; // Admin user ID

    if (!title || !customerId) {
      return res.status(400).json({
        status: 'error',
        message: 'Title and customer are required'
      });
    }

    const leadResult = await pool.query(`
      INSERT INTO leads (
        title, description, customer_id, priority, status, source,
        estimated_value, notes, company_id, created_by, updated_by,
        created_at, updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, NOW(), NOW())
      RETURNING *
    `, [
      title, description, customerId, priority, status, source,
      estimatedValue || null, notes, companyId, userId, userId
    ]);

    const createdLead = leadResult.rows[0];

    res.status(201).json({
      status: 'success',
      message: 'Lead created successfully',
      data: createdLead
    });

  } catch (error) {
    console.error('❌ Create Lead API error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to create lead',
      error: error.message
    });
  }
});

// Update Lead API endpoint (Fixed with Enhanced Logging)
app.put('/api/leads/:id', async (req, res) => {
  try {
    console.log('✏️ Update Lead API called:', req.params.id);
    console.log('📤 Request body:', JSON.stringify(req.body, null, 2));
    console.log('🔑 Request headers:', JSON.stringify(req.headers, null, 2));

    const { id } = req.params;
    const companyId = '8722167d-d2ae-498f-95a3-c18afcf427ae';
    const userId = 'fce8b418-d90b-4f18-a0e0-40347e7c62d6'; // Admin user ID

    // Check if lead exists
    const existingLead = await pool.query(
      'SELECT * FROM leads WHERE id = $1 AND company_id = $2',
      [id, companyId]
    );

    if (existingLead.rows.length === 0) {
      return res.status(404).json({
        status: 'error',
        message: 'Lead not found'
      });
    }

    // Build update query dynamically with proper field mapping
    const updateFields = [];
    const updateValues = [];
    let paramCount = 1;

    // Complete field mapping between frontend and database
    const fieldMapping = {
      'customerId': 'customer_id',
      'estimatedValue': 'estimated_value',
      'assignTo': 'assign_to',
      'leadDate': 'lead_date',
      'assignDate': 'assign_date',
      'followUp': 'follow_up',
      'followUpDate': 'follow_up', // Map followUpDate to follow_up column
      'leadTypeId': 'leadtype_id',
      'leadStatusId': 'leadstatus_id',
      'contactName': 'contact_name',
      'contactEmail': 'contact_email',
      'contactPhone': 'contact_phone'
    };

    // Process each field in the request body
    Object.keys(req.body).forEach(key => {
      if (req.body[key] !== undefined && key !== 'id' && key !== 'companyId' && key !== 'createdBy' && key !== 'createdAt') {
        const dbField = fieldMapping[key] || key;

        // Handle special cases for data types
        let value = req.body[key];

        // Skip empty strings for ALL UUID fields (including assignTo, customerId, etc.)
        const uuidFields = ['assignTo', 'customerId', 'leadTypeId', 'leadStatusId', 'assign_to', 'customer_id', 'leadtype_id', 'leadstatus_id'];
        if ((key.includes('Id') || key.includes('_id') || uuidFields.includes(key)) && value === '') {
          console.log(`⚠️ Skipping empty string for UUID field: ${key}`);
          return;
        }

        // Convert null strings to actual null for ID fields
        if ((key.includes('Id') || key.includes('_id')) && (value === 'null' || value === null)) {
          value = null;
        }

        // Convert date strings to proper format
        if ((key.includes('Date') || key.includes('follow_up')) && value && value !== null) {
          try {
            value = new Date(value).toISOString();
          } catch (e) {
            console.log(`⚠️ Invalid date format for ${key}: ${value}`);
            value = null;
          }
        }

        // Convert numeric values
        if (key === 'estimatedValue' && value !== null && value !== '') {
          value = parseFloat(value) || 0;
        }

        // Convert ID fields to integers
        if ((key === 'leadTypeId' || key === 'leadStatusId') && value !== null && value !== '') {
          value = parseInt(value) || null;
        }

        // Skip fields that don't exist in database
        const skipFields = ['followUpDescription', 'assignDate']; // Add fields that don't exist in DB
        if (skipFields.includes(key)) {
          console.log(`⚠️ Skipping non-existent field: ${key}`);
          return;
        }

        updateFields.push(`${dbField} = $${paramCount}`);
        updateValues.push(value);
        paramCount++;
      }
    });

    // Always update the updated_by and updated_at fields
    updateFields.push(`updated_by = $${paramCount}`);
    updateValues.push(userId);
    paramCount++;

    updateFields.push(`updated_at = NOW()`);

    // Add WHERE clause parameters
    updateValues.push(id);
    updateValues.push(companyId);

    // Ensure we have fields to update
    if (updateFields.length === 0) {
      return res.status(400).json({
        status: 'error',
        message: 'No valid fields to update'
      });
    }

    const updateQuery = `
      UPDATE leads
      SET ${updateFields.join(', ')}
      WHERE id = $${paramCount} AND company_id = $${paramCount + 1}
      RETURNING *
    `;

    console.log('🔍 Update Query:', updateQuery);
    console.log('🔍 Update Values:', updateValues);
    console.log('🔍 Parameter count:', paramCount);

    const updateResult = await pool.query(updateQuery, updateValues);

    if (updateResult.rows.length === 0) {
      return res.status(404).json({
        status: 'error',
        message: 'Lead not found or update failed'
      });
    }

    res.json({
      status: 'success',
      message: 'Lead updated successfully',
      data: updateResult.rows[0]
    });

  } catch (error) {
    console.error('❌ Update Lead API error:', error);
    console.error('❌ Error details:', {
      message: error.message,
      stack: error.stack,
      code: error.code,
      name: error.name
    });
    console.error('❌ Request that caused error:', {
      id: req.params.id,
      body: req.body,
      headers: req.headers.authorization ? 'Bearer [REDACTED]' : 'No auth header'
    });
    res.status(500).json({
      status: 'error',
      message: 'Failed to update lead',
      error: error.message,
      details: error.code || 'Unknown error',
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
});

// Delete Lead API endpoint
app.delete('/api/leads/:id', async (req, res) => {
  try {
    console.log('🗑️ Delete Lead API called:', req.params.id);

    const { id } = req.params;
    const companyId = '8722167d-d2ae-498f-95a3-c18afcf427ae';

    // Check if lead exists
    const existingLead = await pool.query(
      'SELECT * FROM leads WHERE id = $1 AND company_id = $2',
      [id, companyId]
    );

    if (existingLead.rows.length === 0) {
      return res.status(404).json({
        status: 'error',
        message: 'Lead not found'
      });
    }

    // Delete the lead
    await pool.query(
      'DELETE FROM leads WHERE id = $1 AND company_id = $2',
      [id, companyId]
    );

    res.json({
      status: 'success',
      message: 'Lead deleted successfully'
    });

  } catch (error) {
    console.error('❌ Delete Lead API error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to delete lead',
      error: error.message
    });
  }
});

// Users API endpoint with pagination and limit=all support
app.get('/api/users', async (req, res) => {
  try {
    console.log('👥 Users API called with params:', req.query);

    const { page = 1, limit = 10, search = '' } = req.query;
    const companyId = '8722167d-d2ae-498f-95a3-c18afcf427ae';

    // Handle limit=all case
    const isLimitAll = limit === 'all';
    const actualLimit = isLimitAll ? null : parseInt(limit);
    const offset = isLimitAll ? 0 : (parseInt(page) - 1) * actualLimit;

    // Build search condition
    let searchCondition = '';
    let queryParams = [companyId];

    if (search) {
      searchCondition = 'AND (name ILIKE $2 OR email ILIKE $2)';
      queryParams.push(`%${search}%`);
    }

    // Get total count
    const countResult = await pool.query(`
      SELECT COUNT(*) as total
      FROM users
      WHERE company_id = $1 ${searchCondition}
    `, queryParams);

    const totalUsers = parseInt(countResult.rows[0].total);

    // Build the main query
    let mainQuery = `
      SELECT id, name, email, user_type, status, created_at
      FROM users
      WHERE company_id = $1 ${searchCondition}
      ORDER BY name
    `;

    // Add LIMIT and OFFSET only if not fetching all
    let finalQueryParams = [...queryParams];
    if (!isLimitAll) {
      mainQuery += ` LIMIT $${queryParams.length + 1} OFFSET $${queryParams.length + 2}`;
      finalQueryParams.push(actualLimit, offset);
    }

    // Get users
    const usersResult = await pool.query(mainQuery, finalQueryParams);

    const users = usersResult.rows.map(row => ({
      id: row.id,
      name: row.name,
      email: row.email,
      role: row.user_type || 'user',
      status: row.status || 'active',
      createdAt: row.created_at
    }));

    // Calculate pagination info
    const totalPages = isLimitAll ? 1 : Math.ceil(totalUsers / actualLimit);
    const currentPage = isLimitAll ? 1 : parseInt(page);

    res.json({
      status: 'success',
      message: 'Users fetched successfully',
      data: isLimitAll ? users : {
        users,
        pagination: {
          currentPage,
          totalPages,
          totalItems: totalUsers,
          itemsPerPage: actualLimit,
          hasNextPage: currentPage < totalPages,
          hasPrevPage: currentPage > 1
        }
      }
    });

  } catch (error) {
    console.error('❌ Users API error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch users',
      error: error.message
    });
  }
});

// Lead Analytics API endpoints
app.get('/api/leads/analytics/conversion-funnel', async (req, res) => {
  try {
    console.log('📊 Lead Conversion Funnel API called');

    const companyId = '8722167d-d2ae-498f-95a3-c18afcf427ae';

    const funnelData = await Promise.all([
      pool.query('SELECT COUNT(*) as total FROM leads WHERE company_id = $1', [companyId]),
      pool.query('SELECT COUNT(*) as qualified FROM leads WHERE company_id = $1 AND status = $2', [companyId, 'qualified']),
      pool.query('SELECT COUNT(*) as in_progress FROM leads WHERE company_id = $1 AND status = $2', [companyId, 'in_progress']),
      pool.query('SELECT COUNT(*) as converted FROM leads WHERE company_id = $1 AND status = $2', [companyId, 'converted'])
    ]);

    const totalLeads = parseInt(funnelData[0].rows[0].total);
    const qualifiedLeads = parseInt(funnelData[1].rows[0].qualified);
    const inProgressLeads = parseInt(funnelData[2].rows[0].in_progress);
    const convertedLeads = parseInt(funnelData[3].rows[0].converted);

    const conversionFunnel = [
      { stage: 'Total Leads', count: totalLeads, percentage: 100 },
      { stage: 'Qualified', count: qualifiedLeads, percentage: totalLeads > 0 ? ((qualifiedLeads / totalLeads) * 100).toFixed(1) : 0 },
      { stage: 'In Progress', count: inProgressLeads, percentage: totalLeads > 0 ? ((inProgressLeads / totalLeads) * 100).toFixed(1) : 0 },
      { stage: 'Converted', count: convertedLeads, percentage: totalLeads > 0 ? ((convertedLeads / totalLeads) * 100).toFixed(1) : 0 }
    ];

    res.json({
      status: 'success',
      data: conversionFunnel
    });

  } catch (error) {
    console.error('❌ Lead Conversion Funnel API error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch conversion funnel data',
      error: error.message
    });
  }
});

app.get('/api/leads/analytics/source-distribution', async (req, res) => {
  try {
    console.log('📊 Lead Source Distribution API called');

    const companyId = '8722167d-d2ae-498f-95a3-c18afcf427ae';

    const sourceResult = await pool.query(`
      SELECT source, COUNT(*) as count
      FROM leads
      WHERE company_id = $1
      GROUP BY source
      ORDER BY count DESC
    `, [companyId]);

    const sourceDistribution = sourceResult.rows.map(row => ({
      source: row.source || 'Unknown',
      count: parseInt(row.count),
      percentage: 0 // Will be calculated on frontend
    }));

    res.json({
      status: 'success',
      data: sourceDistribution
    });

  } catch (error) {
    console.error('❌ Lead Source Distribution API error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch source distribution data',
      error: error.message
    });
  }
});

app.get('/api/leads/analytics/status-timeline', async (req, res) => {
  try {
    console.log('📊 Lead Status Timeline API called');

    const companyId = '8722167d-d2ae-498f-95a3-c18afcf427ae';

    const statusResult = await pool.query(`
      SELECT status, COUNT(*) as count
      FROM leads
      WHERE company_id = $1
      GROUP BY status
      ORDER BY count DESC
    `, [companyId]);

    const statusTimeline = statusResult.rows.map(row => ({
      status: row.status || 'Unknown',
      count: parseInt(row.count)
    }));

    res.json({
      status: 'success',
      data: statusTimeline
    });

  } catch (error) {
    console.error('❌ Lead Status Timeline API error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch status timeline data',
      error: error.message
    });
  }
});

app.get('/api/leads/analytics/priority-breakdown', async (req, res) => {
  try {
    console.log('📊 Lead Priority Breakdown API called');

    const companyId = '8722167d-d2ae-498f-95a3-c18afcf427ae';

    const priorityResult = await pool.query(`
      SELECT priority, COUNT(*) as count
      FROM leads
      WHERE company_id = $1
      GROUP BY priority
      ORDER BY count DESC
    `, [companyId]);

    const priorityBreakdown = priorityResult.rows.map(row => ({
      priority: row.priority || 'Unknown',
      count: parseInt(row.count)
    }));

    res.json({
      status: 'success',
      data: priorityBreakdown
    });

  } catch (error) {
    console.error('❌ Lead Priority Breakdown API error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch priority breakdown data',
      error: error.message
    });
  }
});

// Lead Conversion API endpoints (Mock Implementation for Demo)
app.post('/api/leads/:id/convert-to-invoice', async (req, res) => {
  try {
    console.log('💰 Converting lead to invoice:', req.params.id);

    const { id } = req.params;
    const { items = [], dueDate, paymentTerms, notes } = req.body;
    const companyId = '8722167d-d2ae-498f-95a3-c18afcf427ae';
    const userId = 'fce8b418-d90b-4f18-a0e0-40347e7c62d6'; // Admin user ID

    // Get lead details
    const leadResult = await pool.query(`
      SELECT l.*, c.first_name, c.last_name, c.email, c.phone, c.address
      FROM leads l
      LEFT JOIN customers c ON l.customer_id = c.id
      WHERE l.id = $1 AND l.company_id = $2
    `, [id, companyId]);

    if (leadResult.rows.length === 0) {
      return res.status(404).json({
        status: 'error',
        message: 'Lead not found'
      });
    }

    const lead = leadResult.rows[0];

    // Mock invoice creation (since exact schema is complex)
    const mockInvoice = {
      id: `invoice-${Date.now()}`,
      sales_code: `INV-${Date.now()}`,
      customer_id: lead.customer_id,
      sales_date: new Date().toISOString(),
      due_date: dueDate || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
      payment_mode: 'cash',
      total_amount: (lead.estimated_value || 1000),
      status: 'draft',
      notes: notes || `Invoice created from lead: ${lead.title}`,
      company_id: companyId,
      created_by: userId,
      created_at: new Date().toISOString()
    };

    // Update lead status to converted
    await pool.query(`
      UPDATE leads
      SET status = 'converted', updated_by = $1, updated_at = NOW()
      WHERE id = $2 AND company_id = $3
    `, [userId, id, companyId]);

    res.json({
      status: 'success',
      message: 'Lead converted to invoice successfully',
      data: {
        lead,
        invoice: mockInvoice,
        invoiceId: mockInvoice.id,
        conversionType: 'lead-to-invoice',
        redirectUrl: `/sales/${mockInvoice.id}`
      }
    });

  } catch (error) {
    console.error('❌ Lead to Invoice conversion error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to convert lead to invoice',
      error: error.message
    });
  }
});

app.post('/api/leads/:id/convert-to-service', async (req, res) => {
  try {
    console.log('🔧 Converting lead to service:', req.params.id);

    const { id } = req.params;
    const { serviceType, priority, scheduledDate, notes } = req.body;
    const companyId = '8722167d-d2ae-498f-95a3-c18afcf427ae';
    const userId = 'fce8b418-d90b-4f18-a0e0-40347e7c62d6'; // Admin user ID

    // Get lead details
    const leadResult = await pool.query(`
      SELECT l.*, c.first_name, c.last_name, c.email, c.phone, c.address
      FROM leads l
      LEFT JOIN customers c ON l.customer_id = c.id
      WHERE l.id = $1 AND l.company_id = $2
    `, [id, companyId]);

    if (leadResult.rows.length === 0) {
      return res.status(404).json({
        status: 'error',
        message: 'Lead not found'
      });
    }

    const lead = leadResult.rows[0];

    // Mock service creation (since exact schema is complex)
    const mockService = {
      id: `service-${Date.now()}`,
      service_code: `SRV-${Date.now()}`,
      customer_id: lead.customer_id,
      service_date: scheduledDate || new Date().toISOString(),
      service_type: serviceType || 'General Service',
      description: lead.description || lead.title,
      priority: priority || lead.priority || 'medium',
      status: 'pending',
      estimated_cost: lead.estimated_value || 0,
      notes: notes || `Service created from lead: ${lead.title}`,
      company_id: companyId,
      created_by: userId,
      created_at: new Date().toISOString()
    };

    // Update lead status to converted
    await pool.query(`
      UPDATE leads
      SET status = 'converted', updated_by = $1, updated_at = NOW()
      WHERE id = $2 AND company_id = $3
    `, [userId, id, companyId]);

    res.json({
      status: 'success',
      message: 'Lead converted to service successfully',
      data: {
        lead,
        service: mockService,
        serviceId: mockService.id,
        conversionType: 'lead-to-service',
        redirectUrl: `/services/${mockService.id}`
      }
    });

  } catch (error) {
    console.error('❌ Lead to Service conversion error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to convert lead to service',
      error: error.message
    });
  }
});

// Lead Types API endpoint
app.get('/api/lead-types', async (req, res) => {
  try {
    console.log('🏷️ Lead Types API called');

    // Mock lead types data
    const leadTypes = [
      { id: 1, name: 'Sales Lead', description: 'Potential sales opportunity', active: true },
      { id: 2, name: 'Service Lead', description: 'Service request inquiry', active: true },
      { id: 3, name: 'Support Lead', description: 'Customer support inquiry', active: true },
      { id: 4, name: 'Partnership Lead', description: 'Business partnership opportunity', active: true },
      { id: 5, name: 'Referral Lead', description: 'Referred by existing customer', active: true }
    ];

    res.json({
      status: 'success',
      message: 'Lead types fetched successfully',
      data: leadTypes
    });

  } catch (error) {
    console.error('❌ Lead Types API error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch lead types',
      error: error.message
    });
  }
});

// Lead Statuses API endpoint
app.get('/api/lead-statuses', async (req, res) => {
  try {
    console.log('📊 Lead Statuses API called');

    // Mock lead statuses data
    const leadStatuses = [
      { id: 1, name: 'Open', description: 'New lead, not yet contacted', color: '#3B82F6', active: true },
      { id: 2, name: 'Contacted', description: 'Initial contact made', color: '#8B5CF6', active: true },
      { id: 3, name: 'Qualified', description: 'Lead has been qualified', color: '#F59E0B', active: true },
      { id: 4, name: 'In Progress', description: 'Actively working on lead', color: '#10B981', active: true },
      { id: 5, name: 'Converted', description: 'Lead converted to customer', color: '#059669', active: true },
      { id: 6, name: 'Lost', description: 'Lead was not converted', color: '#EF4444', active: true },
      { id: 7, name: 'Closed', description: 'Lead closed without conversion', color: '#6B7280', active: true }
    ];

    res.json({
      status: 'success',
      message: 'Lead statuses fetched successfully',
      data: leadStatuses
    });

  } catch (error) {
    console.error('❌ Lead Statuses API error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch lead statuses',
      error: error.message
    });
  }
});

// Sales API endpoint
app.get('/api/sales', async (req, res) => {
  try {
    console.log('💰 Sales API called');

    const companyId = '8722167d-d2ae-498f-95a3-c18afcf427ae';
    const limit = req.query.limit || 50;
    const offset = req.query.offset || 0;

    const salesResult = await pool.query(`
      SELECT s.*, c.first_name, c.last_name, c.email, c.phone
      FROM sales s
      LEFT JOIN customers c ON s.customer_id = c.id
      WHERE s.company_id = $1
      ORDER BY s.created_at DESC
      LIMIT $2 OFFSET $3
    `, [companyId, limit, offset]);

    const totalResult = await pool.query('SELECT COUNT(*) as total FROM sales WHERE company_id = $1', [companyId]);

    const sales = salesResult.rows.map(row => ({
      id: row.id,
      sales_code: row.sales_code,
      invoice_id: row.invoice_id,
      sales_type: row.sales_type,
      status: row.status,
      customer_id: row.customer_id,
      customer_name: `${row.first_name || ''} ${row.last_name || ''}`.trim(),
      customer_email: row.email,
      customer_phone: row.phone,
      sales_date: row.sales_date,
      due_date: row.due_date,
      sub_total: row.sub_total,
      discount_amount: row.discount_amount,
      tax_amount: row.tax_amount,
      grand_total: row.grand_total,
      payment_status: row.payment_status,
      created_at: row.created_at,
      updated_at: row.updated_at
    }));

    res.json({
      status: 'success',
      data: sales,
      pagination: {
        total: parseInt(totalResult.rows[0].total),
        limit: parseInt(limit),
        offset: parseInt(offset),
        pages: Math.ceil(totalResult.rows[0].total / limit)
      }
    });

  } catch (error) {
    console.error('❌ Sales API error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch sales',
      error: error.message
    });
  }
});

// Estimations API endpoint
app.get('/api/estimations', async (req, res) => {
  try {
    console.log('📊 Estimations API called');

    const companyId = '8722167d-d2ae-498f-95a3-c18afcf427ae';
    const limit = req.query.limit || 50;
    const offset = req.query.offset || 0;

    const estimationsResult = await pool.query(`
      SELECT e.*, c.first_name, c.last_name, c.email, c.phone
      FROM estimations e
      LEFT JOIN customers c ON e.customer_id = c.id
      WHERE e.company_id = $1
      ORDER BY e.created_at DESC
      LIMIT $2 OFFSET $3
    `, [companyId, limit, offset]);

    const totalResult = await pool.query('SELECT COUNT(*) as total FROM estimations WHERE company_id = $1', [companyId]);

    const estimations = estimationsResult.rows.map(row => ({
      id: row.id,
      estimate_num: row.estimate_num,
      estimate_date: row.estimate_date,
      estimate_type: row.estimate_type,
      status: row.status,
      customer_id: row.customer_id,
      customer_name: `${row.first_name || ''} ${row.last_name || ''}`.trim(),
      customer_email: row.email,
      customer_phone: row.phone,
      grand_total: row.grand_total,
      items: row.items,
      created_at: row.created_at,
      updated_at: row.updated_at
    }));

    res.json({
      status: 'success',
      data: estimations,
      pagination: {
        total: parseInt(totalResult.rows[0].total),
        limit: parseInt(limit),
        offset: parseInt(offset),
        pages: Math.ceil(totalResult.rows[0].total / limit)
      }
    });

  } catch (error) {
    console.error('❌ Estimations API error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch estimations',
      error: error.message
    });
  }
});

// Expenses API endpoint
app.get('/api/expenses', async (req, res) => {
  try {
    console.log('💸 Expenses API called');

    const companyId = '8722167d-d2ae-498f-95a3-c18afcf427ae';
    const limit = req.query.limit || 50;
    const offset = req.query.offset || 0;

    const expensesResult = await pool.query(`
      SELECT
        e.*,
        s.service_code,
        s.title as service_title,
        et.name as expense_type_name
      FROM expenses e
      LEFT JOIN services s ON e.service_id = s.id
      LEFT JOIN expense_types et ON e.expense_type_id = et.id
      WHERE e.company_id = $1
      ORDER BY e.created_at DESC
      LIMIT $2 OFFSET $3
    `, [companyId, limit, offset]);

    const totalResult = await pool.query('SELECT COUNT(*) as total FROM expenses WHERE company_id = $1', [companyId]);

    const expenses = expensesResult.rows.map(row => {
      const expense = {
        id: row.id,
        nameOfPurpose: row.name_of_purpose,
        expenseTypeId: row.expense_type_id,
        expenseType: row.expense_type_name || row.expense_type || 'Other',
        amount: parseFloat(row.amount) || 0,
        date: row.date,
        description: row.description,
        attachment: row.attachment,
        vendor: row.vendor,
        receiptNumber: row.receipt_number,
        paymentMethod: row.payment_method,
        status: row.status || 'pending',
        isReimbursable: row.is_reimbursable || false,
        notes: row.notes,
        serviceId: row.service_id,
        serviceCode: row.service_code,
        serviceTitle: row.service_title,
        createdAt: row.created_at,
        updatedAt: row.updated_at,
        createdBy: row.created_by,
        updatedBy: row.updated_by
      };

      // Add user associations for "Updated By" field
      if (row.created_by) {
        const createdByUser = mockUsers.find(u => u.id === row.created_by);
        if (createdByUser) {
          expense.createdByUser = {
            id: createdByUser.id,
            name: createdByUser.name,
            email: createdByUser.email,
            phone: createdByUser.phone
          };
        }
      }

      if (row.updated_by) {
        const updatedByUser = mockUsers.find(u => u.id === row.updated_by);
        if (updatedByUser) {
          expense.updatedByUser = {
            id: updatedByUser.id,
            name: updatedByUser.name,
            email: updatedByUser.email,
            phone: updatedByUser.phone
          };
        }
      }

      return expense;
    });

    res.json({
      status: 'success',
      message: 'Expenses retrieved successfully',
      data: {
        expenses: expenses,
        pagination: {
          total: parseInt(totalResult.rows[0].total),
          limit: parseInt(limit),
          offset: parseInt(offset),
          pages: Math.ceil(totalResult.rows[0].total / limit)
        }
      }
    });

  } catch (error) {
    console.error('❌ Expenses API error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch expenses',
      error: error.message
    });
  }
});

// POST /api/expenses - Create new expense
app.post('/api/expenses', async (req, res) => {
  try {
    console.log('💸 Create Expense API called:', req.body);

    // Get user ID from authentication token
    const authHeader = req.headers.authorization;
    let userId = null;

    if (authHeader && authHeader.startsWith('Bearer ')) {
      try {
        const token = authHeader.substring(7);
        const payload = JSON.parse(atob(token.split('.')[1]));
        userId = payload.id;
      } catch (error) {
        console.log('⚠️ Token parsing error:', error.message);
      }
    }

    const {
      nameOfPurpose,
      expenseTypeId,
      amount,
      date,
      description,
      vendor,
      receiptNumber,
      paymentMethod,
      isReimbursable,
      notes
    } = req.body;

    const companyId = '8722167d-d2ae-498f-95a3-c18afcf427ae';

    // Validate required fields
    if (!nameOfPurpose || !expenseTypeId || !amount || !date) {
      return res.status(400).json({
        status: 'error',
        message: 'Name of purpose, expense type, amount, and date are required'
      });
    }

    // Validate expense type exists
    const expenseTypeCheck = await pool.query(
      'SELECT id FROM expense_types WHERE id = $1 AND company_id = $2',
      [expenseTypeId, companyId]
    );

    if (expenseTypeCheck.rows.length === 0) {
      return res.status(400).json({
        status: 'error',
        message: 'Invalid expense type'
      });
    }

    // Create expense
    const result = await pool.query(`
      INSERT INTO expenses (
        name_of_purpose,
        expense_type_id,
        amount,
        date,
        description,
        vendor,
        receipt_number,
        payment_method,
        is_reimbursable,
        notes,
        company_id,
        status,
        created_by,
        updated_by,
        created_at,
        updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, 'pending', $12, $13, NOW(), NOW())
      RETURNING *
    `, [
      nameOfPurpose,
      expenseTypeId,
      parseFloat(amount),
      date,
      description || null,
      vendor || null,
      receiptNumber || null,
      paymentMethod || 'cash',
      isReimbursable || false,
      notes || null,
      companyId,
      userId, // created_by
      userId  // updated_by
    ]);

    const createdExpense = result.rows[0];

    // Get expense with expense type name
    const expenseWithType = await pool.query(`
      SELECT
        e.*,
        et.name as expense_type_name
      FROM expenses e
      LEFT JOIN expense_types et ON e.expense_type_id = et.id
      WHERE e.id = $1
    `, [createdExpense.id]);

    const expense = expenseWithType.rows[0];

    // Format response to match frontend expectations
    const formattedExpense = {
      id: expense.id,
      nameOfPurpose: expense.name_of_purpose,
      expenseTypeId: expense.expense_type_id,
      expenseType: expense.expense_type_name || 'Other',
      amount: parseFloat(expense.amount),
      date: expense.date,
      description: expense.description,
      vendor: expense.vendor,
      receiptNumber: expense.receipt_number,
      paymentMethod: expense.payment_method,
      isReimbursable: expense.is_reimbursable,
      notes: expense.notes,
      status: expense.status,
      createdAt: expense.created_at,
      updatedAt: expense.updated_at,
      createdBy: expense.created_by,
      updatedBy: expense.updated_by
    };

    // Add user associations for "Updated By" field
    if (expense.created_by) {
      const createdByUser = mockUsers.find(u => u.id === expense.created_by);
      if (createdByUser) {
        formattedExpense.createdByUser = {
          id: createdByUser.id,
          name: createdByUser.name,
          email: createdByUser.email,
          phone: createdByUser.phone
        };
      }
    }

    if (expense.updated_by) {
      const updatedByUser = mockUsers.find(u => u.id === expense.updated_by);
      if (updatedByUser) {
        formattedExpense.updatedByUser = {
          id: updatedByUser.id,
          name: updatedByUser.name,
          email: updatedByUser.email,
          phone: updatedByUser.phone
        };
      }
    }

    res.status(201).json({
      status: 'success',
      message: 'Expense created successfully',
      data: {
        expense: formattedExpense
      }
    });

  } catch (error) {
    console.error('❌ Create Expense API error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to create expense',
      error: error.message
    });
  }
});

// GET /api/expenses/:id - Get expense by ID
app.get('/api/expenses/:id', async (req, res) => {
  try {
    console.log('💸 Get Expense by ID API called:', req.params.id);

    const { id } = req.params;
    const companyId = '8722167d-d2ae-498f-95a3-c18afcf427ae';

    // Get expense with expense type name
    const expenseResult = await pool.query(`
      SELECT
        e.*,
        et.name as expense_type_name
      FROM expenses e
      LEFT JOIN expense_types et ON e.expense_type_id = et.id
      WHERE e.id = $1 AND e.company_id = $2
    `, [id, companyId]);

    if (expenseResult.rows.length === 0) {
      return res.status(404).json({
        status: 'error',
        message: 'Expense not found'
      });
    }

    const expense = expenseResult.rows[0];

    // Format response to match frontend expectations
    const formattedExpense = {
      id: expense.id,
      nameOfPurpose: expense.name_of_purpose,
      expenseTypeId: expense.expense_type_id,
      expenseType: expense.expense_type_name || 'Other',
      amount: parseFloat(expense.amount),
      date: expense.date,
      description: expense.description,
      vendor: expense.vendor,
      receiptNumber: expense.receipt_number,
      paymentMethod: expense.payment_method,
      isReimbursable: expense.is_reimbursable,
      notes: expense.notes,
      status: expense.status,
      createdAt: expense.created_at,
      updatedAt: expense.updated_at,
      createdBy: expense.created_by,
      updatedBy: expense.updated_by
    };

    // Add user associations for "Updated By" field
    if (expense.created_by) {
      const createdByUser = mockUsers.find(u => u.id === expense.created_by);
      if (createdByUser) {
        formattedExpense.createdByUser = {
          id: createdByUser.id,
          name: createdByUser.name,
          email: createdByUser.email,
          phone: createdByUser.phone
        };
      }
    }

    if (expense.updated_by) {
      const updatedByUser = mockUsers.find(u => u.id === expense.updated_by);
      if (updatedByUser) {
        formattedExpense.updatedByUser = {
          id: updatedByUser.id,
          name: updatedByUser.name,
          email: updatedByUser.email,
          phone: updatedByUser.phone
        };
      }
    }

    res.json({
      status: 'success',
      message: 'Expense retrieved successfully',
      data: {
        expense: formattedExpense
      }
    });

  } catch (error) {
    console.error('❌ Get Expense by ID API error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch expense',
      error: error.message
    });
  }
});

// PUT /api/expenses/:id - Update expense
app.put('/api/expenses/:id', async (req, res) => {
  try {
    console.log('💸 Update Expense API called:', req.params.id, req.body);

    // Get user ID from authentication token
    const authHeader = req.headers.authorization;
    let userId = null;

    if (authHeader && authHeader.startsWith('Bearer ')) {
      try {
        const token = authHeader.substring(7);
        const payload = JSON.parse(atob(token.split('.')[1]));
        userId = payload.id;
      } catch (error) {
        console.log('⚠️ Token parsing error:', error.message);
      }
    }

    const { id } = req.params;
    const {
      nameOfPurpose,
      expenseTypeId,
      amount,
      date,
      description,
      vendor,
      receiptNumber,
      paymentMethod,
      isReimbursable,
      notes,
      status
    } = req.body;

    const companyId = '8722167d-d2ae-498f-95a3-c18afcf427ae';

    // Check if expense exists
    const existingExpense = await pool.query(
      'SELECT id FROM expenses WHERE id = $1 AND company_id = $2',
      [id, companyId]
    );

    if (existingExpense.rows.length === 0) {
      return res.status(404).json({
        status: 'error',
        message: 'Expense not found'
      });
    }

    // Validate expense type if provided
    if (expenseTypeId) {
      const expenseTypeCheck = await pool.query(
        'SELECT id FROM expense_types WHERE id = $1 AND company_id = $2',
        [expenseTypeId, companyId]
      );

      if (expenseTypeCheck.rows.length === 0) {
        return res.status(400).json({
          status: 'error',
          message: 'Invalid expense type'
        });
      }
    }

    // Build update query dynamically
    const updateFields = [];
    const updateValues = [];
    let paramCount = 0;

    if (nameOfPurpose !== undefined) {
      paramCount++;
      updateFields.push(`name_of_purpose = $${paramCount}`);
      updateValues.push(nameOfPurpose);
    }

    if (expenseTypeId !== undefined) {
      paramCount++;
      updateFields.push(`expense_type_id = $${paramCount}`);
      updateValues.push(expenseTypeId);
    }

    if (amount !== undefined) {
      paramCount++;
      updateFields.push(`amount = $${paramCount}`);
      updateValues.push(parseFloat(amount));
    }

    if (date !== undefined) {
      paramCount++;
      updateFields.push(`date = $${paramCount}`);
      updateValues.push(date);
    }

    if (description !== undefined) {
      paramCount++;
      updateFields.push(`description = $${paramCount}`);
      updateValues.push(description);
    }

    if (vendor !== undefined) {
      paramCount++;
      updateFields.push(`vendor = $${paramCount}`);
      updateValues.push(vendor);
    }

    if (receiptNumber !== undefined) {
      paramCount++;
      updateFields.push(`receipt_number = $${paramCount}`);
      updateValues.push(receiptNumber);
    }

    if (paymentMethod !== undefined) {
      paramCount++;
      updateFields.push(`payment_method = $${paramCount}`);
      updateValues.push(paymentMethod);
    }

    if (isReimbursable !== undefined) {
      paramCount++;
      updateFields.push(`is_reimbursable = $${paramCount}`);
      updateValues.push(isReimbursable);
    }

    if (notes !== undefined) {
      paramCount++;
      updateFields.push(`notes = $${paramCount}`);
      updateValues.push(notes);
    }

    if (status !== undefined) {
      paramCount++;
      updateFields.push(`status = $${paramCount}`);
      updateValues.push(status);
    }

    if (updateFields.length === 0) {
      return res.status(400).json({
        status: 'error',
        message: 'No valid fields to update'
      });
    }

    // Add updated_by if user is authenticated
    if (userId) {
      paramCount++;
      updateFields.push(`updated_by = $${paramCount}`);
      updateValues.push(userId);
    }

    // Add updated_at
    updateFields.push(`updated_at = NOW()`);

    // Add WHERE clause parameters
    paramCount++;
    updateValues.push(id);
    paramCount++;
    updateValues.push(companyId);

    const updateQuery = `
      UPDATE expenses
      SET ${updateFields.join(', ')}
      WHERE id = $${paramCount - 1} AND company_id = $${paramCount}
      RETURNING *
    `;

    const result = await pool.query(updateQuery, updateValues);
    const updatedExpense = result.rows[0];

    // Get expense with expense type name
    const expenseWithType = await pool.query(`
      SELECT
        e.*,
        et.name as expense_type_name
      FROM expenses e
      LEFT JOIN expense_types et ON e.expense_type_id = et.id
      WHERE e.id = $1
    `, [updatedExpense.id]);

    const expense = expenseWithType.rows[0];

    // Format response to match frontend expectations
    const formattedExpense = {
      id: expense.id,
      nameOfPurpose: expense.name_of_purpose,
      expenseTypeId: expense.expense_type_id,
      expenseType: expense.expense_type_name || 'Other',
      amount: parseFloat(expense.amount),
      date: expense.date,
      description: expense.description,
      vendor: expense.vendor,
      receiptNumber: expense.receipt_number,
      paymentMethod: expense.payment_method,
      isReimbursable: expense.is_reimbursable,
      notes: expense.notes,
      status: expense.status,
      createdAt: expense.created_at,
      updatedAt: expense.updated_at,
      createdBy: expense.created_by,
      updatedBy: expense.updated_by
    };

    // Add user associations for "Updated By" field
    if (expense.created_by) {
      const createdByUser = mockUsers.find(u => u.id === expense.created_by);
      if (createdByUser) {
        formattedExpense.createdByUser = {
          id: createdByUser.id,
          name: createdByUser.name,
          email: createdByUser.email,
          phone: createdByUser.phone
        };
      }
    }

    if (expense.updated_by) {
      const updatedByUser = mockUsers.find(u => u.id === expense.updated_by);
      if (updatedByUser) {
        formattedExpense.updatedByUser = {
          id: updatedByUser.id,
          name: updatedByUser.name,
          email: updatedByUser.email,
          phone: updatedByUser.phone
        };
      }
    }

    res.json({
      status: 'success',
      message: 'Expense updated successfully',
      data: {
        expense: formattedExpense
      }
    });

  } catch (error) {
    console.error('❌ Update Expense API error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to update expense',
      error: error.message
    });
  }
});

// DELETE /api/expenses/:id - Delete expense
app.delete('/api/expenses/:id', async (req, res) => {
  try {
    console.log('💸 Delete Expense API called:', req.params.id);

    const { id } = req.params;
    const companyId = '8722167d-d2ae-498f-95a3-c18afcf427ae';

    // Check if expense exists
    const existingExpense = await pool.query(
      'SELECT id, name_of_purpose FROM expenses WHERE id = $1 AND company_id = $2',
      [id, companyId]
    );

    if (existingExpense.rows.length === 0) {
      return res.status(404).json({
        status: 'error',
        message: 'Expense not found'
      });
    }

    const expense = existingExpense.rows[0];

    // Delete the expense
    await pool.query(
      'DELETE FROM expenses WHERE id = $1 AND company_id = $2',
      [id, companyId]
    );

    res.json({
      status: 'success',
      message: `Expense "${expense.name_of_purpose}" deleted successfully`
    });

  } catch (error) {
    console.error('❌ Delete Expense API error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to delete expense',
      error: error.message
    });
  }
});

// Analytics API endpoint for expenses
app.get('/api/analytics/expenses', async (req, res) => {
  try {
    console.log('📊 Expense Analytics API called');

    const companyId = '8722167d-d2ae-498f-95a3-c18afcf427ae';
    const { period = '30' } = req.query;

    // Get basic expense data
    const expensesResult = await pool.query(`
      SELECT COUNT(*)::integer as count, COALESCE(SUM(amount), 0)::numeric as total_amount
      FROM expenses
      WHERE company_id = $1
    `, [companyId]);

    const totalExpenses = parseInt(expensesResult.rows[0].count);
    const totalAmount = parseFloat(expensesResult.rows[0].total_amount || 0);

    // Daily amount trends (last 30 days)
    const dailyAmountTrends = await pool.query(`
      SELECT
        date::date as date,
        COUNT(*)::integer as count,
        SUM(amount)::numeric as total_amount
      FROM expenses
      WHERE company_id = $1
        AND date >= CURRENT_DATE - INTERVAL '30 days'
      GROUP BY date::date
      ORDER BY date DESC
      LIMIT 30
    `, [companyId]);

    // Category amount breakdown
    const categoryAmountBreakdown = await pool.query(`
      SELECT
        COALESCE(et.name, 'Uncategorized') as category,
        COUNT(*)::integer as count,
        SUM(e.amount)::numeric as total_amount
      FROM expenses e
      LEFT JOIN expense_types et ON e.expense_type_id = et.id
      WHERE e.company_id = $1
      GROUP BY et.name
      ORDER BY total_amount DESC
      LIMIT 10
    `, [companyId]);

    // Monthly amount trends (last 6 months)
    const monthlyAmountTrends = await pool.query(`
      SELECT
        TO_CHAR(date, 'YYYY-MM') as month,
        COUNT(*)::integer as count,
        SUM(amount)::numeric as total_amount
      FROM expenses
      WHERE company_id = $1
        AND date >= CURRENT_DATE - INTERVAL '6 months'
      GROUP BY TO_CHAR(date, 'YYYY-MM')
      ORDER BY month DESC
      LIMIT 6
    `, [companyId]);

    // Amount distribution
    const amountDistribution = await pool.query(`
      SELECT
        CASE
          WHEN amount < 1000 THEN 'Under ₹1,000'
          WHEN amount < 5000 THEN '₹1,000 - ₹5,000'
          WHEN amount < 10000 THEN '₹5,000 - ₹10,000'
          WHEN amount < 25000 THEN '₹10,000 - ₹25,000'
          ELSE 'Over ₹25,000'
        END as amount_range,
        COUNT(*)::integer as count,
        SUM(amount)::numeric as total_amount
      FROM expenses
      WHERE company_id = $1
      GROUP BY amount_range
      ORDER BY MIN(amount)
    `, [companyId]);

    // Status distribution (simplified)
    const statusDistribution = [
      { status: 'approved', count: Math.floor(totalExpenses * 0.7) },
      { status: 'pending', count: Math.floor(totalExpenses * 0.2) },
      { status: 'rejected', count: Math.floor(totalExpenses * 0.1) }
    ];

    res.json({
      status: 'success',
      data: {
        // Core analytics data
        dailyAmountTrends: dailyAmountTrends.rows,
        monthlyAmountTrends: monthlyAmountTrends.rows,
        categoryAmountBreakdown: categoryAmountBreakdown.rows,
        amountDistribution: amountDistribution.rows,
        statusDistribution,

        // Summary metrics
        totalExpenses,
        totalAmount,

        // Additional chart data for frontend compatibility
        weeklyAmountAnalysis: monthlyAmountTrends.rows,
        dailyExpenseCount: dailyAmountTrends.rows,
        dailyAverageAmount: dailyAmountTrends.rows,
        cumulativeAmountTrends: dailyAmountTrends.rows
      }
    });

  } catch (error) {
    console.error('❌ Error in expense analytics:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch expense analytics',
      error: error.message
    });
  }
});

// Expense Types API endpoints
app.get('/api/expense-types', async (req, res) => {
  try {
    console.log('💰 Expense Types API called');

    const { active = 'true' } = req.query;
    const companyId = '8722167d-d2ae-498f-95a3-c18afcf427ae'; // Using actual company ID from database

    let query = 'SELECT id, name, description, is_active as "isActive", created_at as "createdAt", updated_at as "updatedAt" FROM expense_types WHERE company_id = $1';
    const params = [companyId];

    if (active === 'true') {
      query += ' AND is_active = true';
    }

    query += ' ORDER BY name ASC';

    const result = await pool.query(query, params);

    res.json({
      status: 'success',
      message: 'Expense types retrieved successfully',
      data: {
        expenseTypes: result.rows
      }
    });
  } catch (error) {
    console.error('❌ Expense Types API error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch expense types',
      error: error.message
    });
  }
});

app.post('/api/expense-types', async (req, res) => {
  try {
    console.log('💰 Create Expense Type API called');

    const { name, description } = req.body;
    const companyId = '8722167d-d2ae-498f-95a3-c18afcf427ae'; // Using actual company ID from database

    if (!name) {
      return res.status(400).json({
        status: 'error',
        message: 'Expense type name is required'
      });
    }

    // Check if expense type already exists
    const existingType = await pool.query(
      'SELECT id FROM expense_types WHERE name = $1 AND company_id = $2',
      [name.trim(), companyId]
    );

    if (existingType.rows.length > 0) {
      return res.status(409).json({
        status: 'error',
        message: 'Expense type with this name already exists'
      });
    }

    const result = await pool.query(
      `INSERT INTO expense_types (name, description, company_id, is_active, created_at, updated_at)
       VALUES ($1, $2, $3, true, NOW(), NOW())
       RETURNING id, name, description, is_active as "isActive", created_at as "createdAt", updated_at as "updatedAt"`,
      [name.trim(), description || null, companyId]
    );

    const expenseType = result.rows[0];

    res.status(201).json({
      status: 'success',
      message: 'Expense type created successfully',
      data: {
        expenseType: expenseType
      }
    });
  } catch (error) {
    console.error('❌ Create Expense Type API error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to create expense type',
      error: error.message
    });
  }
});

app.put('/api/expense-types/:id', async (req, res) => {
  try {
    console.log('💰 Update Expense Type API called');

    const { id } = req.params;
    const { name, description, isActive } = req.body;
    const companyId = '8722167d-d2ae-498f-95a3-c18afcf427ae'; // Using actual company ID from database

    // Find expense type
    const expenseType = await pool.query(
      'SELECT id FROM expense_types WHERE id = $1 AND company_id = $2',
      [id, companyId]
    );

    if (expenseType.rows.length === 0) {
      return res.status(404).json({
        status: 'error',
        message: 'Expense type not found'
      });
    }

    // Check for duplicate name if name is being updated
    if (name) {
      const existingType = await pool.query(
        'SELECT id FROM expense_types WHERE name = $1 AND company_id = $2 AND id != $3',
        [name.trim(), companyId, id]
      );

      if (existingType.rows.length > 0) {
        return res.status(409).json({
          status: 'error',
          message: 'Expense type with this name already exists'
        });
      }
    }

    // Build update query dynamically
    const updateFields = [];
    const updateValues = [];
    let paramCount = 0;

    if (name !== undefined) {
      paramCount++;
      updateFields.push(`name = $${paramCount}`);
      updateValues.push(name.trim());
    }

    if (description !== undefined) {
      paramCount++;
      updateFields.push(`description = $${paramCount}`);
      updateValues.push(description);
    }

    if (isActive !== undefined) {
      paramCount++;
      updateFields.push(`is_active = $${paramCount}`);
      updateValues.push(isActive);
    }

    if (updateFields.length === 0) {
      return res.status(400).json({
        status: 'error',
        message: 'No valid fields to update'
      });
    }

    // Add updated_at
    updateFields.push(`updated_at = NOW()`);

    // Add WHERE clause parameters
    paramCount++;
    updateValues.push(id);
    paramCount++;
    updateValues.push(companyId);

    const updateQuery = `
      UPDATE expense_types
      SET ${updateFields.join(', ')}
      WHERE id = $${paramCount - 1} AND company_id = $${paramCount}
      RETURNING id, name, description, is_active as "isActive", created_at as "createdAt", updated_at as "updatedAt"
    `;

    const result = await pool.query(updateQuery, updateValues);
    const updatedExpenseType = result.rows[0];

    res.json({
      status: 'success',
      message: 'Expense type updated successfully',
      data: {
        expenseType: updatedExpenseType
      }
    });
  } catch (error) {
    console.error('❌ Update Expense Type API error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to update expense type',
      error: error.message
    });
  }
});

app.delete('/api/expense-types/:id', async (req, res) => {
  try {
    console.log('💰 Delete Expense Type API called');

    const { id } = req.params;
    const companyId = '8722167d-d2ae-498f-95a3-c18afcf427ae'; // Using actual company ID from database

    // Check if expense type exists
    const expenseType = await pool.query(
      'SELECT id FROM expense_types WHERE id = $1 AND company_id = $2',
      [id, companyId]
    );

    if (expenseType.rows.length === 0) {
      return res.status(404).json({
        status: 'error',
        message: 'Expense type not found'
      });
    }

    // Check if expense type is being used by any expenses
    const expenseCheck = await pool.query(
      'SELECT id FROM expenses WHERE expense_type_id = $1 LIMIT 1',
      [id]
    );

    if (expenseCheck.rows.length > 0) {
      return res.status(409).json({
        status: 'error',
        message: 'Cannot delete expense type that is being used by expenses'
      });
    }

    // Delete the expense type
    await pool.query(
      'DELETE FROM expense_types WHERE id = $1 AND company_id = $2',
      [id, companyId]
    );

    res.json({
      status: 'success',
      message: 'Expense type deleted successfully'
    });
  } catch (error) {
    console.error('❌ Delete Expense Type API error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to delete expense type',
      error: error.message
    });
  }
});

// AMC API endpoint
app.get('/api/amcs', async (req, res) => {
  try {
    console.log('🔧 AMC API called');

    const companyId = '8722167d-d2ae-498f-95a3-c18afcf427ae';
    const limit = req.query.limit || 50;
    const offset = req.query.offset || 0;

    const amcResult = await pool.query(`
      SELECT a.*, c.first_name, c.last_name, c.email, c.phone
      FROM amcs a
      LEFT JOIN customers c ON a.customer_id = c.id
      WHERE a.company_id = $1
      ORDER BY a.created_at DESC
      LIMIT $2 OFFSET $3
    `, [companyId, limit, offset]);

    const totalResult = await pool.query('SELECT COUNT(*) as total FROM amcs WHERE company_id = $1', [companyId]);

    const amcs = amcResult.rows.map(row => ({
      id: row.id,
      amc_code: row.amc_code,
      title: row.title,
      description: row.amc_details,
      start_date: row.start_date,
      end_date: row.end_date,
      contract_value: row.contract_value,
      paid_amount: row.paid_amount,
      balance_amount: row.balance_amount,
      payment_status: row.payment_status,
      status: row.status,
      customer_id: row.customer_id,
      customer_name: `${row.first_name || ''} ${row.last_name || ''}`.trim(),
      customer_email: row.email,
      customer_phone: row.phone,
      created_at: row.created_at,
      updated_at: row.updated_at
    }));

    res.json({
      status: 'success',
      data: amcs,
      pagination: {
        total: parseInt(totalResult.rows[0].total),
        limit: parseInt(limit),
        offset: parseInt(offset),
        pages: Math.ceil(totalResult.rows[0].total / limit)
      }
    });

  } catch (error) {
    console.error('❌ AMC API error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch AMCs',
      error: error.message
    });
  }
});

// RMA API endpoint
app.get('/api/rmas', async (req, res) => {
  try {
    console.log('🔄 RMA API called');

    const companyId = '8722167d-d2ae-498f-95a3-c18afcf427ae';
    const limit = req.query.limit || 50;
    const offset = req.query.offset || 0;

    const rmaResult = await pool.query(`
      SELECT r.*, c.first_name, c.last_name, c.email, c.phone
      FROM rmas r
      LEFT JOIN customers c ON r.customer_id = c.id
      WHERE r.company_id = $1
      ORDER BY r.created_at DESC
      LIMIT $2 OFFSET $3
    `, [companyId, limit, offset]);

    const totalResult = await pool.query('SELECT COUNT(*) as total FROM rmas WHERE company_id = $1', [companyId]);

    const rmas = rmaResult.rows.map(row => ({
      id: row.id,
      rma_number: row.rma_number,
      product_name: row.product_name,
      serial_number: row.serial_number,
      warranty: row.warranty,
      product_condition: row.product_condition,
      problem_description: row.problem_description,
      rma_status: row.rma_status,
      cost: row.cost,
      customer_id: row.customer_id,
      customer_name: `${row.first_name || ''} ${row.last_name || ''}`.trim(),
      customer_email: row.email,
      customer_phone: row.phone,
      created_at: row.created_at,
      updated_at: row.updated_at
    }));

    res.json({
      status: 'success',
      data: rmas,
      pagination: {
        total: parseInt(totalResult.rows[0].total),
        limit: parseInt(limit),
        offset: parseInt(offset),
        pages: Math.ceil(totalResult.rows[0].total / limit)
      }
    });

  } catch (error) {
    console.error('❌ RMA API error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch RMAs',
      error: error.message
    });
  }
});

// Customers API endpoints with limit=all support
app.get('/api/customers', async (req, res) => {
  try {
    console.log('👥 Customers API called with params:', req.query);

    const companyId = '8722167d-d2ae-498f-95a3-c18afcf427ae';
    const { limit = 10, page = 1, search, status, sortBy = 'created_at', sortOrder = 'desc' } = req.query;

    // Handle limit=all case
    const isLimitAll = limit === 'all';
    const actualLimit = isLimitAll ? null : parseInt(limit);
    const offset = isLimitAll ? 0 : (parseInt(page) - 1) * actualLimit;

    // Build dynamic WHERE clause
    let whereClause = 'WHERE company_id = $1';
    let queryParams = [companyId];
    let paramIndex = 2;

    // Add status filter
    if (status) {
      whereClause += ` AND status = $${paramIndex}`;
      queryParams.push(status);
      paramIndex++;
    }

    // Add search filter
    if (search) {
      whereClause += ` AND (
        name ILIKE $${paramIndex} OR
        first_name ILIKE $${paramIndex} OR
        last_name ILIKE $${paramIndex} OR
        email ILIKE $${paramIndex} OR
        phone ILIKE $${paramIndex} OR
        business_name ILIKE $${paramIndex}
      )`;
      queryParams.push(`%${search}%`);
      paramIndex++;
    }

    // Map frontend field names to database field names for sorting
    const sortFieldMapping = {
      'created_at': 'created_at',
      'first_name': 'first_name',
      'contact_number': 'phone',
      'balance_amount': 'current_balance',
      'business_name': 'business_name',
      'email': 'email',
      'status': 'status'
    };

    const dbSortField = sortFieldMapping[sortBy] || 'created_at';
    const dbSortOrder = sortOrder.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';

    // Build the main query
    let mainQuery = `
      SELECT *
      FROM customers
      ${whereClause}
      ORDER BY ${dbSortField} ${dbSortOrder}
    `;

    // Add LIMIT and OFFSET only if not fetching all
    let finalQueryParams = [...queryParams];
    if (!isLimitAll) {
      mainQuery += ` LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
      finalQueryParams.push(actualLimit, offset);
    }

    const customersResult = await pool.query(mainQuery, finalQueryParams);

    // Get total count for pagination (only needed for paginated responses)
    const totalResult = isLimitAll ? null : await pool.query(`SELECT COUNT(*) as total FROM customers ${whereClause}`, queryParams);

    const customers = customersResult.rows.map(row => ({
      id: row.id,
      customer_code: row.customer_code,
      customerCode: row.customer_code, // Alias for compatibility
      name: row.name,
      first_name: row.first_name,
      last_name: row.last_name,
      firstName: row.first_name, // Alias for compatibility
      lastName: row.last_name, // Alias for compatibility
      email: row.email,
      phone: row.phone,
      alternate_phone: row.alternate_phone,
      alternatePhone: row.alternate_phone, // Alias for compatibility
      business_name: row.business_name,
      businessName: row.business_name, // Alias for compatibility
      customer_type: row.customer_type,
      customerType: row.customer_type, // Alias for compatibility
      status: row.status,
      address: row.address,
      city: row.city,
      state: row.state,
      pincode: row.pincode,
      gst_number: row.gst_number,
      gstNumber: row.gst_number, // Alias for compatibility
      pan_number: row.pan_number,
      panNumber: row.pan_number, // Alias for compatibility
      notes: row.notes,
      credit_limit: row.credit_limit,
      creditLimit: row.credit_limit, // Alias for compatibility
      current_balance: row.current_balance,
      currentBalance: row.current_balance, // Alias for compatibility
      balance_amount: row.current_balance, // Alias for compatibility
      created_at: row.created_at,
      updated_at: row.updated_at
    }));

    // Return different response format based on limit parameter
    if (isLimitAll) {
      res.json({
        status: 'success',
        data: customers
      });
    } else {
      const totalCustomers = parseInt(totalResult.rows[0].total);
      const totalPages = Math.ceil(totalCustomers / actualLimit);
      const currentPage = parseInt(page);

      res.json({
        status: 'success',
        data: {
          customers: customers,
          pagination: {
            total: totalCustomers,
            page: currentPage,
            limit: actualLimit,
            totalPages: totalPages,
            hasNextPage: currentPage < totalPages,
            hasPrevPage: currentPage > 1,
            // Additional fields for compatibility
            currentPage: currentPage,
            totalItems: totalCustomers,
            itemsPerPage: actualLimit,
            hasNextPage: currentPage < totalPages,
            hasPrevPage: currentPage > 1
          }
        }
      });
    }

  } catch (error) {
    console.error('❌ Customers API error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch customers',
      error: error.message
    });
  }
});

// Customer import preview endpoint (MUST be before general /api/customers POST route)
app.post('/api/customers/preview', upload.single('file'), async (req, res) => {
  try {
    console.log('👁️ Customer import preview requested');

    if (!req.file) {
      return res.status(400).json({
        status: 'error',
        message: 'No file uploaded'
      });
    }

    // Parse the uploaded file
    const workbook = XLSX.read(req.file.buffer, { type: 'buffer' });
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const data = XLSX.utils.sheet_to_json(worksheet);

    console.log(`📊 Previewing ${data.length} rows from uploaded file`);

    // Validate the data and collect errors
    const errors = [];
    const validData = [];

    data.forEach((row, index) => {
      const rowNumber = index + 2; // +2 because Excel rows start from 1 and we skip header
      const rowErrors = [];

      // Validate required fields
      if (!row['First Name'] || row['First Name'].toString().trim().length < 2) {
        rowErrors.push('First name is required and must be at least 2 characters');
      }

      if (!row['Contact Number'] || !/^[0-9]{10}$/.test(row['Contact Number'].toString().replace(/\D/g, ''))) {
        rowErrors.push('Contact number must be exactly 10 digits');
      }

      // Validate email if provided
      if (row['Email'] && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(row['Email'])) {
        rowErrors.push('Invalid email format');
      }

      // Validate GST number if provided - Allow empty GST numbers
      if (row['GST Number'] && row['GST Number'].toString().trim() !== '') {
        const gstNumber = row['GST Number'].toString().trim().toUpperCase();
        if (!/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/.test(gstNumber)) {
          rowErrors.push('Invalid GST number format. Expected format: 22AAAAA0000A1Z5');
        }
      }

      if (rowErrors.length > 0) {
        errors.push({
          row: rowNumber,
          errors: rowErrors
        });
      } else {
        validData.push(row);
      }
    });

    // Return preview data with validation results
    res.json({
      status: 'success',
      message: 'File parsed successfully',
      data: {
        previewData: data.slice(0, 10), // Return first 10 rows for preview
        totalRows: data.length,
        validRows: validData.length,
        errorRows: errors.length,
        errors: errors.map(error => ({
          row: error.row,
          error: error.errors.join(', ')
        }))
      }
    });

  } catch (error) {
    console.error('❌ Customer import preview error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to preview import file: ' + error.message
    });
  }
});

// Customer import endpoint (MUST be before general /api/customers POST route)
app.post('/api/customers/import', upload.single('file'), async (req, res) => {
  try {
    console.log('📤 Customer import requested');

    if (!req.file) {
      return res.status(400).json({
        status: 'error',
        message: 'No file uploaded'
      });
    }

    const companyId = '8722167d-d2ae-498f-95a3-c18afcf427ae';

    // Parse the uploaded file
    const workbook = XLSX.read(req.file.buffer, { type: 'buffer' });
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const data = XLSX.utils.sheet_to_json(worksheet);

    console.log(`📊 Processing ${data.length} rows from uploaded file`);

    let successCount = 0;
    let failedCount = 0;
    const errors = [];

    // Helper function to map new field names to database fields
    const mapRowFields = (row) => {
      return {
        first_name: row['First Name'] || row.first_name || '',
        last_name: row['Last Name'] || row.last_name || '',
        phone: row['Contact Number'] || row.phone || '',
        alternate_phone: row['Alternative Number'] || row.alternate_phone || '',
        email: row['Email'] || row.email || '',
        opening_balance: row['Opening Balance'] || row.opening_balance || 0,
        birth_date: row['Birth Date'] || row.birth_date || null,
        anniversary_date: row['Anniversary Date'] || row.anniversary_date || null,
        business_name: row['Business Name'] || row.business_name || '',
        gst_number: row['GST Number'] || row.gst_number || '',
        address: row['Address'] || row.address || '',
        pincode: row['Pincode'] || row.pincode || '',
        state: row['State Name'] || row.state || '',
        district_name: row['District Name'] || row.district_name || '',
        city: row['City Name'] || row.city || '',
        customer_category: row['Customer Category'] || row.customer_category || '',
        notes: row['Notes'] || row.notes || '',
        customer_type: row['Customer Type'] || row.customer_type || 'individual',
        credit_limit: row['Credit Limit'] || row.credit_limit || 0
      };
    };

    // Process each row
    for (let i = 0; i < data.length; i++) {
      const originalRow = data[i];
      const row = mapRowFields(originalRow);
      const rowNumber = i + 2; // +2 because Excel rows start at 1 and we have headers

      try {
        // Validate required fields
        if (!row.first_name && !row.last_name && !row.email) {
          throw new Error('At least one of First Name, Last Name, or Email is required');
        }

        // Generate customer code - FIXED: Use BIGINT and ensure uniqueness
        let customerCode;
        let codeAttempts = 0;
        const maxCodeAttempts = 10;

        do {
          const maxCodeResult = await pool.query(`
            SELECT COALESCE(MAX(CAST(SUBSTRING(customer_code FROM 5) AS BIGINT)), 0) as max_code
            FROM customers
            WHERE company_id = $1
            AND customer_code ~ '^CUST[0-9]+$'
            AND LENGTH(SUBSTRING(customer_code FROM 5)) <= 6
            AND CAST(SUBSTRING(customer_code FROM 5) AS BIGINT) <= 999999
          `, [companyId]);

          customerCode = `CUST${String(maxCodeResult.rows[0].max_code + successCount + 1 + codeAttempts).padStart(6, '0')}`;

          // Check if this code already exists
          const existingCodeResult = await pool.query(`
            SELECT customer_code FROM customers WHERE customer_code = $1
          `, [customerCode]);

          if (existingCodeResult.rows.length === 0) {
            break; // Code is unique, use it
          }

          codeAttempts++;

        } while (codeAttempts < maxCodeAttempts);

        if (codeAttempts >= maxCodeAttempts) {
          throw new Error(`Unable to generate unique customer code for import row ${index + 1}`);
        }

        // Handle empty GST number to avoid unique constraint violation
        const processedGstNumber = row.gst_number && row.gst_number.trim() !== '' ? row.gst_number : null;

        // Insert customer with all fields
        await pool.query(`
          INSERT INTO customers (
            id, customer_code, name, first_name, last_name, email, phone, alternate_phone,
            business_name, customer_type, address, city, state, district_name, pincode,
            gst_number, notes, opening_balance, current_balance, credit_limit, birth_date,
            anniversary_date, status, company_id, created_at, updated_at
          ) VALUES (
            gen_random_uuid(), $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14,
            $15, $16, $17, $18, $19, $20, $21, $22, $23, NOW(), NOW()
          )
        `, [
          customerCode,
          `${row.first_name || ''} ${row.last_name || ''}`.trim(),
          row.first_name || '',
          row.last_name || '',
          row.email || '',
          row.phone || '',
          row.alternate_phone || '',
          row.business_name || '',
          row.customer_type || 'individual',
          row.address || '',
          row.city || '',
          row.state || '',
          row.district_name || '',
          row.pincode || '',
          processedGstNumber,
          row.notes || '',
          parseFloat(row.opening_balance) || 0,
          parseFloat(row.opening_balance) || 0, // current_balance = opening_balance initially
          parseFloat(row.credit_limit) || 0,
          row.birth_date || null,
          row.anniversary_date || null,
          'active',
          companyId
        ]);

        successCount++;

      } catch (error) {
        failedCount++;
        errors.push({
          row: rowNumber,
          error: error.message
        });
        console.error(`❌ Error processing row ${rowNumber}:`, error.message);
      }
    }

    console.log(`✅ Import completed: ${successCount} success, ${failedCount} failed`);

    res.json({
      status: 'success',
      message: 'Import completed successfully',
      data: {
        success: successCount,
        failed: failedCount,
        errors: errors
      }
    });

  } catch (error) {
    console.error('❌ Import error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to import customers',
      error: error.message
    });
  }
});

app.post('/api/customers', async (req, res) => {
  try {
    console.log('👥 Create Customer API called:', req.body);
    console.log('🔍 FIXED FIELD MAPPING VERSION - Debug starting...');

    const companyId = '8722167d-d2ae-498f-95a3-c18afcf427ae';

    // Handle both frontend field names (from CustomerForm.jsx) and backend field names
    const {
      // Frontend field names (from CustomerForm.jsx line 272-289)
      name,
      first_name,
      last_name,
      email,
      phone,
      alternate_phone,
      business_name,
      customer_type,
      address,
      city,
      state,
      pincode,
      gst_number,
      notes,
      status,
      opening_balance,
      current_balance
    } = req.body;

    console.log('🔍 EXTRACTED FIELDS:');
    console.log('  name:', name);
    console.log('  first_name:', first_name);
    console.log('  last_name:', last_name);
    console.log('  phone:', phone);
    console.log('  email:', email);

    // Validation: Ensure required fields are present
    if (!first_name || first_name.trim().length === 0) {
      return res.status(400).json({
        status: 'error',
        message: 'First name is required and cannot be empty',
        error: 'firstName field is missing or empty',
        receivedData: req.body
      });
    }

    if (!phone || phone.trim().length === 0) {
      return res.status(400).json({
        status: 'error',
        message: 'Phone number is required and cannot be empty',
        error: 'phone field is missing or empty',
        receivedData: req.body
      });
    }

    // Generate customer code - FIXED: Use BIGINT and ensure uniqueness
    let customerCode;
    let attempts = 0;
    const maxAttempts = 10;

    do {
      const maxCodeResult = await pool.query(`
        SELECT COALESCE(MAX(CAST(SUBSTRING(customer_code FROM 5) AS BIGINT)), 0) as max_code
        FROM customers
        WHERE company_id = $1
        AND customer_code ~ '^CUST[0-9]+$'
        AND LENGTH(SUBSTRING(customer_code FROM 5)) <= 6
        AND CAST(SUBSTRING(customer_code FROM 5) AS BIGINT) <= 999999
      `, [companyId]);

      customerCode = `CUST${String(maxCodeResult.rows[0].max_code + 1 + attempts).padStart(6, '0')}`;

      // Check if this code already exists (including longer codes)
      const existingCodeResult = await pool.query(`
        SELECT customer_code FROM customers WHERE customer_code = $1
      `, [customerCode]);

      if (existingCodeResult.rows.length === 0) {
        break; // Code is unique, use it
      }

      attempts++;
      console.log(`🔄 Customer code ${customerCode} exists, trying next...`);

    } while (attempts < maxAttempts);

    if (attempts >= maxAttempts) {
      throw new Error('Unable to generate unique customer code after multiple attempts');
    }

    console.log('🔢 CUSTOMER CODE GENERATION:');
    console.log('  Generated customer code:', customerCode);
    console.log('  Attempts needed:', attempts + 1);

    // Create the name field by combining firstName and lastName
    const customerName = name || `${first_name.trim()} ${last_name ? last_name.trim() : ''}`.trim();

    console.log('🔍 PROCESSED FIELDS:');
    console.log('  customerCode:', customerCode);
    console.log('  customerName:', customerName);
    console.log('  customerName length:', customerName.length);

    if (!customerName || customerName.trim() === '') {
      return res.status(400).json({
        status: 'error',
        message: 'Name cannot be empty. Please provide firstName.',
        error: 'Generated name is empty',
        receivedData: req.body
      });
    }

    // Handle empty GST number to avoid unique constraint violation
    const processedGstNumber = gst_number && gst_number.trim() !== '' ? gst_number : null;

    const result = await pool.query(`
      INSERT INTO customers (
        id, customer_code, name, first_name, last_name, email, phone, alternate_phone,
        business_name, customer_type, address, city, state, pincode, gst_number,
        notes, status, opening_balance, current_balance, company_id, created_at, updated_at
      ) VALUES (
        gen_random_uuid(), $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, NOW(), NOW()
      ) RETURNING *
    `, [
      customerCode,
      customerName,
      first_name,
      last_name,
      email,
      phone,
      alternate_phone,
      business_name,
      customer_type || 'individual',
      address,
      city,
      state,
      pincode,
      processedGstNumber, // Use processed GST number (null if empty)
      notes,
      status || 'active',
      opening_balance || 0,
      current_balance || opening_balance || 0,
      companyId
    ]);

    const customer = result.rows[0];

    console.log('✅ Customer created successfully:', customer.customer_code);

    res.status(201).json({
      status: 'success',
      message: 'Customer created successfully',
      data: { customer }
    });

  } catch (error) {
    console.error('❌ Create Customer API error:', error);
    console.error('❌ Error details:', {
      message: error.message,
      code: error.code,
      detail: error.detail,
      constraint: error.constraint
    });

    // Handle specific database errors
    if (error.code === '23505') { // Unique constraint violation
      return res.status(409).json({
        status: 'error',
        message: 'Customer with this phone number or GST number already exists',
        error: 'Duplicate entry',
        constraint: error.constraint
      });
    }

    if (error.code === '23502') { // Not null constraint violation
      return res.status(400).json({
        status: 'error',
        message: 'Required field is missing',
        error: 'Missing required field',
        detail: error.detail
      });
    }

    res.status(500).json({
      status: 'error',
      message: 'Failed to create customer',
      error: error.message,
      receivedData: req.body
    });
  }
});

// Customer template download endpoint
app.get('/api/customers/template', (req, res) => {
  try {
    console.log('📄 Customer template download requested');

    // Create a new workbook
    const workbook = XLSX.utils.book_new();

    // Define the template headers - Complete field set matching old project requirements
    const headers = [
      'First Name',
      'Last Name',
      'Contact Number',
      'Alternative Number',
      'Email',
      'Opening Balance',
      'Birth Date',
      'Anniversary Date',
      'Business Name',
      'GST Number',
      'Address',
      'Pincode',
      'State Name',
      'District Name',
      'City Name',
      'Customer Category',
      'Notes',
      'Customer Type',
      'Credit Limit'
    ];

    // Create sample data - Complete field set
    const sampleData = [
      [
        'John',
        'Doe',
        '9876543210',
        '9876543211',
        '<EMAIL>',
        '1000',
        '1990-01-15',
        '2020-06-20',
        'Doe Enterprises',
        '27ABCDE1234F1Z5',
        '123 Main Street, Sector 1',
        '400001',
        'Maharashtra',
        'Mumbai',
        'Mumbai',
        'VIP Customer',
        'Sample customer with all fields',
        'business',
        '50000'
      ],
      [
        'Jane',
        'Smith',
        '9876543212',
        '',
        '<EMAIL>',
        '500',
        '1985-05-10',
        '',
        'Smith Corp',
        '',
        '456 Oak Avenue, Block B',
        '110001',
        'Delhi',
        'Central Delhi',
        'Delhi',
        'Regular Customer',
        'Another sample customer',
        'individual',
        '25000'
      ]
    ];

    // Combine headers with sample data
    const worksheetData = [headers, ...sampleData];

    // Create worksheet
    const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);

    // Set column widths - Updated for new field set
    const columnWidths = [
      { wch: 15 }, // First Name
      { wch: 15 }, // Last Name
      { wch: 15 }, // Contact Number
      { wch: 18 }, // Alternative Number
      { wch: 25 }, // Email
      { wch: 15 }, // Opening Balance
      { wch: 12 }, // Birth Date
      { wch: 15 }, // Anniversary Date
      { wch: 20 }, // Business Name
      { wch: 20 }, // GST Number
      { wch: 35 }, // Address
      { wch: 10 }, // Pincode
      { wch: 15 }, // State Name
      { wch: 15 }, // District Name
      { wch: 15 }, // City Name
      { wch: 18 }, // Customer Category
      { wch: 30 }, // Notes
      { wch: 15 }, // Customer Type
      { wch: 12 }  // Credit Limit
    ];
    worksheet['!cols'] = columnWidths;

    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Customers');

    // Generate buffer
    const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

    // Set response headers
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename=customer_import_template.xlsx');
    res.setHeader('Content-Length', buffer.length);

    // Send the file
    res.send(buffer);

    console.log('✅ Customer template downloaded successfully');

  } catch (error) {
    console.error('❌ Template download error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to generate template',
      error: error.message
    });
  }
});

app.get('/api/customers/:id', async (req, res) => {
  try {
    console.log('👥 Get Customer by ID API called:', req.params.id);

    const companyId = '8722167d-d2ae-498f-95a3-c18afcf427ae';
    const { id } = req.params;

    const result = await pool.query(`
      SELECT * FROM customers
      WHERE id = $1 AND company_id = $2
    `, [id, companyId]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        status: 'error',
        message: 'Customer not found'
      });
    }

    const customer = result.rows[0];

    res.json({
      status: 'success',
      message: 'Customer retrieved successfully',
      data: { customer }
    });

  } catch (error) {
    console.error('❌ Get Customer API error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch customer',
      error: error.message
    });
  }
});

app.put('/api/customers/:id', async (req, res) => {
  try {
    console.log('👥 Update Customer API called:', req.params.id, req.body);
    console.log('🔍 FIXED UPDATE FIELD MAPPING VERSION');

    const companyId = '8722167d-d2ae-498f-95a3-c18afcf427ae';
    const { id } = req.params;

    // Handle both frontend field names and backend field names
    const {
      // Frontend field names (from CustomerForm.jsx)
      name,
      first_name,
      last_name,
      email,
      phone,
      alternate_phone,
      business_name,
      customer_type,
      address,
      city,
      state,
      pincode,
      gst_number,
      notes,
      status,
      opening_balance,
      current_balance,
      // Legacy field names for backward compatibility
      firstName,
      lastName,
      contactNumber,
      alternateNumber,
      companyName,
      category,
      gstNumber
    } = req.body;

    console.log('🔍 UPDATE EXTRACTED FIELDS:');
    console.log('  name:', name);
    console.log('  first_name:', first_name || firstName);
    console.log('  last_name:', last_name || lastName);
    console.log('  phone:', phone || contactNumber);

    // Check if customer exists
    const existingResult = await pool.query(`
      SELECT * FROM customers
      WHERE id = $1 AND company_id = $2
    `, [id, companyId]);

    if (existingResult.rows.length === 0) {
      return res.status(404).json({
        status: 'error',
        message: 'Customer not found'
      });
    }

    // Use frontend field names with fallback to legacy names
    const finalFirstName = first_name || firstName;
    const finalLastName = last_name || lastName;
    const finalPhone = phone || contactNumber;
    const finalAlternatePhone = alternate_phone || alternateNumber;
    const finalBusinessName = business_name || companyName;
    const finalCustomerType = customer_type || category;
    const finalGstNumber = gst_number || gstNumber;

    // Generate name if not provided
    const finalName = name || (finalFirstName ? `${finalFirstName} ${finalLastName || ''}`.trim() : null);

    // Handle empty GST number to avoid unique constraint violation
    const processedGstNumber = finalGstNumber && finalGstNumber.trim() !== '' ? finalGstNumber : null;

    console.log('🔍 UPDATE PROCESSED FIELDS:');
    console.log('  finalName:', finalName);
    console.log('  finalFirstName:', finalFirstName);
    console.log('  finalPhone:', finalPhone);

    // Update customer
    const result = await pool.query(`
      UPDATE customers SET
        name = COALESCE($1, name),
        first_name = COALESCE($2, first_name),
        last_name = COALESCE($3, last_name),
        email = COALESCE($4, email),
        phone = COALESCE($5, phone),
        alternate_phone = COALESCE($6, alternate_phone),
        business_name = COALESCE($7, business_name),
        customer_type = COALESCE($8, customer_type),
        address = COALESCE($9, address),
        city = COALESCE($10, city),
        state = COALESCE($11, state),
        pincode = COALESCE($12, pincode),
        gst_number = COALESCE($13, gst_number),
        notes = COALESCE($14, notes),
        status = COALESCE($15, status),
        opening_balance = COALESCE($16, opening_balance),
        current_balance = COALESCE($17, current_balance),
        updated_at = NOW()
      WHERE id = $18 AND company_id = $19
      RETURNING *
    `, [
      finalName,
      finalFirstName,
      finalLastName,
      email,
      finalPhone,
      finalAlternatePhone,
      finalBusinessName,
      finalCustomerType,
      address,
      city,
      state,
      pincode,
      processedGstNumber, // Use processed GST number (null if empty)
      notes,
      status,
      opening_balance,
      current_balance,
      id,
      companyId
    ]);

    const customer = result.rows[0];

    console.log('✅ Customer updated successfully:', customer.customer_code);

    res.json({
      status: 'success',
      message: 'Customer updated successfully',
      data: { customer }
    });

  } catch (error) {
    console.error('❌ Update Customer API error:', error);
    console.error('❌ Update Error details:', {
      message: error.message,
      code: error.code,
      detail: error.detail,
      constraint: error.constraint
    });

    // Handle specific database errors
    if (error.code === '23505') { // Unique constraint violation
      return res.status(409).json({
        status: 'error',
        message: 'Customer with this phone number or GST number already exists',
        error: 'Duplicate entry',
        constraint: error.constraint
      });
    }

    res.status(500).json({
      status: 'error',
      message: 'Failed to update customer',
      error: error.message,
      receivedData: req.body
    });
  }
});

app.delete('/api/customers/:id', async (req, res) => {
  try {
    console.log('👥 Delete Customer API called (HARD DELETE):', req.params.id);

    const companyId = '8722167d-d2ae-498f-95a3-c18afcf427ae';
    const { id } = req.params;

    // Check if customer exists
    const existingResult = await pool.query(`
      SELECT * FROM customers
      WHERE id = $1 AND company_id = $2
    `, [id, companyId]);

    if (existingResult.rows.length === 0) {
      return res.status(404).json({
        status: 'error',
        message: 'Customer not found'
      });
    }

    const customer = existingResult.rows[0];

    // Check for related records that would prevent deletion
    const relatedChecks = [
      { table: 'services', column: 'customer_id', name: 'Services' },
      { table: 'amcs', column: 'customer_id', name: 'AMCs' },
      { table: 'sales', column: 'customer_id', name: 'Sales' },
      { table: 'quotations', column: 'customer_id', name: 'Quotations' },
      { table: 'sales_payments', column: 'customer_id', name: 'Payments' }
    ];

    const relatedRecords = [];

    for (const check of relatedChecks) {
      try {
        const countResult = await pool.query(`
          SELECT COUNT(*) as count FROM ${check.table}
          WHERE ${check.column} = $1
        `, [id]);

        const count = parseInt(countResult.rows[0].count);
        if (count > 0) {
          relatedRecords.push({
            module: check.name,
            count: count
          });
        }
      } catch (error) {
        // Table might not exist, skip
        console.log(`⚠️  Table ${check.table} not found or error:`, error.message);
      }
    }

    // If there are related records, prevent deletion
    if (relatedRecords.length > 0) {
      return res.status(400).json({
        status: 'error',
        message: 'Cannot delete customer with existing records',
        details: 'This customer has related records that must be deleted first',
        relatedRecords: relatedRecords,
        customerName: customer.name || `${customer.first_name} ${customer.last_name}`.trim()
      });
    }

    // No related records found, proceed with hard delete
    await pool.query(`
      DELETE FROM customers
      WHERE id = $1 AND company_id = $2
    `, [id, companyId]);

    console.log(`✅ Customer ${customer.name} permanently deleted`);

    res.json({
      status: 'success',
      message: 'Customer permanently deleted',
      customerName: customer.name || `${customer.first_name} ${customer.last_name}`.trim()
    });

  } catch (error) {
    console.error('❌ Delete customer error:', error);

    // Check if it's a foreign key constraint error
    if (error.message.includes('foreign key constraint') || error.code === '23503') {
      return res.status(400).json({
        status: 'error',
        message: 'Cannot delete customer due to existing references',
        details: 'This customer is referenced by other records in the system',
        error: 'Foreign key constraint violation'
      });
    }

    res.status(500).json({
      status: 'error',
      message: 'Failed to delete customer',
      error: error.message
    });
  }
});

// Authentication middleware for employee endpoints
const authenticateEmployee = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        status: 'error',
        message: 'No token provided'
      });
    }

    const token = authHeader.substring(7);

    try {
      // Decode the JWT token to get user info
      const payload = JSON.parse(atob(token.split('.')[1]));

      // Get user from database
      const userResult = await pool.query(`
        SELECT u.id, u.name, u.email, u.user_type, u.company_id, u.status,
               c.id as company_id, c.name as company_name
        FROM users u
        LEFT JOIN companies c ON u.company_id = c.id
        WHERE u.id = $1 AND u.status = 'active'
      `, [payload.id]);

      if (userResult.rows.length === 0) {
        return res.status(401).json({
          status: 'error',
          message: 'User not found or inactive'
        });
      }

      req.user = userResult.rows[0];
      next();

    } catch (tokenError) {
      console.error('Token parsing error:', tokenError);
      return res.status(401).json({
        status: 'error',
        message: 'Invalid token'
      });
    }

  } catch (error) {
    console.error('Authentication error:', error);
    return res.status(500).json({
      status: 'error',
      message: 'Authentication failed'
    });
  }
};

// Authorization middleware for employee endpoints
const authorizeEmployee = (actions = ['read']) => {
  return (req, res, next) => {
    // For now, allow all actions in development
    // TODO: Implement proper role-based authorization
    // Based on RBAC: admin, sub_admin, account_manager can manage employees
    next();
  };
};

// Employee API endpoints
app.get('/api/employees/stats', authenticateEmployee, authorizeEmployee(['read']), async (req, res) => {
  try {
    console.log('👥 Employee Stats API called');

    // Get total employees count (excluding super_admin)
    const totalResult = await pool.query(`
      SELECT COUNT(*) as total FROM users
      WHERE user_type != 'super_admin'
    `);

    // Get active employees count
    const activeResult = await pool.query(`
      SELECT COUNT(*) as active FROM users
      WHERE user_type != 'super_admin' AND status = 'active'
    `);

    // Get inactive employees count
    const inactiveResult = await pool.query(`
      SELECT COUNT(*) as inactive FROM users
      WHERE user_type != 'super_admin' AND status IN ('inactive', 'suspended')
    `);

    // Get employees on leave (for now, we'll use 0 as we don't have leave tracking)
    const onLeave = 0;

    // Get unique departments count (based on user_type)
    const departmentResult = await pool.query(`
      SELECT COUNT(DISTINCT user_type) as departments FROM users
      WHERE user_type != 'super_admin'
    `);

    const stats = {
      totalEmployees: parseInt(totalResult.rows[0].total) || 0,
      activeEmployees: parseInt(activeResult.rows[0].active) || 0,
      inactiveEmployees: parseInt(inactiveResult.rows[0].inactive) || 0,
      onLeave: onLeave,
      departments: parseInt(departmentResult.rows[0].departments) || 0
    };

    console.log('✅ Employee stats retrieved:', stats);

    res.json({
      status: 'success',
      data: stats
    });
  } catch (error) {
    console.error('❌ Error fetching employee stats:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch employee statistics',
      error: error.message
    });
  }
});

app.get('/api/employees', authenticateEmployee, authorizeEmployee(['read']), async (req, res) => {
  try {
    console.log('👥 Employees API called');
    const { page = 1, limit = 10, search = '' } = req.query;
    const offset = (page - 1) * limit;

    // Allow all user types to be listed (removed super_admin filter)
    let whereClause = "WHERE 1=1";
    let countParams = [];
    let employeeParams = [];

    if (search && search.trim()) {
      const searchTerm = `%${search.trim()}%`;
      whereClause += ` AND (name ILIKE $1 OR email ILIKE $2)`;
      countParams = [searchTerm, searchTerm];
      employeeParams = [searchTerm, searchTerm, parseInt(limit), parseInt(offset)];
    } else {
      employeeParams = [parseInt(limit), parseInt(offset)];
    }

    // Get total count
    const countResult = await pool.query(`
      SELECT COUNT(*) as total FROM users ${whereClause}
    `, countParams);

    // Get employees with pagination
    const limitOffset = search && search.trim() ? '$3 OFFSET $4' : '$1 OFFSET $2';
    const employeesResult = await pool.query(`
      SELECT id, name, email, user_type, mobile_number, status,
             total_experience, date_of_birth, skills, address, notes,
             created_at, updated_at
      FROM users
      ${whereClause}
      ORDER BY created_at DESC
      LIMIT ${limitOffset}
    `, employeeParams);

    const totalItems = parseInt(countResult.rows[0].total);
    const totalPages = Math.ceil(totalItems / limit);

    console.log(`✅ Retrieved ${employeesResult.rows.length} employees (page ${page}/${totalPages})`);

    res.json({
      status: 'success',
      data: {
        employees: employeesResult.rows || [],
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalItems,
          itemsPerPage: parseInt(limit),
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1
        }
      }
    });
  } catch (error) {
    console.error('❌ Error fetching employees:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch employees',
      error: error.message
    });
  }
});

// Skills endpoint - Must come before /api/employees/:id to avoid route conflict
app.get('/api/employees/skills', authenticateEmployee, authorizeEmployee(['read']), async (req, res) => {
  try {
    console.log('🛠️ Skills endpoint called');
    console.log('Query params:', req.query);

    const { search, category } = req.query;

    // Build where clause
    let whereClause = 'WHERE is_active = true';
    const replacements = [];
    let paramCount = 0;

    // Add company filter if we have a valid company ID
    if (req.user?.company_id) {
      paramCount++;
      whereClause += ` AND company_id = $${paramCount}`;
      replacements.push(req.user.company_id);
    }

    // Add search filter
    if (search && search.trim()) {
      paramCount++;
      whereClause += ` AND name ILIKE $${paramCount}`;
      replacements.push(`%${search.trim()}%`);
    }

    // Add category filter
    if (category && category.trim()) {
      paramCount++;
      whereClause += ` AND category = $${paramCount}`;
      replacements.push(category.trim());
    }

    console.log('📋 Where clause:', whereClause);
    console.log('📋 Replacements:', replacements);

    const result = await pool.query(`
      SELECT id, name, description, category, is_active, company_id, created_at, updated_at
      FROM skills
      ${whereClause}
      ORDER BY name ASC
    `, replacements);

    console.log(`✅ Found ${result.rows.length} skills`);

    res.status(200).json({
      status: 'success',
      message: 'Skills retrieved successfully',
      data: result.rows
    });

  } catch (error) {
    console.error('❌ Skills endpoint error:', error);
    console.error('❌ Error details:', error.message);
    console.error('❌ Error stack:', error.stack);

    res.status(500).json({
      status: 'error',
      message: 'Failed to retrieve skills',
      error: error.message
    });
  }
});

// Create skill endpoint
app.post('/api/employees/skills', authenticateEmployee, authorizeEmployee(['create']), async (req, res) => {
  try {
    console.log('➕ Create skill endpoint called');
    console.log('Body:', req.body);
    console.log('User object:', req.user);

    const { name, description, category = 'technical' } = req.body;

    if (!name) {
      return res.status(400).json({
        status: 'error',
        message: 'Skill name is required'
      });
    }

    // Get company ID
    const companyId = req.user.company_id;
    console.log('Company ID:', companyId);

    // Check if skill already exists
    const existingSkills = await pool.query(`
      SELECT id FROM skills
      WHERE LOWER(name) = LOWER($1) AND company_id = $2
    `, [name, companyId]);

    if (existingSkills.rows.length > 0) {
      return res.status(400).json({
        status: 'error',
        message: 'Skill already exists'
      });
    }

    // Create new skill
    const result = await pool.query(`
      INSERT INTO skills (name, description, category, company_id, is_active, created_at, updated_at)
      VALUES ($1, $2, $3, $4, true, NOW(), NOW())
      RETURNING id, name, description, category, is_active, company_id, created_at, updated_at
    `, [name.toLowerCase(), description, category, companyId]);

    console.log('✅ Skill created:', result.rows[0]);

    res.status(201).json({
      status: 'success',
      message: 'Skill created successfully',
      data: result.rows[0]
    });

  } catch (error) {
    console.error('❌ Create skill error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to create skill',
      error: error.message
    });
  }
});

// Employee Attendance Routes (must come before /api/employees/:id)
app.get('/api/employees/attendance/stats', authenticateEmployee, authorizeEmployee(['read']), async (req, res) => {
  try {
    console.log('📊 Employee Attendance Stats API called');
    const { period = 'month', userId, startDate, endDate } = req.query;
    const targetUserId = userId || req.user.id;

    // Calculate date range based on period
    let dateRangeStart, dateRangeEnd;
    const now = new Date();

    if (startDate && endDate) {
      dateRangeStart = startDate;
      dateRangeEnd = endDate;
    } else {
      switch (period) {
        case 'week':
          dateRangeStart = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
          break;
        case 'month':
          dateRangeStart = new Date(now.getFullYear(), now.getMonth(), 1).toISOString().split('T')[0];
          break;
        case 'year':
          dateRangeStart = new Date(now.getFullYear(), 0, 1).toISOString().split('T')[0];
          break;
        default:
          dateRangeStart = new Date(now.getFullYear(), now.getMonth(), 1).toISOString().split('T')[0];
      }
      dateRangeEnd = now.toISOString().split('T')[0];
    }

    // Get attendance statistics from database
    const statsResult = await pool.query(`
      SELECT
        COUNT(*) as total_days,
        COUNT(CASE WHEN status IN ('present', 'late', 'half_day') THEN 1 END) as present_days,
        COUNT(CASE WHEN status = 'absent' THEN 1 END) as absent_days,
        COUNT(CASE WHEN status = 'late' THEN 1 END) as late_days,
        COUNT(CASE WHEN status = 'on_leave' THEN 1 END) as leave_days,
        COUNT(CASE WHEN status = 'half_day' THEN 1 END) as half_days,
        ROUND(AVG(total_hours), 2) as average_working_hours,
        ROUND(SUM(total_hours), 2) as total_working_hours,
        ROUND(SUM(overtime_hours), 2) as total_overtime_hours,
        ROUND(
          (COUNT(CASE WHEN status IN ('present', 'late', 'half_day') THEN 1 END) * 100.0 /
           NULLIF(COUNT(CASE WHEN status != 'on_leave' THEN 1 END), 0)), 2
        ) as attendance_percentage
      FROM attendances
      WHERE user_id = $1
        AND date >= $2
        AND date <= $3
    `, [targetUserId, dateRangeStart, dateRangeEnd]);

    const stats = statsResult.rows[0];

    res.json({
      status: 'success',
      data: {
        stats: {
          totalDays: parseInt(stats.total_days) || 0,
          presentDays: parseInt(stats.present_days) || 0,
          absentDays: parseInt(stats.absent_days) || 0,
          lateDays: parseInt(stats.late_days) || 0,
          leaveDays: parseInt(stats.leave_days) || 0,
          halfDays: parseInt(stats.half_days) || 0,
          averageWorkingHours: parseFloat(stats.average_working_hours) || 0,
          totalWorkingHours: parseFloat(stats.total_working_hours) || 0,
          totalOvertimeHours: parseFloat(stats.total_overtime_hours) || 0,
          attendancePercentage: parseFloat(stats.attendance_percentage) || 0
        },
        period,
        userId: targetUserId,
        dateRange: {
          startDate: dateRangeStart,
          endDate: dateRangeEnd
        }
      }
    });
  } catch (error) {
    console.error('❌ Attendance stats error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch attendance stats',
      error: error.message
    });
  }
});

app.get('/api/employees/attendance/today', authenticateEmployee, async (req, res) => {
  try {
    console.log('📅 Today Attendance API called for user:', req.user.id);

    const today = new Date().toISOString().split('T')[0];

    // Get today's attendance record from database
    const attendanceResult = await pool.query(`
      SELECT
        id,
        user_id,
        date,
        check_in_time,
        check_out_time,
        break_duration,
        total_hours,
        overtime_hours,
        status,
        location,
        notes,
        is_manual_entry,
        device_info,
        created_at,
        updated_at
      FROM attendances
      WHERE user_id = $1 AND date = $2
    `, [req.user.id, today]);

    let todayAttendance = null;
    if (attendanceResult.rows.length > 0) {
      const record = attendanceResult.rows[0];
      todayAttendance = {
        id: record.id,
        userId: record.user_id,
        date: record.date,
        checkInTime: record.check_in_time,
        checkOutTime: record.check_out_time,
        breakDuration: record.break_duration,
        totalHours: parseFloat(record.total_hours) || 0,
        overtimeHours: parseFloat(record.overtime_hours) || 0,
        status: record.status,
        location: record.location,
        notes: record.notes,
        isManualEntry: record.is_manual_entry,
        deviceInfo: record.device_info,
        createdAt: record.created_at,
        updatedAt: record.updated_at
      };
    }

    res.json({
      status: 'success',
      data: {
        attendance: todayAttendance,
        canCheckIn: !todayAttendance || !todayAttendance.checkInTime,
        canCheckOut: todayAttendance && todayAttendance.checkInTime && !todayAttendance.checkOutTime,
        date: today
      }
    });
  } catch (error) {
    console.error('❌ Today attendance error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch today attendance',
      error: error.message
    });
  }
});

app.get('/api/employees/attendance/analytics', authenticateEmployee, authorizeEmployee(['read']), async (req, res) => {
  try {
    console.log('📈 Employee Attendance Analytics API called');
    const { period = 'month', userId } = req.query;

    // Calculate date range
    const now = new Date();
    let dateRangeStart;

    switch (period) {
      case 'week':
        dateRangeStart = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
        break;
      case 'month':
        dateRangeStart = new Date(now.getFullYear(), now.getMonth(), 1).toISOString().split('T')[0];
        break;
      case 'year':
        dateRangeStart = new Date(now.getFullYear(), 0, 1).toISOString().split('T')[0];
        break;
      default:
        dateRangeStart = new Date(now.getFullYear(), now.getMonth(), 1).toISOString().split('T')[0];
    }

    const dateRangeEnd = now.toISOString().split('T')[0];

    // Get attendance trend data
    const trendQuery = `
      SELECT
        date,
        COUNT(CASE WHEN status IN ('present', 'late', 'half_day') THEN 1 END) as present,
        COUNT(CASE WHEN status = 'absent' THEN 1 END) as absent,
        COUNT(CASE WHEN status = 'on_leave' THEN 1 END) as on_leave
      FROM attendances
      WHERE date >= $1 AND date <= $2
      ${userId ? 'AND user_id = $3' : ''}
      GROUP BY date
      ORDER BY date DESC
      LIMIT 30
    `;

    const trendParams = userId ? [dateRangeStart, dateRangeEnd, userId] : [dateRangeStart, dateRangeEnd];
    const trendResult = await pool.query(trendQuery, trendParams);

    // Get time analysis
    const timeAnalysisQuery = `
      SELECT
        AVG(EXTRACT(HOUR FROM check_in_time) + EXTRACT(MINUTE FROM check_in_time)/60.0) as avg_check_in,
        AVG(EXTRACT(HOUR FROM check_out_time) + EXTRACT(MINUTE FROM check_out_time)/60.0) as avg_check_out,
        COUNT(CASE WHEN status = 'late' THEN 1 END) as late_arrivals,
        COUNT(CASE WHEN check_out_time < (date + INTERVAL '17 hours') THEN 1 END) as early_departures
      FROM attendances
      WHERE date >= $1 AND date <= $2
        AND check_in_time IS NOT NULL
        ${userId ? 'AND user_id = $3' : ''}
    `;

    const timeParams = userId ? [dateRangeStart, dateRangeEnd, userId] : [dateRangeStart, dateRangeEnd];
    const timeResult = await pool.query(timeAnalysisQuery, timeParams);

    const timeData = timeResult.rows[0];
    const avgCheckIn = parseFloat(timeData.avg_check_in) || 9;
    const avgCheckOut = parseFloat(timeData.avg_check_out) || 18;

    const analytics = {
      attendanceTrend: trendResult.rows.map(row => ({
        date: row.date,
        present: parseInt(row.present),
        absent: parseInt(row.absent),
        onLeave: parseInt(row.on_leave)
      })),
      timeAnalysis: {
        averageCheckIn: `${Math.floor(avgCheckIn)}:${Math.round((avgCheckIn % 1) * 60).toString().padStart(2, '0')}`,
        averageCheckOut: `${Math.floor(avgCheckOut)}:${Math.round((avgCheckOut % 1) * 60).toString().padStart(2, '0')}`,
        lateArrivals: parseInt(timeData.late_arrivals) || 0,
        earlyDepartures: parseInt(timeData.early_departures) || 0
      }
    };

    res.json({
      status: 'success',
      data: {
        analytics,
        period,
        userId: userId || req.user.id,
        dateRange: {
          startDate: dateRangeStart,
          endDate: dateRangeEnd
        }
      }
    });
  } catch (error) {
    console.error('❌ Attendance analytics error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch attendance analytics',
      error: error.message
    });
  }
});

app.get('/api/employees/attendance', authenticateEmployee, authorizeEmployee(['read']), async (req, res) => {
  try {
    console.log('📋 Employee Attendance Records API called');
    const { page = 1, limit = 10, date, userId, status, startDate, endDate, search } = req.query;
    const offset = (page - 1) * limit;

    // Build WHERE clause dynamically
    let whereConditions = ['1=1'];
    let queryParams = [];
    let paramIndex = 1;

    // Filter by user ID (if specified, otherwise show all for admins)
    if (userId) {
      whereConditions.push(`a.user_id = $${paramIndex}`);
      queryParams.push(userId);
      paramIndex++;
    }

    // Filter by specific date
    if (date) {
      whereConditions.push(`a.date = $${paramIndex}`);
      queryParams.push(date);
      paramIndex++;
    }

    // Filter by date range
    if (startDate && endDate) {
      whereConditions.push(`a.date >= $${paramIndex} AND a.date <= $${paramIndex + 1}`);
      queryParams.push(startDate, endDate);
      paramIndex += 2;
    }

    // Filter by status
    if (status && status !== 'all') {
      whereConditions.push(`a.status = $${paramIndex}`);
      queryParams.push(status);
      paramIndex++;
    }

    // Search by employee name
    if (search) {
      whereConditions.push(`u.name ILIKE $${paramIndex}`);
      queryParams.push(`%${search}%`);
      paramIndex++;
    }

    const whereClause = whereConditions.join(' AND ');

    // Get total count for pagination
    const countQuery = `
      SELECT COUNT(*) as total
      FROM attendances a
      JOIN users u ON a.user_id = u.id
      WHERE ${whereClause}
    `;

    const countResult = await pool.query(countQuery, queryParams);
    const totalRecords = parseInt(countResult.rows[0].total);

    // Get attendance records with employee details
    const recordsQuery = `
      SELECT
        a.id,
        a.user_id,
        u.name as employee_name,
        a.date,
        a.check_in_time,
        a.check_out_time,
        a.break_duration,
        a.total_hours,
        a.overtime_hours,
        a.status,
        a.location,
        a.notes,
        a.is_manual_entry,
        a.device_info,
        a.created_at,
        a.updated_at
      FROM attendances a
      JOIN users u ON a.user_id = u.id
      WHERE ${whereClause}
      ORDER BY a.date DESC, a.created_at DESC
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;

    queryParams.push(parseInt(limit), offset);
    const recordsResult = await pool.query(recordsQuery, queryParams);

    const attendanceRecords = recordsResult.rows.map(record => ({
      id: record.id,
      userId: record.user_id,
      employeeName: record.employee_name,
      date: record.date,
      checkInTime: record.check_in_time,
      checkOutTime: record.check_out_time,
      breakDuration: record.break_duration,
      totalHours: parseFloat(record.total_hours) || 0,
      overtimeHours: parseFloat(record.overtime_hours) || 0,
      status: record.status,
      location: record.location,
      notes: record.notes,
      isManualEntry: record.is_manual_entry,
      deviceInfo: record.device_info,
      createdAt: record.created_at,
      updatedAt: record.updated_at
    }));

    res.json({
      status: 'success',
      data: {
        attendanceRecords,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: totalRecords,
          totalPages: Math.ceil(totalRecords / limit)
        },
        filters: {
          userId,
          date,
          startDate,
          endDate,
          status,
          search
        }
      }
    });
  } catch (error) {
    console.error('❌ Attendance records error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch attendance records',
      error: error.message
    });
  }
});

app.post('/api/employees/attendance/check-in', authenticateEmployee, async (req, res) => {
  try {
    console.log('⏰ Employee Check-in API called');
    const { location, notes, deviceInfo } = req.body;
    const userId = req.user.id;
    const today = new Date().toISOString().split('T')[0];
    const checkInTime = new Date();

    // Check if already checked in today
    const existingRecord = await pool.query(
      'SELECT id, check_in_time FROM attendances WHERE user_id = $1 AND date = $2',
      [userId, today]
    );

    if (existingRecord.rows.length > 0 && existingRecord.rows[0].check_in_time) {
      return res.status(400).json({
        status: 'error',
        message: 'Already checked in today',
        data: {
          existingCheckIn: existingRecord.rows[0].check_in_time
        }
      });
    }

    let attendanceRecord;

    if (existingRecord.rows.length > 0) {
      // Update existing record with check-in time
      const updateResult = await pool.query(`
        UPDATE attendances
        SET
          check_in_time = $1,
          location = $2,
          notes = $3,
          device_info = $4,
          updated_at = CURRENT_TIMESTAMP
        WHERE user_id = $5 AND date = $6
        RETURNING *
      `, [checkInTime, location || 'Office', notes || 'Check-in', JSON.stringify(deviceInfo), userId, today]);

      attendanceRecord = updateResult.rows[0];
    } else {
      // Create new attendance record
      const insertResult = await pool.query(`
        INSERT INTO attendances (
          user_id, date, check_in_time, location, notes, device_info, company_id
        ) VALUES ($1, $2, $3, $4, $5, $6, $7)
        RETURNING *
      `, [userId, today, checkInTime, location || 'Office', notes || 'Check-in', JSON.stringify(deviceInfo), req.user.company_id]);

      attendanceRecord = insertResult.rows[0];
    }

    res.json({
      status: 'success',
      message: 'Checked in successfully',
      data: {
        id: attendanceRecord.id,
        userId: attendanceRecord.user_id,
        date: attendanceRecord.date,
        checkInTime: attendanceRecord.check_in_time,
        location: attendanceRecord.location,
        status: attendanceRecord.status,
        notes: attendanceRecord.notes
      }
    });
  } catch (error) {
    console.error('❌ Check-in error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to check in',
      error: error.message
    });
  }
});

app.post('/api/employees/attendance/check-out', authenticateEmployee, async (req, res) => {
  try {
    console.log('⏰ Employee Check-out API called');
    const { notes, breakDuration } = req.body;
    const userId = req.user.id;
    const today = new Date().toISOString().split('T')[0];
    const checkOutTime = new Date();

    // Check if there's a check-in record for today
    const existingRecord = await pool.query(
      'SELECT * FROM attendances WHERE user_id = $1 AND date = $2',
      [userId, today]
    );

    if (existingRecord.rows.length === 0 || !existingRecord.rows[0].check_in_time) {
      return res.status(400).json({
        status: 'error',
        message: 'No check-in record found for today. Please check in first.'
      });
    }

    const record = existingRecord.rows[0];

    if (record.check_out_time) {
      return res.status(400).json({
        status: 'error',
        message: 'Already checked out today',
        data: {
          existingCheckOut: record.check_out_time
        }
      });
    }

    // Update record with check-out time
    const updateResult = await pool.query(`
      UPDATE attendances
      SET
        check_out_time = $1,
        break_duration = $2,
        notes = CASE
          WHEN notes IS NULL OR notes = '' THEN $3
          ELSE notes || '; ' || $3
        END,
        updated_at = CURRENT_TIMESTAMP
      WHERE user_id = $4 AND date = $5
      RETURNING *
    `, [
      checkOutTime,
      breakDuration || record.break_duration || 60,
      notes || 'Check-out',
      userId,
      today
    ]);

    const updatedRecord = updateResult.rows[0];

    res.json({
      status: 'success',
      message: 'Checked out successfully',
      data: {
        id: updatedRecord.id,
        userId: updatedRecord.user_id,
        date: updatedRecord.date,
        checkInTime: updatedRecord.check_in_time,
        checkOutTime: updatedRecord.check_out_time,
        totalHours: parseFloat(updatedRecord.total_hours) || 0,
        overtimeHours: parseFloat(updatedRecord.overtime_hours) || 0,
        breakDuration: updatedRecord.break_duration,
        status: updatedRecord.status,
        location: updatedRecord.location,
        notes: updatedRecord.notes
      }
    });
  } catch (error) {
    console.error('❌ Check-out error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to check out',
      error: error.message
    });
  }
});

app.post('/api/employees/attendance/manual', authenticateEmployee, authorizeEmployee(['create']), async (req, res) => {
  try {
    console.log('📝 Manual Attendance Creation API called');
    const { userId, date, checkInTime, checkOutTime, status, notes, location, breakDuration } = req.body;

    // Validate required fields
    if (!userId || !date) {
      return res.status(400).json({
        status: 'error',
        message: 'User ID and date are required'
      });
    }

    // Check if attendance record already exists for this user and date
    const existingRecord = await pool.query(
      'SELECT id FROM attendances WHERE user_id = $1 AND date = $2',
      [userId, date]
    );

    if (existingRecord.rows.length > 0) {
      return res.status(409).json({
        status: 'error',
        message: 'Attendance record already exists for this date'
      });
    }

    // Get user's company_id
    const userResult = await pool.query(
      'SELECT company_id FROM users WHERE id = $1',
      [userId]
    );

    if (userResult.rows.length === 0) {
      return res.status(404).json({
        status: 'error',
        message: 'User not found'
      });
    }

    const companyId = userResult.rows[0].company_id;

    // Create manual attendance record
    const insertResult = await pool.query(`
      INSERT INTO attendances (
        user_id,
        date,
        check_in_time,
        check_out_time,
        status,
        location,
        notes,
        break_duration,
        is_manual_entry,
        entered_by,
        company_id
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
      RETURNING *
    `, [
      userId,
      date,
      checkInTime ? new Date(`${date}T${checkInTime}`) : null,
      checkOutTime ? new Date(`${date}T${checkOutTime}`) : null,
      status || 'present',
      location || 'Manual Entry',
      notes || 'Manual attendance entry',
      breakDuration || 60,
      true,
      req.user.id,
      companyId
    ]);

    const manualAttendance = insertResult.rows[0];

    res.json({
      status: 'success',
      message: 'Manual attendance created successfully',
      data: {
        id: manualAttendance.id,
        userId: manualAttendance.user_id,
        date: manualAttendance.date,
        checkInTime: manualAttendance.check_in_time,
        checkOutTime: manualAttendance.check_out_time,
        totalHours: parseFloat(manualAttendance.total_hours) || 0,
        overtimeHours: parseFloat(manualAttendance.overtime_hours) || 0,
        status: manualAttendance.status,
        location: manualAttendance.location,
        notes: manualAttendance.notes,
        breakDuration: manualAttendance.break_duration,
        isManualEntry: manualAttendance.is_manual_entry,
        enteredBy: manualAttendance.entered_by,
        createdAt: manualAttendance.created_at
      }
    });
  } catch (error) {
    console.error('❌ Manual attendance creation error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to create manual attendance',
      error: error.message
    });
  }
});

app.get('/api/employees/attendance/export', authenticateEmployee, authorizeEmployee(['read']), async (req, res) => {
  try {
    console.log('📊 Attendance Export API called');
    const { format = 'csv', startDate, endDate, userId } = req.query;

    // Mock export functionality
    const exportData = {
      filename: `attendance_export_${new Date().toISOString().split('T')[0]}.${format}`,
      downloadUrl: `/api/downloads/attendance_${Date.now()}.${format}`,
      recordCount: 150,
      dateRange: {
        startDate: startDate || '2024-01-01',
        endDate: endDate || new Date().toISOString().split('T')[0]
      }
    };

    res.json({
      status: 'success',
      message: 'Export prepared successfully',
      data: exportData
    });
  } catch (error) {
    console.error('❌ Attendance export error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to export attendance data',
      error: error.message
    });
  }
});

// Update attendance record
app.put('/api/employees/attendance/:id', authenticateEmployee, authorizeEmployee(['update']), async (req, res) => {
  try {
    console.log('✏️ Update Attendance API called for ID:', req.params.id);
    const { id } = req.params;
    const { checkInTime, checkOutTime, status, location, notes, breakDuration } = req.body;

    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(id)) {
      return res.status(400).json({
        status: 'error',
        message: 'Invalid attendance record ID format'
      });
    }

    // Check if record exists
    const existingRecord = await pool.query(
      'SELECT * FROM attendances WHERE id = $1',
      [id]
    );

    if (existingRecord.rows.length === 0) {
      return res.status(404).json({
        status: 'error',
        message: 'Attendance record not found'
      });
    }

    // Update the record
    const updateResult = await pool.query(`
      UPDATE attendances
      SET
        check_in_time = COALESCE($1, check_in_time),
        check_out_time = COALESCE($2, check_out_time),
        status = COALESCE($3, status),
        location = COALESCE($4, location),
        notes = COALESCE($5, notes),
        break_duration = COALESCE($6, break_duration),
        updated_at = CURRENT_TIMESTAMP
      WHERE id = $7
      RETURNING *
    `, [
      checkInTime ? new Date(checkInTime) : null,
      checkOutTime ? new Date(checkOutTime) : null,
      status,
      location,
      notes,
      breakDuration,
      id
    ]);

    const updatedRecord = updateResult.rows[0];

    res.json({
      status: 'success',
      message: 'Attendance record updated successfully',
      data: {
        id: updatedRecord.id,
        userId: updatedRecord.user_id,
        date: updatedRecord.date,
        checkInTime: updatedRecord.check_in_time,
        checkOutTime: updatedRecord.check_out_time,
        totalHours: parseFloat(updatedRecord.total_hours) || 0,
        overtimeHours: parseFloat(updatedRecord.overtime_hours) || 0,
        status: updatedRecord.status,
        location: updatedRecord.location,
        notes: updatedRecord.notes,
        breakDuration: updatedRecord.break_duration,
        isManualEntry: updatedRecord.is_manual_entry,
        updatedAt: updatedRecord.updated_at
      }
    });
  } catch (error) {
    console.error('❌ Update attendance error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to update attendance record',
      error: error.message
    });
  }
});

// Delete attendance record
app.delete('/api/employees/attendance/:id', authenticateEmployee, authorizeEmployee(['delete']), async (req, res) => {
  try {
    console.log('🗑️ Delete Attendance API called for ID:', req.params.id);
    const { id } = req.params;

    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(id)) {
      return res.status(400).json({
        status: 'error',
        message: 'Invalid attendance record ID format'
      });
    }

    // Check if record exists
    const existingRecord = await pool.query(
      'SELECT id, user_id, date FROM attendances WHERE id = $1',
      [id]
    );

    if (existingRecord.rows.length === 0) {
      return res.status(404).json({
        status: 'error',
        message: 'Attendance record not found'
      });
    }

    // Delete the record
    await pool.query('DELETE FROM attendances WHERE id = $1', [id]);

    res.json({
      status: 'success',
      message: 'Attendance record deleted successfully',
      data: {
        deletedId: id,
        userId: existingRecord.rows[0].user_id,
        date: existingRecord.rows[0].date
      }
    });
  } catch (error) {
    console.error('❌ Delete attendance error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to delete attendance record',
      error: error.message
    });
  }
});

// Get single attendance record
app.get('/api/employees/attendance/:id', authenticateEmployee, authorizeEmployee(['read']), async (req, res) => {
  try {
    console.log('📋 Get Single Attendance API called for ID:', req.params.id);
    const { id } = req.params;

    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(id)) {
      return res.status(400).json({
        status: 'error',
        message: 'Invalid attendance record ID format'
      });
    }

    // Get attendance record with employee details
    const recordResult = await pool.query(`
      SELECT
        a.*,
        u.name as employee_name,
        u.email as employee_email
      FROM attendances a
      JOIN users u ON a.user_id = u.id
      WHERE a.id = $1
    `, [id]);

    if (recordResult.rows.length === 0) {
      return res.status(404).json({
        status: 'error',
        message: 'Attendance record not found'
      });
    }

    const record = recordResult.rows[0];

    res.json({
      status: 'success',
      data: {
        id: record.id,
        userId: record.user_id,
        employeeName: record.employee_name,
        employeeEmail: record.employee_email,
        date: record.date,
        checkInTime: record.check_in_time,
        checkOutTime: record.check_out_time,
        totalHours: parseFloat(record.total_hours) || 0,
        overtimeHours: parseFloat(record.overtime_hours) || 0,
        status: record.status,
        location: record.location,
        notes: record.notes,
        breakDuration: record.break_duration,
        isManualEntry: record.is_manual_entry,
        enteredBy: record.entered_by,
        deviceInfo: record.device_info,
        createdAt: record.created_at,
        updatedAt: record.updated_at
      }
    });
  } catch (error) {
    console.error('❌ Get attendance record error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch attendance record',
      error: error.message
    });
  }
});

app.get('/api/employees/:id', authenticateEmployee, authorizeEmployee(['read']), async (req, res) => {
  try {
    console.log('👤 Get Employee by ID API called:', req.params.id);
    const { id } = req.params;

    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(id)) {
      return res.status(400).json({
        status: 'error',
        message: 'Invalid employee ID format',
        receivedId: id
      });
    }

    const employeeResult = await pool.query(`
      SELECT id, name, email, user_type, mobile_number, status,
             total_experience, date_of_birth, skills, address, notes,
             created_at, updated_at
      FROM users
      WHERE id = $1
    `, [id]);

    if (employeeResult.rows.length === 0) {
      return res.status(404).json({
        status: 'error',
        message: 'Employee not found'
      });
    }

    console.log('✅ Employee retrieved:', employeeResult.rows[0].name);

    res.json({
      status: 'success',
      data: employeeResult.rows[0]
    });
  } catch (error) {
    console.error('❌ Error fetching employee:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch employee',
      error: error.message
    });
  }
});

// Create Employee
app.post('/api/employees', authenticateEmployee, authorizeEmployee(['create']), async (req, res) => {
  try {
    console.log('👤 Create Employee API called');
    console.log('Request body:', req.body);

    // Handle both camelCase and snake_case field names
    const {
      name,
      email,
      password,
      user_type,
      userType,
      mobile_number,
      mobileNumber,
      date_of_birth,
      dateOfBirth,
      skills,
      total_experience,
      totalExperience,
      address,
      notes,
      status = 'active'
    } = req.body;

    // Use the provided field or fallback to alternative naming
    const finalUserType = user_type || userType;
    const finalMobileNumber = mobile_number || mobileNumber;
    const finalDateOfBirth = date_of_birth || dateOfBirth;
    const finalTotalExperience = total_experience || totalExperience;

    // Debug logging
    console.log('Field mapping results:');
    console.log('  name:', name);
    console.log('  email:', email);
    console.log('  user_type:', user_type);
    console.log('  userType:', userType);
    console.log('  finalUserType:', finalUserType);

    // Basic validation
    if (!name || !email || !finalUserType) {
      console.log('❌ Validation failed:', { name: !!name, email: !!email, finalUserType: !!finalUserType });
      return res.status(400).json({
        status: 'error',
        message: 'Name, email, and user type are required'
      });
    }

    // Check if email already exists
    const existingUser = await pool.query(
      'SELECT id FROM users WHERE email = $1',
      [email]
    );

    if (existingUser.rows.length > 0) {
      return res.status(400).json({
        status: 'error',
        message: 'Email already exists'
      });
    }

    // Insert new employee using gen_random_uuid()
    const result = await pool.query(`
      INSERT INTO users (
        id, name, email, password, user_type, mobile_number,
        date_of_birth, skills, total_experience, address, notes, status,
        company_id, created_at, updated_at
      ) VALUES (gen_random_uuid(), $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, NOW(), NOW())
      RETURNING id, name, email, user_type, mobile_number, status,
                total_experience, date_of_birth, skills, address, notes,
                created_at, updated_at
    `, [
      name, email, password || 'defaultpass123', finalUserType, finalMobileNumber,
      finalDateOfBirth, skills, finalTotalExperience || 0, address, notes, status,
      req.user.company_id
    ]);

    console.log('✅ Employee created:', result.rows[0].name);

    res.status(201).json({
      status: 'success',
      message: 'Employee created successfully',
      data: result.rows[0]
    });
  } catch (error) {
    console.error('❌ Error creating employee:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to create employee',
      error: error.message
    });
  }
});

// Update Employee
app.put('/api/employees/:id', authenticateEmployee, authorizeEmployee(['update']), async (req, res) => {
  try {
    console.log('👤 Update Employee API called:', req.params.id);
    console.log('Update body:', req.body);
    const { id } = req.params;
    const {
      name,
      email,
      user_type,
      mobile_number,
      date_of_birth,
      skills,
      total_experience,
      address,
      notes,
      status
    } = req.body;

    // Check if employee exists (allow all user types including super_admin)
    const existingEmployee = await pool.query(
      'SELECT id FROM users WHERE id = $1',
      [id]
    );

    if (existingEmployee.rows.length === 0) {
      return res.status(404).json({
        status: 'error',
        message: 'Employee not found'
      });
    }

    // Build update query dynamically (following working pattern from old project)
    const updateFields = [];
    const replacements = { id };

    const fieldMapping = {
      name: 'name',
      email: 'email',
      user_type: 'user_type',
      userType: 'user_type',
      mobile_number: 'mobile_number',
      mobileNumber: 'mobile_number',
      total_experience: 'total_experience',
      totalExperience: 'total_experience',
      status: 'status',
      date_of_birth: 'date_of_birth',
      dateOfBirth: 'date_of_birth',
      skills: 'skills',
      address: 'address',
      notes: 'notes'
    };

    // Build dynamic update fields (simplified approach)
    console.log('Processing fields:', Object.keys(req.body));
    let paramCount = 0;
    const values = [];

    Object.keys(req.body).forEach(key => {
      console.log(`Checking field: ${key} -> ${fieldMapping[key]}, value:`, req.body[key]);
      if (fieldMapping[key] && req.body[key] !== undefined && req.body[key] !== '') {
        paramCount++;
        updateFields.push(`${fieldMapping[key]} = $${paramCount}`);
        if (key === 'total_experience' || key === 'totalExperience') {
          values.push(parseInt(req.body[key]) || 0);
        } else {
          values.push(req.body[key]?.trim() || null);
        }
        console.log(`Added field: ${fieldMapping[key]} = $${paramCount}`);
      }
    });

    if (updateFields.length === 0) {
      return res.status(400).json({
        status: 'error',
        message: 'No valid fields to update'
      });
    }

    // Add updated_at
    updateFields.push('updated_at = NOW()');

    // Add id parameter
    paramCount++;
    values.push(id);

    const updateQuery = `
      UPDATE users SET ${updateFields.join(', ')}
      WHERE id = $${paramCount} AND user_type != 'super_admin'
      RETURNING id, name, email, user_type, mobile_number, status,
                total_experience, date_of_birth, skills, address, notes,
                created_at, updated_at
    `;

    console.log('Final update query:', updateQuery);
    console.log('Final values:', values);

    const result = await pool.query(updateQuery, values);

    if (result.rows.length === 0) {
      return res.status(404).json({
        status: 'error',
        message: 'Employee not found or cannot be updated'
      });
    }

    console.log('✅ Employee updated:', result.rows[0].name);

    res.json({
      status: 'success',
      message: 'Employee updated successfully',
      data: result.rows[0]
    });
  } catch (error) {
    console.error('❌ Error updating employee:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to update employee',
      error: error.message
    });
  }
});

// Delete Employee
app.delete('/api/employees/:id', authenticateEmployee, authorizeEmployee(['delete']), async (req, res) => {
  try {
    console.log('👤 Delete Employee API called:', req.params.id);
    const { id } = req.params;

    // Check if employee exists (allow all user types including super_admin)
    const existingEmployee = await pool.query(
      'SELECT id, name FROM users WHERE id = $1',
      [id]
    );

    if (existingEmployee.rows.length === 0) {
      return res.status(404).json({
        status: 'error',
        message: 'Employee not found or cannot be deleted'
      });
    }

    // Delete employee (allow all user types including super_admin)
    const result = await pool.query(
      'DELETE FROM users WHERE id = $1 RETURNING name',
      [id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({
        status: 'error',
        message: 'Employee not found or cannot be deleted'
      });
    }

    console.log('✅ Employee deleted:', result.rows[0].name);

    res.json({
      status: 'success',
      message: 'Employee deleted successfully'
    });
  } catch (error) {
    console.error('❌ Error deleting employee:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to delete employee',
      error: error.message
    });
  }
});

// Upload Profile Image
app.post('/api/employees/upload-profile-image', authenticateEmployee, upload.single('image'), async (req, res) => {
  try {
    console.log('📸 Upload Profile Image API called');

    if (!req.file) {
      return res.status(400).json({
        status: 'error',
        message: 'No image file uploaded'
      });
    }

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
    if (!allowedTypes.includes(req.file.mimetype)) {
      return res.status(400).json({
        status: 'error',
        message: 'Invalid file type. Only JPEG, PNG, and GIF images are allowed.'
      });
    }

    // Validate file size (5MB limit)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (req.file.size > maxSize) {
      return res.status(400).json({
        status: 'error',
        message: 'File size too large. Maximum size is 5MB.'
      });
    }

    // For now, we'll just return a success response with a mock URL
    // In a real implementation, you would:
    // 1. Save the file to a storage service (AWS S3, local filesystem, etc.)
    // 2. Generate a unique filename
    // 3. Return the URL where the image can be accessed

    const mockImageUrl = `/uploads/profiles/${Date.now()}_${req.file.originalname}`;

    console.log('✅ Profile image uploaded successfully');
    console.log('File details:', {
      originalName: req.file.originalname,
      mimetype: req.file.mimetype,
      size: req.file.size,
      mockUrl: mockImageUrl
    });

    res.status(200).json({
      status: 'success',
      message: 'Profile image uploaded successfully',
      data: {
        imageUrl: mockImageUrl,
        originalName: req.file.originalname,
        size: req.file.size,
        mimetype: req.file.mimetype
      }
    });

  } catch (error) {
    console.error('❌ Error uploading profile image:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to upload profile image',
      error: error.message
    });
  }
});

// Use proxy for all non-API routes
app.use((req, res, next) => {
  if (req.path.startsWith('/api')) {
    next(); // Let API routes be handled normally
  } else {
    viteProxy(req, res, next); // Proxy everything else to Vite
  }
});

// Start servers
async function startServers() {
  try {
    // Start Vite dev server first
    startViteServer();

    // Wait a moment for Vite to start
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Start Express server
    app.listen(PORT, () => {
      console.log('');
      console.log('🎉 TrackNew Development Server Started Successfully!');
      console.log('');
      console.log('📍 Access your application at: http://localhost:' + PORT);
      console.log('🔧 Vite dev server running on: http://localhost:' + VITE_PORT + ' (internal)');
      console.log('');
      console.log('✨ Features enabled:');
      console.log('  • Hot Module Replacement (HMR)');
      console.log('  • Cache-busting headers');
      console.log('  • Real-time file watching');
      console.log('  • Database connectivity');
      console.log('  • Single-port architecture');
      console.log('');
      console.log('🔄 UI changes will now be immediately visible!');
      console.log('');
    });

  } catch (error) {
    console.error('❌ Failed to start servers:', error);
    process.exit(1);
  }
}

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down servers...');
  if (viteProcess) {
    viteProcess.kill();
  }
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Shutting down servers...');
  if (viteProcess) {
    viteProcess.kill();
  }
  process.exit(0);
});

// Start the servers
startServers();
