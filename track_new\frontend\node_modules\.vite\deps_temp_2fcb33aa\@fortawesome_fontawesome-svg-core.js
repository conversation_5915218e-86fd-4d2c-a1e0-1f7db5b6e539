import {
  api,
  config$1,
  counter,
  dom$1,
  findIconDefinition$1,
  icon,
  layer,
  library$1,
  noAuto$1,
  parse$1,
  text,
  toHtml$1
} from "./chunk-GH2QFOMP.js";
import "./chunk-DC5AMYBS.js";
export {
  api,
  config$1 as config,
  counter,
  dom$1 as dom,
  findIconDefinition$1 as findIconDefinition,
  icon,
  layer,
  library$1 as library,
  noAuto$1 as noAuto,
  parse$1 as parse,
  text,
  toHtml$1 as toHtml
};
//# sourceMappingURL=@fortawesome_fontawesome-svg-core.js.map
