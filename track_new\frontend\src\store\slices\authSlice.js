import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import authService from '../../services/authService';
import { removeToken, setToken, setRefreshToken, getToken, isTokenValid, getTokenPayload } from '../../utils/auth';

// Async thunks with API method references
export const login = createAsyncThunk(
  'auth/login',
  async (credentials, { rejectWithValue }) => {
    try {
      // API Method: authService.login() -> POST /auth/login
      const response = await authService.login(credentials);
      console.log('AuthSlice: Login response received:', response);

      // The response structure is: { status, message, data: { user, token, refreshToken } }
      const { data } = response;
      setToken(data.token);
      if (data.refreshToken) {
        setRefreshToken(data.refreshToken);
      }
      return data; // Return the data object which contains user, token, etc.
    } catch (error) {
      console.error('AuthSlice: Login error:', error);
      return rejectWithValue(error.response?.data?.message || error.message || 'Login failed');
    }
  }
);

export const register = createAsyncThunk(
  'auth/register',
  async (userData, { rejectWithValue }) => {
    try {
      // API Method: authService.register() -> POST /auth/register
      const response = await authService.register(userData);
      setToken(response.data.token);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Registration failed');
    }
  }
);

export const logout = createAsyncThunk(
  'auth/logout',
  async (_, { rejectWithValue }) => {
    try {
      // API Method: authService.logout() -> POST /auth/logout
      await authService.logout();
      removeToken();
      return null;
    } catch (error) {
      removeToken(); // Remove token even if API call fails
      return rejectWithValue(error.response?.data?.message || 'Logout failed');
    }
  }
);

export const checkAuth = createAsyncThunk(
  'auth/checkAuth',
  async (_, { rejectWithValue }) => {
    try {
      // First check if we have a valid token in localStorage
      const token = getToken();
      if (!token) {
        return rejectWithValue('No token found');
      }

      // Check if token is valid (not expired)
      if (!isTokenValid(token)) {
        removeToken();
        return rejectWithValue('Token expired');
      }

      // Try to get fresh user data from API
      try {
        // API Method: authService.getProfile() -> GET /auth/profile
        const response = await authService.getProfile();
        return response.data;
      } catch (apiError) {
        // If API fails, fall back to token data (for offline/mock scenarios)
        const payload = getTokenPayload(token);
        if (payload) {
          return {
            user: {
              id: payload.id,
              email: payload.email,
              userType: payload.userType,
              name: payload.name || 'User',
              status: 'active'
            },
            company: {
              id: '1',
              name: 'TrackNew Demo Company',
              status: 'active'
            }
          };
        }
        throw apiError;
      }
    } catch (error) {
      removeToken();
      return rejectWithValue(error.response?.data?.message || error.message || 'Authentication check failed');
    }
  }
);

export const updateProfile = createAsyncThunk(
  'auth/updateProfile',
  async (profileData, { rejectWithValue }) => {
    try {
      // API Method: authService.updateProfile() -> PUT /auth/profile
      const response = await authService.updateProfile(profileData);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Profile update failed');
    }
  }
);

export const changePassword = createAsyncThunk(
  'auth/changePassword',
  async (passwordData, { rejectWithValue }) => {
    try {
      // API Method: authService.changePassword() -> POST /auth/change-password
      const response = await authService.changePassword(passwordData);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Password change failed');
    }
  }
);

// Initial state - check for existing token to set initial authentication state
const getInitialAuthState = () => {
  const token = getToken();
  const isValidToken = token && isTokenValid(token);

  console.log('🔍 AuthSlice: Initializing with token check:', {
    hasToken: !!token,
    isValidToken,
    token: token ? token.substring(0, 20) + '...' : null
  });

  if (isValidToken) {
    // Try to get user data from token payload
    const payload = getTokenPayload(token);
    if (payload) {
      return {
        user: {
          id: payload.id,
          email: payload.email,
          userType: payload.userType,
          name: payload.name || 'User',
          status: 'active'
        },
        company: {
          id: '1',
          name: 'TrackNew Demo Company',
          status: 'active'
        },
        token,
        isAuthenticated: true,
        isLoading: false,
        error: null,
        lastLoginAt: null,
      };
    }
  }

  // Default unauthenticated state
  return {
    user: null,
    company: null,
    token: null,
    isAuthenticated: false,
    isLoading: false,
    error: null,
    lastLoginAt: null,
  };
};

const initialState = getInitialAuthState();

// Auth slice
const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setUser: (state, action) => {
      state.user = action.payload;
    },
    setCompany: (state, action) => {
      state.company = action.payload;
    },

    resetAuth: () => {
      // Clear all localStorage data when resetting auth
      const keysToRemove = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith('tracknew_')) {
          keysToRemove.push(key);
        }
      }
      keysToRemove.forEach(key => localStorage.removeItem(key));

      // Also clear legacy keys
      localStorage.removeItem('track_new');
      localStorage.removeItem('user');
      localStorage.removeItem('token');

      return initialState;
    },
  },
  extraReducers: (builder) => {
    builder
      // Login
      .addCase(login.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(login.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isAuthenticated = true;
        state.user = action.payload.user;
        state.company = action.payload.company;
        state.token = action.payload.token;
        state.lastLoginAt = new Date().toISOString();
        state.error = null;
      })
      .addCase(login.rejected, (state, action) => {
        state.isLoading = false;
        state.isAuthenticated = false;
        state.user = null;
        state.company = null;
        state.token = null;
        state.error = action.payload;
      })

      // Register
      .addCase(register.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(register.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isAuthenticated = true;
        state.user = action.payload.user;
        state.company = action.payload.company;
        state.token = action.payload.token;
        state.error = null;
      })
      .addCase(register.rejected, (state, action) => {
        state.isLoading = false;
        state.isAuthenticated = false;
        state.user = null;
        state.company = null;
        state.token = null;
        state.error = action.payload;
      })

      // Logout
      .addCase(logout.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(logout.fulfilled, (state) => {
        return initialState;
      })
      .addCase(logout.rejected, (state) => {
        return initialState;
      })

      // Check Auth
      .addCase(checkAuth.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(checkAuth.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isAuthenticated = true;
        state.user = action.payload.user;
        state.company = action.payload.company;
        state.error = null;
      })
      .addCase(checkAuth.rejected, (state, action) => {
        state.isLoading = false;
        state.isAuthenticated = false;
        state.user = null;
        state.company = null;
        state.token = null;
        // Don't set error for "No token found" as it's expected on initial load
        if (action.payload !== 'No token found') {
          state.error = action.payload;
        }
      })

      // Update Profile
      .addCase(updateProfile.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateProfile.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = { ...state.user, ...action.payload.user };
        state.error = null;
      })
      .addCase(updateProfile.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // Change Password
      .addCase(changePassword.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(changePassword.fulfilled, (state) => {
        state.isLoading = false;
        state.error = null;
      })
      .addCase(changePassword.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      });
  },
});

// Actions
export const { clearError, setUser, setCompany, resetAuth } = authSlice.actions;

// Selectors
export const selectAuth = (state) => state.auth;
export const selectUser = (state) => state.auth.user;
export const selectCompany = (state) => state.auth.company;
export const selectIsAuthenticated = (state) => state.auth.isAuthenticated;
export const selectAuthLoading = (state) => state.auth.isLoading;
export const selectAuthError = (state) => state.auth.error;

export default authSlice.reducer;
