if(!self.define){let e,i={};const s=(s,a)=>(s=new URL(s+".js",a).href,i[s]||new Promise((i=>{if("document"in self){const e=document.createElement("script");e.src=s,e.onload=i,document.head.appendChild(e)}else e=s,importScripts(s),i()})).then((()=>{let e=i[s];if(!e)throw new Error(`Module ${s} didn’t register its module`);return e})));self.define=(a,r)=>{const c=e||("document"in self?document.currentScript.src:"")||location.href;if(i[c])return;let n={};const d=e=>s(e,c),t={module:{uri:c},exports:n,require:d};i[c]=Promise.all(a.map((e=>t[e]||d(e)))).then((e=>(r(...e),n)))}}define(["./workbox-6e5f094d"],(function(e){"use strict";self.skipWaiting(),e.clientsClaim(),e.precacheAndRoute([{url:"app-status.html",revision:"236ad22172cb3649c7fce918e2bb317b"},{url:"assets/index-S4mLatTV-1751437224181.css",revision:null},{url:"assets/leadService-DGtUbXkU-1751437224181.js",revision:null},{url:"assets/router-DrGV_cTB-1751437224181.js",revision:null},{url:"assets/ui-CFFfOq2L-1751437224181.js",revision:null},{url:"assets/utils-0ePtVbef-1751437224181.js",revision:null},{url:"assets/vendor-CDaM45aE-1751437224181.js",revision:null},{url:"cache-test.html",revision:"fd510ee462fd53603bb2527e289ac783"},{url:"clear-browser-cache.html",revision:"2804f178b0567e82669338b4b694f50b"},{url:"clear-cache.html",revision:"253c10e3eb97f57e80d99ae1c65dce65"},{url:"clear-cache.js",revision:"f4b9de89524e4c0d5c20ffe2868b6205"},{url:"debug-detector.html",revision:"ad181f7cbcd455820b7c222f79ea50ee"},{url:"diagnostic.html",revision:"f0d35a86d4dddbe0444c455d21ba2e2e"},{url:"image-converter.html",revision:"82ab1e52f1668fb0c07eae98e8dded42"},{url:"images/empty-state.png",revision:"ee354b02ceb77ac70c6b8a1b2e3d7650"},{url:"images/empty-state.svg",revision:"0233d6508e96ef933f1f8967510788f0"},{url:"images/service_page/Add.png",revision:"9ae8c844cfa6f84dd749e23854f31008"},{url:"images/service_page/address.png",revision:"ee549420abade7061df7fe8206ee0863"},{url:"images/service_page/anniversary.png",revision:"e3b344fcaccb463a3867c57a480288cb"},{url:"images/service_page/Calendar.png",revision:"fc31add62892d0f4fca74dfa8a56d4ea"},{url:"images/service_page/cooperation.png",revision:"a6c38e6dbdbc183e532d8d98467b6720"},{url:"images/service_page/Email.png",revision:"eeebd14f2b3ecaea8105bfb94e1ef7c7"},{url:"images/service_page/happy-birthday.png",revision:"34c68882d55c12ef2260df1cf74d821a"},{url:"images/service_page/map.png",revision:"434e605a412a14f9c369db1da133eaf0"},{url:"images/service_page/Money.png",revision:"f2f23e599c90e23668aeae0c60a325e5"},{url:"images/service_page/Phone_call.png",revision:"16897aa5680d1c41528162025be71ef6"},{url:"images/service_page/User.png",revision:"a2fc706de6c4ae6e8cb21595f3988af7"},{url:"images/service_page/Writing.png",revision:"9cfb8e8c61d2736a06f95306636a78e9"},{url:"index.html",revision:"18b0a6859ff0f85eb57138e93f452025"},{url:"monitor-auto-reload.html",revision:"20a619f61c5e562a71f111e8022fabf7"},{url:"pwa-192x192.png",revision:"ee354b02ceb77ac70c6b8a1b2e3d7650"},{url:"pwa-192x192.svg",revision:"d964dd5089ecbbf31803aadfd3e1194b"},{url:"registerSW.js",revision:"1872c500de691dce40960bb85481de07"},{url:"test-api.html",revision:"8cdccc6fe4067ae1029039ad55465455"},{url:"test.html",revision:"0109c48dd882219009f1fae4ee820fa7"},{url:"update-token.html",revision:"86ce29f32f28096f996b206944d72f8d"},{url:"vite.svg",revision:"458f60d72df7981af95b88885e66d2d5"},{url:"pwa-192x192.png",revision:"ee354b02ceb77ac70c6b8a1b2e3d7650"},{url:"manifest.webmanifest",revision:"1d8fe022baeb71c079f824f820d200f1"}],{}),e.cleanupOutdatedCaches(),e.registerRoute(new e.NavigationRoute(e.createHandlerBoundToURL("index.html"))),e.registerRoute(/^https:\/\/api\.tracknew\.com\/.*/i,new e.NetworkFirst({cacheName:"api-cache",plugins:[new e.ExpirationPlugin({maxEntries:10,maxAgeSeconds:604800}),new e.CacheableResponsePlugin({statuses:[0,200]})]}),"GET"),e.registerRoute(/\.(?:js|css)$/,new e.StaleWhileRevalidate({cacheName:"static-resources",plugins:[new e.ExpirationPlugin({maxEntries:60,maxAgeSeconds:86400})]}),"GET"),e.registerRoute((({request:e})=>"navigate"===e.mode),new e.NetworkFirst({cacheName:"pages",plugins:[new e.ExpirationPlugin({maxEntries:50,maxAgeSeconds:86400})]}),"GET")}));
//# sourceMappingURL=sw.js.map
