import React from 'react';
import {
  PencilIcon,
  TrashIcon,
  EyeIcon,
  Squares2X2Icon,
  WrenchScrewdriverIcon
} from '@heroicons/react/24/outline';
import { clsx } from 'clsx';
import '../../styles/design-system.css';
import './ServiceCategories.css';

/**
 * Service Category Card Component
 * 
 * Displays a service category in card format with actions
 */
const ServiceCategoryCard = ({ category, onEdit, onView, onDelete }) => {
  const handleEdit = (e) => {
    e.stopPropagation();
    onEdit(category);
  };

  const handleView = (e) => {
    e.stopPropagation();
    onView(category);
  };

  const handleDelete = (e) => {
    e.stopPropagation();
    onDelete(category);
  };

  return (
    <div
      className="category-card cursor-pointer"
      onClick={handleView}
    >
      {/* Card Header - Compact */}
      <div className="category-card-header">
        <div className="flex items-center space-x-2 flex-1 min-w-0">
          <div className="w-6 h-6 bg-white bg-opacity-20 rounded-full flex items-center justify-center flex-shrink-0">
            <WrenchScrewdriverIcon className="w-3 h-3 text-white" />
          </div>
          <div className="min-w-0 flex-1">
            <h3 className="category-card-title">
              {category.serviceCategory}
            </h3>
          </div>
        </div>
        <span className={clsx(
          'status-badge',
          category.serviceStatus === 1 ? 'active' : 'inactive'
        )}>
          {category.serviceStatus === 1 ? 'Active' : 'Inactive'}
        </span>
      </div>

      {/* Card Body - Compact */}
      <div className="category-card-body">
        <div className="category-card-description">
          {category.description || `${category.servicesCount || 0} Services • Order: ${category.sortOrder || 0}`}
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2 text-xs">
            {category.form && Object.keys(category.form).length > 0 && (
              <span className="text-teal-600 font-medium">Dynamic Form</span>
            )}
            {category.isDefault && (
              <span className="text-orange-600 font-medium">⭐ Default</span>
            )}
          </div>

          <div className="category-action-buttons">
            <button
              onClick={handleView}
              className="category-action-btn view"
              title="View Details"
            >
              <EyeIcon className="w-3 h-3" />
            </button>
            <button
              onClick={handleEdit}
              className="category-action-btn edit"
              title="Edit Category"
            >
              <PencilIcon className="w-3 h-3" />
            </button>
            <button
              onClick={handleDelete}
              disabled={category.servicesCount > 0}
              className={clsx(
                "category-action-btn",
                category.servicesCount > 0
                  ? "opacity-50 cursor-not-allowed"
                  : "delete"
              )}
              title={category.servicesCount > 0 ? "Cannot delete - has services" : "Delete Category"}
            >
              <TrashIcon className="w-3 h-3" />
            </button>
          </div>
        </div>
      </div>

    </div>
  );
};

export default ServiceCategoryCard;
