/**
 * CategoryStats Component
 * Displays Service Categories and Customer Categories statistics at the top of the page
 */

import React, { useState, useEffect } from 'react';
import { 
  TagIcon, 
  UserGroupIcon, 
  CheckCircleIcon, 
  XCircleIcon,
  ChartBarIcon,
  CubeIcon
} from '@heroicons/react/24/outline';
import axios from 'axios';

const CategoryStats = ({ activeTab }) => {
  const [serviceStats, setServiceStats] = useState({
    totalCategories: 0,
    activeCategories: 0,
    inactiveCategories: 0
  });
  
  const [customerStats, setCustomerStats] = useState({
    totalCategories: 0,
    activeCategories: 0,
    inactiveCategories: 0
  });
  
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch Service Categories Stats
  const fetchServiceStats = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get('/api/service_categories/stats', {
        headers: { Authorization: `Bear<PERSON> ${token}` }
      });
      
      if (response.data.status === 'success') {
        setServiceStats(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching service category stats:', error);
      setError('Failed to load service category statistics');
    }
  };

  // Fetch Customer Categories Stats
  const fetchCustomerStats = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get('/api/customer-categories/stats', {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      if (response.data.status === 'success') {
        setCustomerStats(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching customer category stats:', error);
      setError('Failed to load customer category statistics');
    }
  };

  useEffect(() => {
    const loadStats = async () => {
      setLoading(true);
      setError(null);
      
      try {
        await Promise.all([fetchServiceStats(), fetchCustomerStats()]);
      } catch (error) {
        setError('Failed to load category statistics');
      } finally {
        setLoading(false);
      }
    };

    loadStats();
  }, []);

  // Service Categories Stats Cards
  const serviceStatsCards = [
    {
      title: 'Total Service Categories',
      value: serviceStats.totalCategories,
      icon: TagIcon,
      color: 'bg-teal-500',
      textColor: 'text-teal-600',
      bgColor: 'bg-teal-50',
      change: `${serviceStats.activeCategories} active`,
      changeType: 'positive'
    },
    {
      title: 'Active Categories',
      value: serviceStats.activeCategories,
      icon: CheckCircleIcon,
      color: 'bg-green-500',
      textColor: 'text-green-600',
      bgColor: 'bg-green-50',
      change: `${Math.round(((serviceStats.activeCategories || 0) / (serviceStats.totalCategories || 1)) * 100)}% of total`,
      changeType: 'neutral'
    },
    {
      title: 'Inactive Categories',
      value: serviceStats.inactiveCategories,
      icon: XCircleIcon,
      color: 'bg-orange-500',
      textColor: 'text-orange-600',
      bgColor: 'bg-orange-50',
      change: `${Math.round(((serviceStats.inactiveCategories || 0) / (serviceStats.totalCategories || 1)) * 100)}% of total`,
      changeType: 'neutral'
    }
  ];

  // Customer Categories Stats Cards
  const customerStatsCards = [
    {
      title: 'Total Customer Categories',
      value: customerStats.totalCategories,
      icon: UserGroupIcon,
      color: 'bg-teal-500',
      textColor: 'text-teal-600',
      bgColor: 'bg-teal-50',
      change: `${customerStats.activeCategories} active`,
      changeType: 'positive'
    },
    {
      title: 'Active Categories',
      value: customerStats.activeCategories,
      icon: CheckCircleIcon,
      color: 'bg-green-500',
      textColor: 'text-green-600',
      bgColor: 'bg-green-50',
      change: `${Math.round(((customerStats.activeCategories || 0) / (customerStats.totalCategories || 1)) * 100)}% of total`,
      changeType: 'neutral'
    },
    {
      title: 'Inactive Categories',
      value: customerStats.inactiveCategories,
      icon: XCircleIcon,
      color: 'bg-orange-500',
      textColor: 'text-orange-600',
      bgColor: 'bg-orange-50',
      change: `${Math.round(((customerStats.inactiveCategories || 0) / (customerStats.totalCategories || 1)) * 100)}% of total`,
      changeType: 'neutral'
    }
  ];

  // Loading state
  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        {[1, 2, 3].map((i) => (
          <div key={i} className="bg-white rounded-lg shadow animate-pulse">
            <div className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0 bg-gray-200 rounded-md p-3 w-12 h-12"></div>
                <div className="ml-4 flex-1">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-6 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
              <div className="mt-4">
                <div className="h-3 bg-gray-200 rounded w-2/3"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
        <div className="flex">
          <XCircleIcon className="h-5 w-5 text-red-400" />
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">Error Loading Statistics</h3>
            <p className="text-sm text-red-700 mt-1">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  // Choose which stats to display based on active tab
  const currentStats = activeTab === 'service-categories' ? serviceStatsCards : customerStatsCards;

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
      {currentStats.map((card, index) => (
        <div key={index} className="bg-white rounded-lg shadow hover:shadow-md transition-shadow duration-200">
          <div className="p-6">
            <div className="flex items-center">
              <div className={`flex-shrink-0 ${card.bgColor} rounded-md p-3`}>
                <card.icon className={`h-6 w-6 ${card.textColor}`} />
              </div>
              <div className="ml-4 flex-1">
                <p className="text-sm font-medium text-gray-500 truncate">{card.title}</p>
                <p className="text-2xl font-semibold text-gray-900">{card.value}</p>
              </div>
            </div>
            <div className="mt-4">
              <p className={`text-sm ${
                card.changeType === 'positive' ? 'text-green-600' :
                card.changeType === 'negative' ? 'text-red-600' :
                'text-gray-600'
              }`}>
                {card.change}
              </p>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default CategoryStats;
