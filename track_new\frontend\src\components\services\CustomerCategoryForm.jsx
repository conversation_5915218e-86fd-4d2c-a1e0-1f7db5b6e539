import React, { useState, useEffect } from 'react';
import { UserGroupIcon, ExclamationCircleIcon, CheckCircleIcon } from '@heroicons/react/24/outline';
import LoadingSpinner from '../common/LoadingSpinner';
import { usePermissions } from '../../hooks/usePermissions';
import '../../styles/design-system.css';

/**
 * Customer Category Form Component
 *
 * Advanced form for creating and editing customer categories with role-based customization
 */
const CustomerCategoryForm = ({ customerCategory, onSuccess, onCancel }) => {
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [touched, setTouched] = useState({});
  const [isValidating, setIsValidating] = useState(false);
  const [validationStatus, setValidationStatus] = useState({});

  // Role-based permissions
  const { canAccess, userRole } = usePermissions();
  const canManageAdvanced = canAccess('categories', 'manage') || ['admin', 'sub_admin'].includes(userRole);
  const canSetDiscounts = canAccess('pricing', 'manage') || ['admin', 'sub_admin', 'account_manager'].includes(userRole);

  // Form state
  const [formData, setFormData] = useState({
    categoryName: '',
    description: '',
    color: '#3B82F6',
    isActive: true,
    sortOrder: 0,
    discountPercentage: 0,
    specialTerms: ''
  });

  // Initialize form data
  useEffect(() => {
    if (customerCategory) {
      setFormData({
        categoryName: customerCategory.categoryName || '',
        description: customerCategory.description || '',
        color: customerCategory.color || '#3B82F6',
        isActive: customerCategory.isActive !== undefined ? customerCategory.isActive : true,
        sortOrder: customerCategory.sortOrder || 0,
        discountPercentage: customerCategory.discountPercentage || 0,
        specialTerms: customerCategory.specialTerms || ''
      });
    }
  }, [customerCategory]);

  // Advanced validation rules
  const validationRules = {
    categoryName: {
      required: true,
      minLength: 2,
      maxLength: 100,
      pattern: /^[a-zA-Z0-9\s\-_&()]+$/,
      message: 'Category name must be 2-100 characters and contain only letters, numbers, spaces, and basic symbols'
    },
    description: {
      maxLength: 500,
      message: 'Description must not exceed 500 characters'
    },
    discountPercentage: {
      min: 0,
      max: 100,
      message: 'Discount percentage must be between 0 and 100'
    },
    sortOrder: {
      min: 0,
      message: 'Sort order must be a non-negative number'
    },
    color: {
      pattern: /^#[0-9A-Fa-f]{6}$/,
      message: 'Color must be a valid hex color code'
    }
  };

  // Real-time field validation
  const validateField = async (name, value) => {
    const rules = validationRules[name];
    if (!rules) return null;

    // Required validation
    if (rules.required && (!value || value.toString().trim() === '')) {
      return `${name.charAt(0).toUpperCase() + name.slice(1)} is required`;
    }

    // Length validations
    if (rules.minLength && value.length < rules.minLength) {
      return `Minimum ${rules.minLength} characters required`;
    }
    if (rules.maxLength && value.length > rules.maxLength) {
      return `Maximum ${rules.maxLength} characters allowed`;
    }

    // Number validations
    if (rules.min !== undefined && parseFloat(value) < rules.min) {
      return `Minimum value is ${rules.min}`;
    }
    if (rules.max !== undefined && parseFloat(value) > rules.max) {
      return `Maximum value is ${rules.max}`;
    }

    // Pattern validation
    if (rules.pattern && !rules.pattern.test(value)) {
      return rules.message;
    }

    // Async validation for category name uniqueness
    if (name === 'categoryName' && value.trim()) {
      try {
        const response = await fetch(`/api/customer-categories/check-name?name=${encodeURIComponent(value.trim())}&excludeId=${customerCategory?.id || ''}`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('tracknew_token')}`
          }
        });
        const data = await response.json();
        if (!data.available) {
          return 'Category name already exists';
        }
      } catch (error) {
        console.warn('Name validation failed:', error);
      }
    }

    return null;
  };

  const handleInputChange = async (e) => {
    const { name, value, type, checked } = e.target;
    const newValue = type === 'checkbox' ? checked :
                     type === 'number' ? parseFloat(value) || 0 : value;

    setFormData(prev => ({
      ...prev,
      [name]: newValue
    }));

    // Mark field as touched
    setTouched(prev => ({ ...prev, [name]: true }));

    // Clear previous error
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }

    // Real-time validation with debounce
    if (touched[name] || name === 'categoryName') {
      setIsValidating(true);
      setValidationStatus(prev => ({ ...prev, [name]: 'validating' }));

      setTimeout(async () => {
        const error = await validateField(name, newValue);
        setErrors(prev => ({ ...prev, [name]: error }));
        setValidationStatus(prev => ({
          ...prev,
          [name]: error ? 'error' : 'success'
        }));
        setIsValidating(false);
      }, 300);
    }
  };

  const handleBlur = async (e) => {
    const { name, value } = e.target;
    setTouched(prev => ({ ...prev, [name]: true }));

    const error = await validateField(name, value);
    setErrors(prev => ({ ...prev, [name]: error }));
    setValidationStatus(prev => ({
      ...prev,
      [name]: error ? 'error' : 'success'
    }));
  };

  const validateForm = async () => {
    const newErrors = {};
    const newValidationStatus = {};

    for (const [name, value] of Object.entries(formData)) {
      const error = await validateField(name, value);
      if (error) {
        newErrors[name] = error;
        newValidationStatus[name] = 'error';
      } else {
        newValidationStatus[name] = 'success';
      }
    }

    setErrors(newErrors);
    setValidationStatus(newValidationStatus);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Comprehensive form validation
    const isValid = await validateForm();
    if (!isValid) {
      // Focus on first error field
      const firstErrorField = Object.keys(errors)[0];
      if (firstErrorField) {
        document.querySelector(`[name="${firstErrorField}"]`)?.focus();
      }
      return;
    }

    setLoading(true);
    setErrors({});

    try {
      // Prepare submission data with role-based filtering
      const submitData = { ...formData };

      // Remove fields user doesn't have permission to modify
      if (!canSetDiscounts) {
        delete submitData.discountPercentage;
      }
      if (!canManageAdvanced) {
        delete submitData.sortOrder;
        delete submitData.specialTerms;
      }

      const url = customerCategory
        ? `/api/customer-categories/${customerCategory.id}`
        : '/api/customer-categories';
      const method = customerCategory ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('tracknew_token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(submitData)
      });

      const data = await response.json();

      if (!response.ok) {
        if (response.status === 400 && data.errors) {
          // Handle validation errors from backend
          const backendErrors = {};
          data.errors.forEach(error => {
            backendErrors[error.field] = error.message;
          });
          setErrors(backendErrors);
          return;
        }
        throw new Error(data.message || 'Failed to save customer category');
      }

      if (data.success) {
        // Show success feedback
        setValidationStatus(prev => {
          const newStatus = {};
          Object.keys(prev).forEach(key => {
            newStatus[key] = 'success';
          });
          return newStatus;
        });

        // Call success callback after brief delay to show success state
        setTimeout(() => {
          onSuccess(data.data);
        }, 500);
      } else {
        throw new Error(data.message || 'Failed to save customer category');
      }
    } catch (error) {
      console.error('Error saving customer category:', error);
      setErrors({ submit: error.message });
    } finally {
      setLoading(false);
    }
  };

  // Color presets for easy selection
  const colorPresets = [
    '#3B82F6', '#EF4444', '#10B981', '#F59E0B',
    '#8B5CF6', '#EC4899', '#06B6D4', '#84CC16',
    '#F97316', '#6366F1', '#14B8A6', '#F43F5E'
  ];

  // Field validation status icon
  const getValidationIcon = (fieldName) => {
    const status = validationStatus[fieldName];
    if (status === 'validating') {
      return <div className="animate-spin w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full" />;
    }
    if (status === 'error') {
      return <ExclamationCircleIcon className="w-4 h-4 text-red-500" />;
    }
    if (status === 'success' && touched[fieldName]) {
      return <CheckCircleIcon className="w-4 h-4 text-green-500" />;
    }
    return null;
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Category Name */}
      <div>
        <label htmlFor="categoryName" className="block text-sm font-medium text-gray-700">
          Category Name *
        </label>
        <div className="relative mt-1">
          <input
            type="text"
            id="categoryName"
            name="categoryName"
            value={formData.categoryName}
            onChange={handleInputChange}
            onBlur={handleBlur}
            className={`block w-full border rounded-md shadow-sm py-2 px-3 pr-10 focus:outline-none focus:ring-2 ${
              errors.categoryName
                ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                : validationStatus.categoryName === 'success'
                  ? 'border-green-300 focus:ring-green-500 focus:border-green-500'
                  : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
            }`}
            placeholder="Enter category name"
            required
          />
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
            {getValidationIcon('categoryName')}
          </div>
        </div>
        {errors.categoryName && (
          <p className="mt-1 text-sm text-red-600 flex items-center">
            <ExclamationCircleIcon className="w-4 h-4 mr-1" />
            {errors.categoryName}
          </p>
        )}
      </div>

      {/* Description */}
      <div>
        <label htmlFor="description" className="block text-sm font-medium text-gray-700">
          Description
        </label>
        <div className="relative mt-1">
          <textarea
            id="description"
            name="description"
            rows={3}
            value={formData.description}
            onChange={handleInputChange}
            onBlur={handleBlur}
            className={`block w-full border rounded-md shadow-sm py-2 px-3 pr-10 focus:outline-none focus:ring-2 ${
              errors.description
                ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                : validationStatus.description === 'success'
                  ? 'border-green-300 focus:ring-green-500 focus:border-green-500'
                  : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
            }`}
            placeholder="Enter category description"
            maxLength={500}
          />
          <div className="absolute top-2 right-2">
            {getValidationIcon('description')}
          </div>
        </div>
        <div className="mt-1 flex justify-between text-xs text-gray-500">
          <span>{errors.description && <span className="text-red-600">{errors.description}</span>}</span>
          <span>{formData.description.length}/500</span>
        </div>
      </div>

      {/* Color and Status */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label htmlFor="color" className="block text-sm font-medium text-gray-700">
            Color Theme
          </label>
          <div className="mt-1 space-y-3">
            <div className="flex items-center space-x-3">
              <input
                type="color"
                id="color"
                name="color"
                value={formData.color}
                onChange={handleInputChange}
                onBlur={handleBlur}
                className="h-10 w-20 border border-gray-300 rounded-md cursor-pointer"
              />
              <div className="relative flex-1">
                <input
                  type="text"
                  value={formData.color}
                  onChange={(e) => {
                    const newColor = e.target.value;
                    setFormData(prev => ({ ...prev, color: newColor }));
                    handleInputChange({ target: { name: 'color', value: newColor } });
                  }}
                  onBlur={handleBlur}
                  className={`w-full border rounded-md shadow-sm py-2 px-3 pr-10 focus:outline-none focus:ring-2 ${
                    errors.color
                      ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                      : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
                  }`}
                  placeholder="#3B82F6"
                  pattern="^#[0-9A-Fa-f]{6}$"
                />
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                  {getValidationIcon('color')}
                </div>
              </div>
            </div>

            {/* Color Presets */}
            <div className="flex flex-wrap gap-2">
              {colorPresets.map((preset) => (
                <button
                  key={preset}
                  type="button"
                  onClick={() => {
                    setFormData(prev => ({ ...prev, color: preset }));
                    handleInputChange({ target: { name: 'color', value: preset } });
                  }}
                  className={`w-8 h-8 rounded-full border-2 hover:scale-110 transition-transform ${
                    formData.color === preset ? 'border-gray-800' : 'border-gray-300'
                  }`}
                  style={{ backgroundColor: preset }}
                  title={preset}
                />
              ))}
            </div>
            {errors.color && (
              <p className="text-sm text-red-600 flex items-center">
                <ExclamationCircleIcon className="w-4 h-4 mr-1" />
                {errors.color}
              </p>
            )}
          </div>
        </div>

        {canManageAdvanced && (
          <div>
            <label htmlFor="sortOrder" className="block text-sm font-medium text-gray-700">
              Sort Order
            </label>
            <div className="relative mt-1">
              <input
                type="number"
                id="sortOrder"
                name="sortOrder"
                value={formData.sortOrder}
                onChange={handleInputChange}
                onBlur={handleBlur}
                min="0"
                className={`block w-full border rounded-md shadow-sm py-2 px-3 pr-10 focus:outline-none focus:ring-2 ${
                  errors.sortOrder
                    ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                    : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
                }`}
                placeholder="0"
              />
              <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                {getValidationIcon('sortOrder')}
              </div>
            </div>
            {errors.sortOrder && (
              <p className="mt-1 text-sm text-red-600 flex items-center">
                <ExclamationCircleIcon className="w-4 h-4 mr-1" />
                {errors.sortOrder}
              </p>
            )}
          </div>
        )}
      </div>

      {/* Discount and Status */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {canSetDiscounts && (
          <div>
            <label htmlFor="discountPercentage" className="block text-sm font-medium text-gray-700">
              Discount Percentage (%)
              <span className="text-xs text-gray-500 ml-1">• Admin/Manager only</span>
            </label>
            <div className="relative mt-1">
              <input
                type="number"
                id="discountPercentage"
                name="discountPercentage"
                value={formData.discountPercentage}
                onChange={handleInputChange}
                onBlur={handleBlur}
                min="0"
                max="100"
                step="0.01"
                className={`block w-full border rounded-md shadow-sm py-2 px-3 pr-10 focus:outline-none focus:ring-2 ${
                  errors.discountPercentage
                    ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                    : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
                }`}
                placeholder="0.00"
              />
              <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                {getValidationIcon('discountPercentage')}
              </div>
            </div>
            {errors.discountPercentage && (
              <p className="mt-1 text-sm text-red-600 flex items-center">
                <ExclamationCircleIcon className="w-4 h-4 mr-1" />
                {errors.discountPercentage}
              </p>
            )}
            <p className="mt-1 text-xs text-gray-500">
              Default discount for customers in this category
            </p>
          </div>
        )}

        <div className="flex flex-col justify-center">
          <div className="flex items-center space-x-4">
            <div className="flex items-center">
              <input
                id="isActive"
                name="isActive"
                type="checkbox"
                checked={formData.isActive}
                onChange={handleInputChange}
                className="h-4 w-4 text-teal-600 focus:ring-teal-500 border-gray-300 rounded"
              />
              <label htmlFor="isActive" className="ml-2 block text-sm font-medium text-gray-900">
                Active Category
              </label>
            </div>
          </div>
          <p className="mt-1 text-xs text-gray-500">
            Inactive categories won't appear in customer selection
          </p>
        </div>
      </div>

      {/* Special Terms */}
      {canManageAdvanced && (
        <div>
          <label htmlFor="specialTerms" className="block text-sm font-medium text-gray-700">
            Special Terms & Conditions
            <span className="text-xs text-gray-500 ml-1">• Admin/Sub-admin only</span>
          </label>
          <div className="relative mt-1">
            <textarea
              id="specialTerms"
              name="specialTerms"
              rows={3}
              value={formData.specialTerms}
              onChange={handleInputChange}
              onBlur={handleBlur}
              className={`block w-full border rounded-md shadow-sm py-2 px-3 pr-10 focus:outline-none focus:ring-2 ${
                errors.specialTerms
                  ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                  : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
              }`}
              placeholder="Enter any special terms for this customer category"
              maxLength={1000}
            />
            <div className="absolute top-2 right-2">
              {getValidationIcon('specialTerms')}
            </div>
          </div>
          <div className="mt-1 flex justify-between text-xs text-gray-500">
            <span>Special terms, conditions, or notes for this category</span>
            <span>{formData.specialTerms.length}/1000</span>
          </div>
          {errors.specialTerms && (
            <p className="mt-1 text-sm text-red-600 flex items-center">
              <ExclamationCircleIcon className="w-4 h-4 mr-1" />
              {errors.specialTerms}
            </p>
          )}
        </div>
      )}

      {/* Form Status Messages */}
      {errors.submit && (
        <div className="rounded-md bg-red-50 p-4">
          <div className="flex">
            <ExclamationCircleIcon className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">
                Error saving category
              </h3>
              <div className="mt-2 text-sm text-red-700">
                {errors.submit}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Form Validation Summary */}
      {Object.keys(errors).length > 0 && !errors.submit && (
        <div className="rounded-md bg-yellow-50 p-4">
          <div className="flex">
            <ExclamationCircleIcon className="h-5 w-5 text-yellow-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">
                Please fix the following errors:
              </h3>
              <ul className="mt-2 text-sm text-yellow-700 list-disc list-inside">
                {Object.entries(errors).map(([field, error]) => (
                  <li key={field}>{error}</li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      )}

      {/* Form Actions */}
      <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
        <button
          type="button"
          onClick={onCancel}
          disabled={loading}
          className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={loading || isValidating || Object.keys(errors).length > 0}
          className={`px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed ${
            loading
              ? 'bg-gray-400'
              : Object.keys(errors).length > 0
                ? 'bg-gray-400'
                : 'bg-gradient-to-r from-teal-600 to-teal-700 hover:from-teal-700 hover:to-teal-800 focus:ring-teal-500'
          }`}
        >
          {loading ? (
            <div className="flex items-center">
              <LoadingSpinner size="sm" />
              <span className="ml-2">Saving...</span>
            </div>
          ) : isValidating ? (
            <div className="flex items-center">
              <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full" />
              <span className="ml-2">Validating...</span>
            </div>
          ) : (
            <>
              {customerCategory ? 'Update Category' : 'Create Category'}
              {Object.values(validationStatus).some(status => status === 'success') && (
                <CheckCircleIcon className="w-4 h-4 ml-2 inline" />
              )}
            </>
          )}
        </button>
      </div>

      {/* Role-based Help Text */}
      <div className="mt-4 text-xs text-gray-500 space-y-1">
        <p>• Required fields are marked with an asterisk (*)</p>
        {!canSetDiscounts && (
          <p>• Discount settings require Admin or Manager permissions</p>
        )}
        {!canManageAdvanced && (
          <p>• Advanced settings (Sort Order, Special Terms) require Admin permissions</p>
        )}
      </div>
    </form>
  );
};

export default CustomerCategoryForm;
