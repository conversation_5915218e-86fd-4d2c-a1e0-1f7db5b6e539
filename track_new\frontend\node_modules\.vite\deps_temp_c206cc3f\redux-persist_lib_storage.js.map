{"version": 3, "sources": ["../../redux-persist/lib/storage/getStorage.js", "../../redux-persist/lib/storage/createWebStorage.js", "../../redux-persist/lib/storage/index.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports.default = getStorage;\n\nfunction _typeof(obj) { if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }; } return _typeof(obj); }\n\nfunction noop() {}\n\nvar noopStorage = {\n  getItem: noop,\n  setItem: noop,\n  removeItem: noop\n};\n\nfunction hasStorage(storageType) {\n  if ((typeof self === \"undefined\" ? \"undefined\" : _typeof(self)) !== 'object' || !(storageType in self)) {\n    return false;\n  }\n\n  try {\n    var storage = self[storageType];\n    var testKey = \"redux-persist \".concat(storageType, \" test\");\n    storage.setItem(testKey, 'test');\n    storage.getItem(testKey);\n    storage.removeItem(testKey);\n  } catch (e) {\n    if (process.env.NODE_ENV !== 'production') console.warn(\"redux-persist \".concat(storageType, \" test failed, persistence will be disabled.\"));\n    return false;\n  }\n\n  return true;\n}\n\nfunction getStorage(type) {\n  var storageType = \"\".concat(type, \"Storage\");\n  if (hasStorage(storageType)) return self[storageType];else {\n    if (process.env.NODE_ENV !== 'production') {\n      console.error(\"redux-persist failed to create sync storage. falling back to noop storage.\");\n    }\n\n    return noopStorage;\n  }\n}", "\"use strict\";\n\nexports.__esModule = true;\nexports.default = createWebStorage;\n\nvar _getStorage = _interopRequireDefault(require(\"./getStorage\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction createWebStorage(type) {\n  var storage = (0, _getStorage.default)(type);\n  return {\n    getItem: function getItem(key) {\n      return new Promise(function (resolve, reject) {\n        resolve(storage.getItem(key));\n      });\n    },\n    setItem: function setItem(key, item) {\n      return new Promise(function (resolve, reject) {\n        resolve(storage.setItem(key, item));\n      });\n    },\n    removeItem: function removeItem(key) {\n      return new Promise(function (resolve, reject) {\n        resolve(storage.removeItem(key));\n      });\n    }\n  };\n}", "\"use strict\";\n\nexports.__esModule = true;\nexports.default = void 0;\n\nvar _createWebStorage = _interopRequireDefault(require(\"./createWebStorage\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar _default = (0, _createWebStorage.default)('local');\n\nexports.default = _default;"], "mappings": ";;;;;AAAA;AAAA;AAAA;AAEA,YAAQ,aAAa;AACrB,YAAQ,UAAU;AAElB,aAAS,QAAQ,KAAK;AAAE,UAAI,OAAO,WAAW,cAAc,OAAO,OAAO,aAAa,UAAU;AAAE,kBAAU,SAASA,SAAQC,MAAK;AAAE,iBAAO,OAAOA;AAAA,QAAK;AAAA,MAAG,OAAO;AAAE,kBAAU,SAASD,SAAQC,MAAK;AAAE,iBAAOA,QAAO,OAAO,WAAW,cAAcA,KAAI,gBAAgB,UAAUA,SAAQ,OAAO,YAAY,WAAW,OAAOA;AAAA,QAAK;AAAA,MAAG;AAAE,aAAO,QAAQ,GAAG;AAAA,IAAG;AAE9V,aAAS,OAAO;AAAA,IAAC;AAEjB,QAAI,cAAc;AAAA,MAChB,SAAS;AAAA,MACT,SAAS;AAAA,MACT,YAAY;AAAA,IACd;AAEA,aAAS,WAAW,aAAa;AAC/B,WAAK,OAAO,SAAS,cAAc,cAAc,QAAQ,IAAI,OAAO,YAAY,EAAE,eAAe,OAAO;AACtG,eAAO;AAAA,MACT;AAEA,UAAI;AACF,YAAI,UAAU,KAAK,WAAW;AAC9B,YAAI,UAAU,iBAAiB,OAAO,aAAa,OAAO;AAC1D,gBAAQ,QAAQ,SAAS,MAAM;AAC/B,gBAAQ,QAAQ,OAAO;AACvB,gBAAQ,WAAW,OAAO;AAAA,MAC5B,SAAS,GAAG;AACV,YAAI,KAAuC,SAAQ,KAAK,iBAAiB,OAAO,aAAa,6CAA6C,CAAC;AAC3I,eAAO;AAAA,MACT;AAEA,aAAO;AAAA,IACT;AAEA,aAAS,WAAW,MAAM;AACxB,UAAI,cAAc,GAAG,OAAO,MAAM,SAAS;AAC3C,UAAI,WAAW,WAAW,EAAG,QAAO,KAAK,WAAW;AAAA,WAAO;AACzD,YAAI,MAAuC;AACzC,kBAAQ,MAAM,4EAA4E;AAAA,QAC5F;AAEA,eAAO;AAAA,MACT;AAAA,IACF;AAAA;AAAA;;;AC3CA;AAAA;AAAA;AAEA,YAAQ,aAAa;AACrB,YAAQ,UAAU;AAElB,QAAI,cAAc,uBAAuB,oBAAuB;AAEhE,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,iBAAiB,MAAM;AAC9B,UAAI,WAAW,GAAG,YAAY,SAAS,IAAI;AAC3C,aAAO;AAAA,QACL,SAAS,SAAS,QAAQ,KAAK;AAC7B,iBAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,oBAAQ,QAAQ,QAAQ,GAAG,CAAC;AAAA,UAC9B,CAAC;AAAA,QACH;AAAA,QACA,SAAS,SAAS,QAAQ,KAAK,MAAM;AACnC,iBAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,oBAAQ,QAAQ,QAAQ,KAAK,IAAI,CAAC;AAAA,UACpC,CAAC;AAAA,QACH;AAAA,QACA,YAAY,SAAS,WAAW,KAAK;AACnC,iBAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,oBAAQ,QAAQ,WAAW,GAAG,CAAC;AAAA,UACjC,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAAA;AAAA;;;AC5BA;AAAA;AAEA,YAAQ,aAAa;AACrB,YAAQ,UAAU;AAElB,QAAI,oBAAoB,uBAAuB,0BAA6B;AAE5E,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,QAAI,YAAY,GAAG,kBAAkB,SAAS,OAAO;AAErD,YAAQ,UAAU;AAAA;AAAA;", "names": ["_typeof", "obj"]}