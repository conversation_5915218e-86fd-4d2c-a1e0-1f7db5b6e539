/**
 * Simple Categories Module Test
 * Tests basic functionality with authentication
 */

import axios from 'axios';

const BASE_URL = 'http://localhost:8080/api';

async function testCategoriesModule() {
  console.log('🚀 Testing Categories Module...\n');
  
  try {
    // Step 1: Test login to get token
    console.log('1. Testing authentication...');
    const loginData = {
      email: '<EMAIL>',
      password: 'admin123'
    };
    
    const loginResponse = await axios.post(`${BASE_URL}/auth/login`, loginData);
    
    if (loginResponse.status === 200 && loginResponse.data.data.token) {
      console.log('✅ Authentication successful');

      const token = loginResponse.data.data.token;
      const headers = {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      };
      
      // Step 2: Test Service Categories GET
      console.log('2. Testing Service Categories GET...');
      const serviceCategoriesResponse = await axios.get(`${BASE_URL}/service_categories?limit=5`, { headers });
      
      if (serviceCategoriesResponse.status === 200) {
        console.log('✅ Service Categories GET successful');
        console.log(`   Found ${serviceCategoriesResponse.data.data.categories.length} service categories`);
      } else {
        console.log('❌ Service Categories GET failed');
      }
      
      // Step 3: Test Customer Categories GET
      console.log('3. Testing Customer Categories GET...');
      const customerCategoriesResponse = await axios.get(`${BASE_URL}/customer-categories?limit=5`, { headers });
      
      if (customerCategoriesResponse.status === 200) {
        console.log('✅ Customer Categories GET successful');
        console.log(`   Found ${customerCategoriesResponse.data.data.categories.length} customer categories`);
      } else {
        console.log('❌ Customer Categories GET failed');
      }
      
      // Step 4: Test Service Categories CREATE
      console.log('4. Testing Service Categories CREATE...');
      const createServiceCategoryData = {
        serviceCategory: 'Test Service Category',
        description: 'Test category for validation',
        serviceStatus: 1,
        sortOrder: 999
      };
      
      const createServiceResponse = await axios.post(`${BASE_URL}/service_categories`, createServiceCategoryData, { headers });
      
      if (createServiceResponse.status === 201) {
        console.log('✅ Service Category CREATE successful');
        const createdId = createServiceResponse.data.data.id;
        
        // Step 5: Test Service Categories DELETE (cleanup)
        console.log('5. Testing Service Categories DELETE...');
        const deleteResponse = await axios.delete(`${BASE_URL}/service_categories/${createdId}`, { headers });
        
        if (deleteResponse.status === 200) {
          console.log('✅ Service Category DELETE successful');
        } else {
          console.log('❌ Service Category DELETE failed');
        }
      } else {
        console.log('❌ Service Category CREATE failed');
      }
      
      // Step 6: Test Customer Categories CREATE
      console.log('6. Testing Customer Categories CREATE...');
      const createCustomerCategoryData = {
        categoryName: 'Test Customer Category',
        description: 'Test customer category for validation',
        discountPercentage: 5.0,
        isActive: true,
        sortOrder: 999
      };
      
      const createCustomerResponse = await axios.post(`${BASE_URL}/customer-categories`, createCustomerCategoryData, { headers });
      
      if (createCustomerResponse.status === 201) {
        console.log('✅ Customer Category CREATE successful');
        const createdId = createCustomerResponse.data.data.id;
        
        // Step 7: Test Customer Categories DELETE (cleanup)
        console.log('7. Testing Customer Categories DELETE...');
        const deleteResponse = await axios.delete(`${BASE_URL}/customer-categories/${createdId}`, { headers });
        
        if (deleteResponse.status === 200) {
          console.log('✅ Customer Category DELETE successful');
        } else {
          console.log('❌ Customer Category DELETE failed');
        }
      } else {
        console.log('❌ Customer Category CREATE failed');
      }
      
      console.log('\n🎉 Categories Module Test Complete!');
      console.log('✅ All basic CRUD operations are working correctly.');
      console.log('✅ Authentication is working.');
      console.log('✅ API endpoints are responding correctly.');
      console.log('✅ Database connectivity is confirmed.');
      
    } else {
      console.log('❌ Authentication failed');
      console.log('Status:', loginResponse.status);
      console.log('Response:', JSON.stringify(loginResponse.data, null, 2));
      console.log('Please check if the default admin user exists in the database.');
    }
    
  } catch (error) {
    console.log('❌ Test failed with error:', error.message);
    if (error.response) {
      console.log('   Status:', error.response.status);
      console.log('   Data:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

// Run the test
testCategoriesModule();
