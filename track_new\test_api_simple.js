/**
 * Simple API Test for Customer Categories
 */

const BASE_URL = 'http://localhost:8080';

async function testBasicAPI() {
  console.log('🧪 Testing Basic API Access...');
  
  try {
    // Test 1: Check if server is running
    const healthResponse = await fetch(`${BASE_URL}/api/customer-categories`, {
      method: 'GET'
    });
    
    console.log('Response status:', healthResponse.status);
    console.log('Response headers:', Object.fromEntries(healthResponse.headers.entries()));
    
    const responseText = await healthResponse.text();
    console.log('Response body (first 200 chars):', responseText.substring(0, 200));
    
    // Test 2: Try to get a valid auth token
    const loginResponse = await fetch(`${BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        username: 'admin',
        password: 'admin123'
      })
    });
    
    console.log('\nLogin response status:', loginResponse.status);
    const loginText = await loginResponse.text();
    console.log('Login response (first 200 chars):', loginText.substring(0, 200));
    
    if (loginResponse.ok) {
      try {
        const loginData = JSON.parse(loginText);
        if (loginData.token) {
          console.log('✅ Got auth token, testing authenticated request...');
          
          // Test authenticated request
          const authResponse = await fetch(`${BASE_URL}/api/customer-categories`, {
            headers: {
              'Authorization': `Bearer ${loginData.token}`,
              'Content-Type': 'application/json'
            }
          });
          
          console.log('Authenticated request status:', authResponse.status);
          const authText = await authResponse.text();
          console.log('Authenticated response (first 200 chars):', authText.substring(0, 200));
          
          if (authResponse.ok) {
            try {
              const authData = JSON.parse(authText);
              console.log('✅ Authenticated API working:', {
                success: authData.success,
                dataCount: authData.data?.categories?.length || 0
              });
              
              // Test name availability check
              const nameCheckResponse = await fetch(`${BASE_URL}/api/customer-categories/check-name?name=TestName`, {
                headers: {
                  'Authorization': `Bearer ${loginData.token}`,
                  'Content-Type': 'application/json'
                }
              });
              
              console.log('Name check status:', nameCheckResponse.status);
              const nameCheckText = await nameCheckResponse.text();
              console.log('Name check response:', nameCheckText.substring(0, 200));
              
              if (nameCheckResponse.ok) {
                const nameCheckData = JSON.parse(nameCheckText);
                console.log('✅ Name availability check working:', nameCheckData);
              }
              
            } catch (parseError) {
              console.log('❌ Could not parse authenticated response as JSON');
            }
          }
        }
      } catch (parseError) {
        console.log('❌ Could not parse login response as JSON');
      }
    }
    
  } catch (error) {
    console.error('❌ API test failed:', error.message);
  }
}

testBasicAPI();
