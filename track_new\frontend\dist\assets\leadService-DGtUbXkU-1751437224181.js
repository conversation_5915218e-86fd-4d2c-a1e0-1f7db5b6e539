import{a}from"./index-Ck020PKJ-1751437224181.js";import"./vendor-CDaM45aE-1751437224181.js";import"./router-DrGV_cTB-1751437224181.js";import"./ui-CFFfOq2L-1751437224181.js";import"./utils-0ePtVbef-1751437224181.js";const d={async getLeads(e={}){return(await a.get("/leads",{params:e})).data},async getLead(e){return(await a.get(`/leads/${e}`)).data},async createLead(e){return(await a.post("/leads",e)).data},async updateLead(e,t){return(await a.put(`/leads/${e}`,t)).data},async deleteLead(e){return(await a.delete(`/leads/${e}`)).data},async getLeadStats(){return(await a.get("/leads/stats")).data},async getLeadTypes(e={}){return(await a.get("/lead-types",{params:e})).data},async createLeadType(e){return(await a.post("/lead-types",e)).data},async updateLeadType(e,t){return(await a.put(`/lead-types/${e}`,t)).data},async deleteLeadType(e){return(await a.delete(`/lead-types/${e}`)).data},async getLeadStatuses(e={}){return(await a.get("/lead-statuses",{params:e})).data},async createLeadStatus(e){return(await a.post("/lead-statuses",e)).data},async updateLeadStatus(e,t){return(await a.put(`/lead-statuses/${e}`,t)).data},async deleteLeadStatus(e){return(await a.delete(`/lead-statuses/${e}`)).data},async getEnquiries(e={}){return(await a.get("/enquiries",{params:e})).data},async getEnquiry(e){return(await a.get(`/enquiries/${e}`)).data},async createEnquiry(e){return(await a.post("/enquiries",e)).data},async updateEnquiry(e,t){return(await a.put(`/enquiries/${e}`,t)).data},async deleteEnquiry(e){return(await a.delete(`/enquiries/${e}`)).data},async convertEnquiryToLead(e,t){return(await a.post(`/enquiries/${e}/convert-to-lead`,t)).data},async getEnquiryStats(){return(await a.get("/enquiries/stats")).data},getStatusColor(e){return{open:"bg-blue-100 text-blue-800",in_progress:"bg-yellow-100 text-yellow-800",qualified:"bg-purple-100 text-purple-800",converted:"bg-green-100 text-green-800",closed:"bg-gray-100 text-gray-800",lost:"bg-red-100 text-red-800"}[e]||"bg-gray-100 text-gray-800"},getPriorityColor(e){return{low:"bg-green-100 text-green-800",medium:"bg-yellow-100 text-yellow-800",high:"bg-orange-100 text-orange-800",urgent:"bg-red-100 text-red-800"}[e]||"bg-gray-100 text-gray-800"},getSourceIcon(e){return{website:"fa-solid fa-globe",phone:"fa-solid fa-phone",email:"fa-solid fa-envelope",referral:"fa-solid fa-user-friends",social:"fa-solid fa-share-alt",direct:"fa-solid fa-handshake"}[e]||"fa-solid fa-question"},formatLeadData(e){var t,s;return{...e,statusColor:this.getStatusColor(e.status),priorityColor:this.getPriorityColor(e.priority),sourceIcon:this.getSourceIcon(e.source),formattedDate:e.leadDate?new Date(e.leadDate).toLocaleDateString():"",formattedFollowUp:e.followUp?new Date(e.followUp).toLocaleDateString():"",customerName:((t=e.customer)==null?void 0:t.name)||"Unknown Customer",assignedUserName:((s=e.assignedUser)==null?void 0:s.name)||"Unassigned"}},buildFilterParams(e){const t={};return e.search&&(t.search=e.search),e.status&&e.status!=="all"&&(t.status=e.status),e.priority&&e.priority!=="all"&&(t.priority=e.priority),e.assignTo&&(t.assignTo=e.assignTo),e.customerId&&(t.customerId=e.customerId),e.source&&e.source!=="all"&&(t.source=e.source),e.sortBy&&(t.sortBy=e.sortBy),e.sortOrder&&(t.sortOrder=e.sortOrder),e.page&&(t.page=e.page),e.limit&&(t.limit=e.limit),t},mapOldStatus(e){return{0:"open",1:"in_progress",2:"qualified",3:"converted",4:"closed",5:"lost",pending:"open",progress:"in_progress",completed:"qualified",cancelled:"closed",hold:"closed"}[e]||"open"},getStatusCounts(e){return[{type:"pending",total:e.pending||e.open||0,icon:"fa-solid fa-hourglass-half"},{type:"open",total:e.open||0,icon:"fa-regular fa-folder-open"},{type:"progress",total:e.in_progress||0,icon:"fa-solid fa-bars-progress"},{type:"completed",total:e.qualified||0,icon:"fa-solid fa-flag-checkered"},{type:"cancelled",total:e.closed||0,icon:"fa-solid fa-ban"},{type:"hold",total:e.lost||0,icon:"fa-solid fa-hand"},{type:"all",total:e.all||0,icon:"fa-solid fa-circle-check"}]}};export{d as default};
//# sourceMappingURL=leadService-DGtUbXkU-1751437224181.js.map
