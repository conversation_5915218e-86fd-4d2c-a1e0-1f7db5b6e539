/* Service Categories Component Styles - Teal/Orange Theme with Compact Design */

/* ===== HEADER STYLES ===== */
.categories-header {
  background: linear-gradient(135deg, #f0fdfa 0%, #ccfbf1 100%);
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border: 1px solid #a7f3d0;
}

.categories-title {
  color: #0f766e;
  font-weight: 700;
  font-size: 1.75rem;
  margin-bottom: 0.5rem;
}

.categories-subtitle {
  color: #0f766e;
  opacity: 0.8;
}

/* ===== TAB STYLES - Teal/Orange Theme ===== */
.categories-tabs {
  border-bottom: 2px solid #e5e7eb;
  margin-bottom: 1.5rem;
}

.categories-tab {
  padding: 0.75rem 1rem;
  border-bottom: 3px solid transparent;
  font-weight: 600;
  font-size: 0.875rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.categories-tab.active.service-categories {
  border-bottom-color: #0f766e;
  color: #0f766e;
  background: linear-gradient(135deg, #f0fdfa 0%, #ccfbf1 100%);
}

.categories-tab.active.customer-categories {
  border-bottom-color: #ea580c;
  color: #ea580c;
  background: linear-gradient(135deg, #fff7ed 0%, #fed7aa 100%);
}

.categories-tab:not(.active) {
  color: #6b7280;
}

.categories-tab:not(.active):hover {
  color: #374151;
  background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
}

/* ===== BUTTON STYLES - Teal/Orange Theme ===== */
.btn-categories-primary {
  background: linear-gradient(135deg, #0f766e 0%, #14b8a6 50%, #2dd4bf 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 600;
  font-size: 0.875rem;
  box-shadow: 0 6px 20px rgba(15, 118, 110, 0.3);
  position: relative;
  overflow: hidden;
}

.btn-categories-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-categories-primary:hover::before {
  left: 100%;
}

.btn-categories-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(15, 118, 110, 0.4);
}

.btn-categories-secondary {
  background: linear-gradient(135deg, #ea580c 0%, #f97316 50%, #fb923c 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 600;
  font-size: 0.875rem;
  box-shadow: 0 6px 20px rgba(234, 88, 12, 0.3);
  position: relative;
  overflow: hidden;
}

.btn-categories-secondary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-categories-secondary:hover::before {
  left: 100%;
}

.btn-categories-secondary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(234, 88, 12, 0.4);
}

/* ===== SEARCH AND FILTER STYLES ===== */
.categories-search-container {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  margin-bottom: 1.5rem;
  border: 1px solid #e5e7eb;
}

.categories-search-input {
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  transition: all 0.3s ease;
  background: white;
}

.categories-search-input:focus {
  outline: none;
  border-color: #0f766e;
  box-shadow: 0 0 0 3px rgba(15, 118, 110, 0.1);
}

.categories-filter-select {
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  padding: 0.75rem 1rem;
  transition: all 0.3s ease;
  background: white;
}

.categories-filter-select:focus {
  outline: none;
  border-color: #0f766e;
  box-shadow: 0 0 0 3px rgba(15, 118, 110, 0.1);
}

/* ===== VIEW MODE TOGGLE ===== */
.categories-view-toggle {
  background: linear-gradient(135deg, #fed7aa 0%, #fb923c 100%);
  border-radius: 8px;
  padding: 0.25rem;
  border: 1px solid #fdba74;
}

.categories-view-toggle button {
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  transition: all 0.3s ease;
  font-weight: 500;
}

.categories-view-toggle button.active {
  background: linear-gradient(135deg, #0f766e 0%, #14b8a6 100%);
  color: white;
  box-shadow: 0 2px 4px rgba(15, 118, 110, 0.3);
}

.categories-view-toggle button:not(.active) {
  color: #ea580c;
  background: transparent;
}

.categories-view-toggle button:not(.active):hover {
  background: rgba(255, 255, 255, 0.5);
}

/* ===== CARD STYLES - Compact Design (30-40% Height Reduction) ===== */
.category-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid #e2e8f0;
  border-radius: 16px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  height: 140px; /* 30-40% height reduction from standard 200px+ */
}

.category-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #0f766e 0%, #ea580c 50%, #14b8a6 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.category-card:hover {
  box-shadow: 0 20px 25px -5px rgba(15, 118, 110, 0.15), 0 10px 10px -5px rgba(234, 88, 12, 0.1);
  transform: translateY(-4px);
  border-color: #fbbf24;
}

.category-card:hover::before {
  opacity: 1;
}

.category-card-header {
  padding: 0.75rem;
  background: linear-gradient(135deg, #0f766e 0%, #14b8a6 100%);
  color: white;
  height: 60px; /* Compact header */
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.category-card-body {
  padding: 0.75rem;
  height: 80px; /* Compact body */
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.category-card-title {
  font-weight: 600;
  font-size: 0.875rem;
  color: white;
  margin: 0;
  line-height: 1.2;
}

.category-card-description {
  font-size: 0.75rem;
  color: #6b7280;
  line-height: 1.3;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* ===== CUSTOMER CATEGORY CARD VARIATIONS ===== */
.category-card.customer-category .category-card-header {
  background: linear-gradient(135deg, #ea580c 0%, #f97316 100%);
}

/* ===== STATUS BADGES ===== */
.status-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.status-badge.active {
  background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
  color: #166534;
  border: 1px solid #86efac;
}

.status-badge.inactive {
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
  color: #991b1b;
  border: 1px solid #f87171;
}

/* ===== ACTION BUTTONS ===== */
.category-action-buttons {
  display: flex;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.category-action-btn {
  padding: 0.375rem;
  border-radius: 6px;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.75rem;
}

.category-action-btn.view {
  background: linear-gradient(135deg, #64748b 0%, #475569 100%);
  color: white;
}

.category-action-btn.edit {
  background: linear-gradient(135deg, #ea580c 0%, #f97316 100%);
  color: white;
}

.category-action-btn.delete {
  background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
  color: white;
}

.category-action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* ===== GRID LAYOUT ===== */
.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

/* ===== LIST VIEW STYLES ===== */
.categories-list {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 2rem;
}

.categories-list-item {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.categories-list-item:last-child {
  border-bottom: none;
}

.categories-list-item:hover {
  background: linear-gradient(135deg, #f0fdfa 0%, #ccfbf1 100%);
}

/* ===== PAGINATION STYLES - Teal/Orange Theme ===== */
.categories-pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 2rem;
  padding: 1rem;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.categories-pagination .pagination-button {
  background: linear-gradient(135deg, #0f766e 0%, #14b8a6 100%);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  margin: 0 0.25rem;
}

.categories-pagination .pagination-button:hover {
  background: linear-gradient(135deg, #134e4a 0%, #0f766e 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(15, 118, 110, 0.3);
}

.categories-pagination .pagination-button.active {
  background: linear-gradient(135deg, #ea580c 0%, #f97316 100%);
  box-shadow: 0 4px 8px rgba(234, 88, 12, 0.3);
}

.categories-pagination .pagination-button:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* ===== EMPTY STATE ===== */
.categories-empty-state {
  text-align: center;
  padding: 3rem 1rem;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12px;
  border: 2px dashed #cbd5e1;
  margin: 2rem 0;
}

.categories-empty-state h3 {
  color: #475569;
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.categories-empty-state p {
  color: #64748b;
  font-size: 0.875rem;
}

/* ===== LOADING STATE ===== */
.categories-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 3rem;
  background: linear-gradient(135deg, #f0fdfa 0%, #ccfbf1 100%);
  border-radius: 12px;
  margin: 2rem 0;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .categories-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .category-card {
    height: 120px; /* Even more compact on mobile */
  }

  .category-card-header {
    height: 50px;
    padding: 0.5rem;
  }

  .category-card-body {
    height: 70px;
    padding: 0.5rem;
  }

  .categories-header {
    padding: 1rem;
  }

  .categories-title {
    font-size: 1.5rem;
  }

  .btn-categories-primary,
  .btn-categories-secondary {
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
  }
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */
.category-card:focus-within {
  outline: 2px solid #0f766e;
  outline-offset: 2px;
}

.categories-tab:focus {
  outline: 2px solid #0f766e;
  outline-offset: 2px;
}

.btn-categories-primary:focus,
.btn-categories-secondary:focus {
  outline: 2px solid #ffffff;
  outline-offset: 2px;
}

/* ===== ANIMATION ENHANCEMENTS ===== */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.category-card {
  animation: slideInUp 0.3s ease-out;
}

.categories-header {
  animation: slideInUp 0.2s ease-out;
}

/* ===== HOVER EFFECTS FOR BETTER UX ===== */
.categories-search-input:hover {
  border-color: #14b8a6;
}

.categories-filter-select:hover {
  border-color: #14b8a6;
}
