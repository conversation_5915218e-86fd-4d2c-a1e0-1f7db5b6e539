import React, { useEffect, useRef } from 'react';
import { Routes, Route, Navigate, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { Helmet } from 'react-helmet-async';

// Pages
import Login from './pages/auth/Login';
import Dashboard from './pages/DashboardSimple';
import Services from './pages/Services';
import ServiceCategories from './pages/ServiceCategories';
import RMA from './pages/RMA';
import Customers from './pages/Customers';
import Leads from './pages/Leads';
import WebsiteEnquiry from './pages/WebsiteEnquiry';
import AMCs from './pages/AMCs';
import Sales from './pages/Sales';
import Estimations from './pages/Estimations';
import Inventory from './pages/Inventory';
import Reports from './pages/Reports';
import Employees from './pages/Employees';
import EmployeeFeatures from './pages/EmployeeFeatures';
import Expenses from './pages/Expenses';
import StockAdjust from './pages/StockAdjust';
import Purchase from './pages/Purchase';
import PaymentOut from './pages/PaymentOut';
import Website from './pages/Website';
import Ecommerce from './pages/Ecommerce';
import Settings from './pages/Settings';
import Unauthorized from './pages/Unauthorized';
import Test from './pages/Test';
import InventoryTest from './pages/InventoryTest';
import UITestPage from './pages/UITestPage';
import PaymentOutTest from './components/inventory/PaymentOutTest';
import AnalyticsEndpointTester from './components/debug/AnalyticsEndpointTester';

// Components
import LoadingSpinner from './components/common/LoadingSpinner';
import ProtectedRoute from './components/auth/ProtectedRoute';
import DashboardLayout from './components/layouts/DashboardLayout';
import NotificationSystem from './components/common/NotificationSystem';
// import AuthInitializer from './components/auth/AuthInitializer'; // DISABLED - causing auth loops
import AuthDebugPanel from './components/debug/AuthDebugPanel';

// Store
import { checkAuth, selectAuth } from './store/slices/authSlice';

function App() {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { isAuthenticated, isLoading } = useSelector(selectAuth);
  const hasCheckedAuth = useRef(false);

  useEffect(() => {
    // EMERGENCY FIX: Clear all authentication data to start fresh
    if (!hasCheckedAuth.current) {
      hasCheckedAuth.current = true;
      console.log('🚨 EMERGENCY FIX: Clearing all authentication data to resolve unauthorized redirect issue...');

      // Clear all possible authentication storage keys
      const keysToRemove = [
        'tracknew_token',
        'tracknew_user',
        'tracknew_company',
        'tracknew_refresh_token',
        'tracknew_auto_login',
        'token',
        'user',
        'company',
        'persist:root'
      ];

      keysToRemove.forEach(key => {
        localStorage.removeItem(key);
        console.log(`🧹 Cleared: ${key}`);
      });

      // Also clear sessionStorage
      sessionStorage.clear();

      console.log('✅ All authentication data cleared. User should now see login page.');
    }
  }, [dispatch]);

  // Listen for custom logout events to prevent port switching
  useEffect(() => {
    const handleAuthLogout = (event) => {
      console.log('App: Received auth logout event:', event.detail);
      // Use React Router navigation instead of window.location
      navigate('/login', { replace: true });
    };

    window.addEventListener('auth:logout', handleAuthLogout);

    return () => {
      window.removeEventListener('auth:logout', handleAuthLogout);
    };
  }, [navigate]);

  // DEBUGGING: Add detailed logging for authentication state
  console.log('🔍 DEBUGGING - Current URL:', window.location.href);
  console.log('🔍 DEBUGGING - Auth state:', { isAuthenticated, isLoading });
  console.log('🔍 DEBUGGING - Token exists:', !!localStorage.getItem('tracknew_token'));

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <LoadingSpinner size="lg" text="Loading TrackNew..." />
      </div>
    );
  }

  return (
    <>
      <Helmet>
        <title>TrackNew - Service Management Application</title>
        <meta name="description" content="Comprehensive service management application for tracking services, customers, inventory, and more." />
      </Helmet>

      <Routes>
        {/* Public Routes */}
        <Route path="/test" element={<Test />} />
        <Route path="/inventory-test" element={<InventoryTest />} />
        <Route path="/ui-test" element={<UITestPage />} />
        <Route path="/payment-test" element={<PaymentOutTest />} />
        <Route path="/analytics-test" element={<AnalyticsEndpointTester />} />

        {/* Debug Route - Show authentication state */}
        <Route
          path="/auth-debug"
          element={
            <div style={{ padding: '20px', fontFamily: 'monospace' }}>
              <h2>Authentication Debug</h2>
              <p><strong>isAuthenticated:</strong> {String(isAuthenticated)}</p>
              <p><strong>isLoading:</strong> {String(isLoading)}</p>
              <p><strong>Token exists:</strong> {String(!!localStorage.getItem('tracknew_token'))}</p>
              <p><strong>Current URL:</strong> {window.location.href}</p>
              <p><strong>Should redirect to:</strong> {isAuthenticated ? '/dashboard' : '/login'}</p>
              <button
                onClick={() => {
                  localStorage.clear();
                  window.location.reload();
                }}
                style={{ padding: '10px', marginTop: '10px', backgroundColor: '#ef4444', color: 'white', border: 'none', borderRadius: '4px' }}
              >
                Clear All Storage & Reload
              </button>
            </div>
          }
        />
        <Route
          path="/login"
          element={
            isAuthenticated ? (
              <Navigate to="/dashboard" replace />
            ) : (
              <Login />
            )
          }
        />

        {/* Protected Routes with Role-Based Access Control */}

        {/* Dashboard - All roles can access */}
        <Route
          path="/dashboard"
          element={
            <ProtectedRoute navigation="dashboard">
              <DashboardLayout>
                <Dashboard />
              </DashboardLayout>
            </ProtectedRoute>
          }
        />

        {/* Services - All roles except sales_man */}
        <Route
          path="/services"
          element={
            <ProtectedRoute
              navigation="services"
              requiredRoles={['admin', 'sub_admin', 'account_manager', 'service_manager', 'service_engineer']}
            >
              <DashboardLayout>
                <Services />
              </DashboardLayout>
            </ProtectedRoute>
          }
        />

        {/* Service Categories - All roles except sales_man */}
        <Route
          path="/services/categories"
          element={
            <ProtectedRoute
              navigation="services"
              requiredRoles={['admin', 'sub_admin', 'account_manager', 'service_manager', 'service_engineer']}
            >
              <DashboardLayout>
                <ServiceCategories />
              </DashboardLayout>
            </ProtectedRoute>
          }
        />

        {/* RMA - All roles except sales_man */}
        <Route
          path="/rma"
          element={
            <ProtectedRoute
              navigation="services"
              requiredRoles={['admin', 'sub_admin', 'account_manager', 'service_manager', 'service_engineer']}
            >
              <DashboardLayout>
                <RMA />
              </DashboardLayout>
            </ProtectedRoute>
          }
        />

        {/* Customers - All roles except sales_man */}
        <Route
          path="/customers"
          element={
            <ProtectedRoute
              navigation="customers"
              requiredRoles={['admin', 'sub_admin', 'account_manager', 'service_manager', 'service_engineer']}
            >
              <DashboardLayout>
                <Customers />
              </DashboardLayout>
            </ProtectedRoute>
          }
        />

        {/* Leads - All roles except sales_man */}
        <Route
          path="/leads"
          element={
            <ProtectedRoute
              navigation="leads"
              requiredRoles={['admin', 'sub_admin', 'account_manager', 'service_manager', 'service_engineer']}
            >
              <DashboardLayout>
                <Leads />
              </DashboardLayout>
            </ProtectedRoute>
          }
        />

        {/* Website Enquiry - All roles except sales_man */}
        <Route
          path="/website-enquiry"
          element={
            <ProtectedRoute
              navigation="leads"
              requiredRoles={['admin', 'sub_admin', 'account_manager', 'service_manager', 'service_engineer']}
            >
              <DashboardLayout>
                <WebsiteEnquiry />
              </DashboardLayout>
            </ProtectedRoute>
          }
        />

        {/* AMCs - All roles except sales_man */}
        <Route
          path="/amcs"
          element={
            <ProtectedRoute
              navigation="amcs"
              requiredRoles={['admin', 'sub_admin', 'account_manager', 'service_manager', 'service_engineer']}
            >
              <DashboardLayout>
                <AMCs />
              </DashboardLayout>
            </ProtectedRoute>
          }
        />

        {/* Sales - All roles can access */}
        <Route
          path="/sales"
          element={
            <ProtectedRoute navigation="sales">
              <DashboardLayout>
                <Sales />
              </DashboardLayout>
            </ProtectedRoute>
          }
        />

        {/* Sales Sub-routes - All roles can access */}
        <Route
          path="/sales/invoice"
          element={
            <ProtectedRoute navigation="sales">
              <DashboardLayout>
                <Sales />
              </DashboardLayout>
            </ProtectedRoute>
          }
        />
        <Route
          path="/sales/payment-in"
          element={
            <ProtectedRoute navigation="sales">
              <DashboardLayout>
                <Sales />
              </DashboardLayout>
            </ProtectedRoute>
          }
        />
        <Route
          path="/sales/proforma"
          element={
            <ProtectedRoute navigation="sales">
              <DashboardLayout>
                <Sales />
              </DashboardLayout>
            </ProtectedRoute>
          }
        />
        <Route
          path="/sales/quote-estimate"
          element={
            <ProtectedRoute navigation="sales">
              <DashboardLayout>
                <Sales />
              </DashboardLayout>
            </ProtectedRoute>
          }
        />

        {/* Estimations/Quotations - All roles except sales_man */}
        <Route
          path="/estimations"
          element={
            <ProtectedRoute
              navigation="quotations"
              requiredRoles={['admin', 'sub_admin', 'account_manager', 'service_manager', 'service_engineer']}
            >
              <DashboardLayout>
                <Estimations />
              </DashboardLayout>
            </ProtectedRoute>
          }
        />

        {/* Inventory - Admin, sub_admin and account_manager */}
        <Route
          path="/inventory"
          element={
            <ProtectedRoute
              navigation="inventory"
              requiredRoles={['admin', 'sub_admin', 'account_manager']}
            >
              <DashboardLayout>
                <Inventory />
              </DashboardLayout>
            </ProtectedRoute>
          }
        />
        <Route
          path="/inventory/purchase"
          element={
            <ProtectedRoute
              navigation="inventory"
              requiredRoles={['admin', 'sub_admin', 'account_manager']}
            >
              <DashboardLayout>
                <Purchase />
              </DashboardLayout>
            </ProtectedRoute>
          }
        />
        <Route
          path="/inventory/supplier"
          element={
            <ProtectedRoute
              navigation="inventory"
              requiredRoles={['admin', 'sub_admin', 'account_manager']}
            >
              <DashboardLayout>
                <Inventory />
              </DashboardLayout>
            </ProtectedRoute>
          }
        />
        <Route
          path="/inventory/warehouse"
          element={
            <ProtectedRoute
              navigation="inventory"
              requiredRoles={['admin', 'sub_admin', 'account_manager']}
            >
              <DashboardLayout>
                <Inventory />
              </DashboardLayout>
            </ProtectedRoute>
          }
        />
        <Route
          path="/inventory/payment-out"
          element={
            <ProtectedRoute
              navigation="inventory"
              requiredRoles={['admin', 'sub_admin', 'account_manager']}
            >
              <DashboardLayout>
                <PaymentOut />
              </DashboardLayout>
            </ProtectedRoute>
          }
        />
        <Route
          path="/inventory/stock-adjust"
          element={
            <ProtectedRoute
              navigation="inventory"
              requiredRoles={['admin', 'sub_admin', 'account_manager']}
            >
              <DashboardLayout>
                <StockAdjust />
              </DashboardLayout>
            </ProtectedRoute>
          }
        />

        {/* Reports - All roles except sales_man */}
        <Route
          path="/reports"
          element={
            <ProtectedRoute
              navigation="reports"
              requiredRoles={['admin', 'sub_admin', 'account_manager', 'service_manager', 'service_engineer']}
            >
              <DashboardLayout>
                <Reports />
              </DashboardLayout>
            </ProtectedRoute>
          }
        />

        {/* Employees - Admin and sub_admin */}
        <Route
          path="/employees"
          element={
            <ProtectedRoute
              navigation="users"
              requiredRoles={['admin', 'sub_admin']}
            >
              <DashboardLayout>
                <Employees />
              </DashboardLayout>
            </ProtectedRoute>
          }
        />

        {/* Employee Features Demo - Admin and sub_admin */}
        <Route
          path="/employee-features"
          element={
            <ProtectedRoute
              navigation="users"
              requiredRoles={['admin', 'sub_admin']}
            >
              <DashboardLayout>
                <EmployeeFeatures />
              </DashboardLayout>
            </ProtectedRoute>
          }
        />

        {/* Expenses - Admin, sub_admin and account_manager */}
        <Route
          path="/expenses"
          element={
            <ProtectedRoute
              requiredRoles={['admin', 'sub_admin', 'account_manager']}
            >
              <DashboardLayout>
                <Expenses />
              </DashboardLayout>
            </ProtectedRoute>
          }
        />

        {/* Website - Admin, sub_admin and account_manager */}
        <Route
          path="/website"
          element={
            <ProtectedRoute
              requiredRoles={['admin', 'sub_admin', 'account_manager']}
            >
              <DashboardLayout>
                <Website />
              </DashboardLayout>
            </ProtectedRoute>
          }
        />

        {/* Ecommerce - Admin, sub_admin and account_manager */}
        <Route
          path="/ecommerce"
          element={
            <ProtectedRoute
              requiredRoles={['admin', 'sub_admin', 'account_manager']}
            >
              <DashboardLayout>
                <Ecommerce />
              </DashboardLayout>
            </ProtectedRoute>
          }
        />

        {/* Settings - Only admin and super_admin */}
        <Route
          path="/settings"
          element={
            <ProtectedRoute
              navigation="settings"
              requiredRoles={['admin', 'super_admin']}
            >
              <DashboardLayout>
                <Settings />
              </DashboardLayout>
            </ProtectedRoute>
          }
        />

        {/* Unauthorized page */}
        <Route path="/unauthorized" element={<Unauthorized />} />

        {/* Fallback Routes */}
        <Route
          path="/"
          element={
            (() => {
              const redirectTo = isAuthenticated ? "/dashboard" : "/login";
              console.log('🔍 ROOT ROUTE REDIRECT - isAuthenticated:', isAuthenticated, 'redirecting to:', redirectTo);
              return <Navigate to={redirectTo} replace />;
            })()
          }
        />
        <Route
          path="*"
          element={
            <Navigate
              to={isAuthenticated ? "/dashboard" : "/login"}
              replace
            />
          }
        />
      </Routes>

      {/* Global Notification System */}
      <NotificationSystem />

      {/* Auth Debug Panel (Development Only) */}
      <AuthDebugPanel />
    </>
  );
}

export default App;
