{"version": 3, "file": "sw.js", "sources": ["C:/Users/<USER>/AppData/Local/Temp/0404aa74868fb374c86f03004275e0ab/sw.js"], "sourcesContent": ["import {registerRoute as workbox_routing_registerRoute} from 'D:/New_TrackNew/track_new/frontend/node_modules/workbox-routing/registerRoute.mjs';\nimport {ExpirationPlugin as workbox_expiration_ExpirationPlugin} from 'D:/New_TrackNew/track_new/frontend/node_modules/workbox-expiration/ExpirationPlugin.mjs';\nimport {CacheableResponsePlugin as workbox_cacheable_response_CacheableResponsePlugin} from 'D:/New_TrackNew/track_new/frontend/node_modules/workbox-cacheable-response/CacheableResponsePlugin.mjs';\nimport {NetworkFirst as workbox_strategies_NetworkFirst} from 'D:/New_TrackNew/track_new/frontend/node_modules/workbox-strategies/NetworkFirst.mjs';\nimport {StaleWhileRevalidate as workbox_strategies_StaleWhileRevalidate} from 'D:/New_TrackNew/track_new/frontend/node_modules/workbox-strategies/StaleWhileRevalidate.mjs';\nimport {clientsClaim as workbox_core_clientsClaim} from 'D:/New_TrackNew/track_new/frontend/node_modules/workbox-core/clientsClaim.mjs';\nimport {precacheAndRoute as workbox_precaching_precacheAndRoute} from 'D:/New_TrackNew/track_new/frontend/node_modules/workbox-precaching/precacheAndRoute.mjs';\nimport {cleanupOutdatedCaches as workbox_precaching_cleanupOutdatedCaches} from 'D:/New_TrackNew/track_new/frontend/node_modules/workbox-precaching/cleanupOutdatedCaches.mjs';\nimport {NavigationRoute as workbox_routing_NavigationRoute} from 'D:/New_TrackNew/track_new/frontend/node_modules/workbox-routing/NavigationRoute.mjs';\nimport {createHandlerBoundToURL as workbox_precaching_createHandlerBoundToURL} from 'D:/New_TrackNew/track_new/frontend/node_modules/workbox-precaching/createHandlerBoundToURL.mjs';/**\n * Welcome to your Workbox-powered service worker!\n *\n * You'll need to register this file in your web app.\n * See https://goo.gl/nhQhGp\n *\n * The rest of the code is auto-generated. Please don't update this file\n * directly; instead, make changes to your Workbox build configuration\n * and re-run your build process.\n * See https://goo.gl/2aRDsh\n */\n\n\n\n\n\n\n\n\nself.skipWaiting();\n\nworkbox_core_clientsClaim();\n\n\n/**\n * The precacheAndRoute() method efficiently caches and responds to\n * requests for URLs in the manifest.\n * See https://goo.gl/S9QRab\n */\nworkbox_precaching_precacheAndRoute([\n  {\n    \"url\": \"app-status.html\",\n    \"revision\": \"236ad22172cb3649c7fce918e2bb317b\"\n  },\n  {\n    \"url\": \"assets/index-S4mLatTV-*************.css\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/leadService-CwSIh4ia-*************.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/router-BtxlV-6n-*************.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/ui-Bu5u-5po-*************.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/utils-C8ewhTUc-*************.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/vendor-CDaM45aE-*************.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"cache-test.html\",\n    \"revision\": \"fd510ee462fd53603bb2527e289ac783\"\n  },\n  {\n    \"url\": \"clear-browser-cache.html\",\n    \"revision\": \"2804f178b0567e82669338b4b694f50b\"\n  },\n  {\n    \"url\": \"clear-cache.html\",\n    \"revision\": \"253c10e3eb97f57e80d99ae1c65dce65\"\n  },\n  {\n    \"url\": \"clear-cache.js\",\n    \"revision\": \"f4b9de89524e4c0d5c20ffe2868b6205\"\n  },\n  {\n    \"url\": \"debug-detector.html\",\n    \"revision\": \"ad181f7cbcd455820b7c222f79ea50ee\"\n  },\n  {\n    \"url\": \"diagnostic.html\",\n    \"revision\": \"f0d35a86d4dddbe0444c455d21ba2e2e\"\n  },\n  {\n    \"url\": \"image-converter.html\",\n    \"revision\": \"82ab1e52f1668fb0c07eae98e8dded42\"\n  },\n  {\n    \"url\": \"images/empty-state.png\",\n    \"revision\": \"ee354b02ceb77ac70c6b8a1b2e3d7650\"\n  },\n  {\n    \"url\": \"images/empty-state.svg\",\n    \"revision\": \"0233d6508e96ef933f1f8967510788f0\"\n  },\n  {\n    \"url\": \"images/service_page/Add.png\",\n    \"revision\": \"9ae8c844cfa6f84dd749e23854f31008\"\n  },\n  {\n    \"url\": \"images/service_page/address.png\",\n    \"revision\": \"ee549420abade7061df7fe8206ee0863\"\n  },\n  {\n    \"url\": \"images/service_page/anniversary.png\",\n    \"revision\": \"e3b344fcaccb463a3867c57a480288cb\"\n  },\n  {\n    \"url\": \"images/service_page/Calendar.png\",\n    \"revision\": \"fc31add62892d0f4fca74dfa8a56d4ea\"\n  },\n  {\n    \"url\": \"images/service_page/cooperation.png\",\n    \"revision\": \"a6c38e6dbdbc183e532d8d98467b6720\"\n  },\n  {\n    \"url\": \"images/service_page/Email.png\",\n    \"revision\": \"eeebd14f2b3ecaea8105bfb94e1ef7c7\"\n  },\n  {\n    \"url\": \"images/service_page/happy-birthday.png\",\n    \"revision\": \"34c68882d55c12ef2260df1cf74d821a\"\n  },\n  {\n    \"url\": \"images/service_page/map.png\",\n    \"revision\": \"434e605a412a14f9c369db1da133eaf0\"\n  },\n  {\n    \"url\": \"images/service_page/Money.png\",\n    \"revision\": \"f2f23e599c90e23668aeae0c60a325e5\"\n  },\n  {\n    \"url\": \"images/service_page/Phone_call.png\",\n    \"revision\": \"16897aa5680d1c41528162025be71ef6\"\n  },\n  {\n    \"url\": \"images/service_page/User.png\",\n    \"revision\": \"a2fc706de6c4ae6e8cb21595f3988af7\"\n  },\n  {\n    \"url\": \"images/service_page/Writing.png\",\n    \"revision\": \"9cfb8e8c61d2736a06f95306636a78e9\"\n  },\n  {\n    \"url\": \"index.html\",\n    \"revision\": \"46b3233b066c5d5f2a71d9d2b0adceb0\"\n  },\n  {\n    \"url\": \"monitor-auto-reload.html\",\n    \"revision\": \"20a619f61c5e562a71f111e8022fabf7\"\n  },\n  {\n    \"url\": \"pwa-192x192.png\",\n    \"revision\": \"ee354b02ceb77ac70c6b8a1b2e3d7650\"\n  },\n  {\n    \"url\": \"pwa-192x192.svg\",\n    \"revision\": \"d964dd5089ecbbf31803aadfd3e1194b\"\n  },\n  {\n    \"url\": \"registerSW.js\",\n    \"revision\": \"1872c500de691dce40960bb85481de07\"\n  },\n  {\n    \"url\": \"test-api.html\",\n    \"revision\": \"8cdccc6fe4067ae1029039ad55465455\"\n  },\n  {\n    \"url\": \"test.html\",\n    \"revision\": \"0109c48dd882219009f1fae4ee820fa7\"\n  },\n  {\n    \"url\": \"update-token.html\",\n    \"revision\": \"86ce29f32f28096f996b206944d72f8d\"\n  },\n  {\n    \"url\": \"vite.svg\",\n    \"revision\": \"458f60d72df7981af95b88885e66d2d5\"\n  },\n  {\n    \"url\": \"pwa-192x192.png\",\n    \"revision\": \"ee354b02ceb77ac70c6b8a1b2e3d7650\"\n  },\n  {\n    \"url\": \"manifest.webmanifest\",\n    \"revision\": \"1d8fe022baeb71c079f824f820d200f1\"\n  }\n], {});\nworkbox_precaching_cleanupOutdatedCaches();\nworkbox_routing_registerRoute(new workbox_routing_NavigationRoute(workbox_precaching_createHandlerBoundToURL(\"index.html\")));\n\n\nworkbox_routing_registerRoute(/^https:\\/\\/api\\.tracknew\\.com\\/.*/i, new workbox_strategies_NetworkFirst({ \"cacheName\":\"api-cache\", plugins: [new workbox_expiration_ExpirationPlugin({ maxEntries: 10, maxAgeSeconds: 604800 }), new workbox_cacheable_response_CacheableResponsePlugin({ statuses: [ 0, 200 ] })] }), 'GET');\nworkbox_routing_registerRoute(/\\.(?:js|css)$/, new workbox_strategies_StaleWhileRevalidate({ \"cacheName\":\"static-resources\", plugins: [new workbox_expiration_ExpirationPlugin({ maxEntries: 60, maxAgeSeconds: 86400 })] }), 'GET');\nworkbox_routing_registerRoute(({ request }) => request.mode === \"navigate\", new workbox_strategies_NetworkFirst({ \"cacheName\":\"pages\", plugins: [new workbox_expiration_ExpirationPlugin({ maxEntries: 50, maxAgeSeconds: 86400 })] }), 'GET');\n\n\n\n\n"], "names": ["self", "skipWaiting", "workbox_core_clientsClaim", "workbox_precaching_precacheAndRoute", "url", "revision", "workbox_precaching_cleanupOutdatedCaches", "workbox", "registerRoute", "workbox_routing_NavigationRoute", "workbox_precaching_createHandlerBoundToURL", "workbox_routing_registerRoute", "workbox_strategies_NetworkFirst", "cacheName", "plugins", "workbox_expiration_ExpirationPlugin", "maxEntries", "maxAgeSeconds", "workbox_cacheable_response_CacheableResponsePlugin", "statuses", "workbox_strategies_StaleWhileRevalidate", "request", "mode"], "mappings": "0nBA4BAA,KAAKC,cAELC,EAAAA,eAQAC,EAAAA,iBAAoC,CAClC,CACEC,IAAO,kBACPC,SAAY,oCAEd,CACED,IAAO,0CACPC,SAAY,MAEd,CACED,IAAO,+CACPC,SAAY,MAEd,CACED,IAAO,0CACPC,SAAY,MAEd,CACED,IAAO,sCACPC,SAAY,MAEd,CACED,IAAO,yCACPC,SAAY,MAEd,CACED,IAAO,0CACPC,SAAY,MAEd,CACED,IAAO,kBACPC,SAAY,oCAEd,CACED,IAAO,2BACPC,SAAY,oCAEd,CACED,IAAO,mBACPC,SAAY,oCAEd,CACED,IAAO,iBACPC,SAAY,oCAEd,CACED,IAAO,sBACPC,SAAY,oCAEd,CACED,IAAO,kBACPC,SAAY,oCAEd,CACED,IAAO,uBACPC,SAAY,oCAEd,CACED,IAAO,yBACPC,SAAY,oCAEd,CACED,IAAO,yBACPC,SAAY,oCAEd,CACED,IAAO,8BACPC,SAAY,oCAEd,CACED,IAAO,kCACPC,SAAY,oCAEd,CACED,IAAO,sCACPC,SAAY,oCAEd,CACED,IAAO,mCACPC,SAAY,oCAEd,CACED,IAAO,sCACPC,SAAY,oCAEd,CACED,IAAO,gCACPC,SAAY,oCAEd,CACED,IAAO,yCACPC,SAAY,oCAEd,CACED,IAAO,8BACPC,SAAY,oCAEd,CACED,IAAO,gCACPC,SAAY,oCAEd,CACED,IAAO,qCACPC,SAAY,oCAEd,CACED,IAAO,+BACPC,SAAY,oCAEd,CACED,IAAO,kCACPC,SAAY,oCAEd,CACED,IAAO,aACPC,SAAY,oCAEd,CACED,IAAO,2BACPC,SAAY,oCAEd,CACED,IAAO,kBACPC,SAAY,oCAEd,CACED,IAAO,kBACPC,SAAY,oCAEd,CACED,IAAO,gBACPC,SAAY,oCAEd,CACED,IAAO,gBACPC,SAAY,oCAEd,CACED,IAAO,YACPC,SAAY,oCAEd,CACED,IAAO,oBACPC,SAAY,oCAEd,CACED,IAAO,WACPC,SAAY,oCAEd,CACED,IAAO,kBACPC,SAAY,oCAEd,CACED,IAAO,uBACPC,SAAY,qCAEb,CAAE,GACLC,EAAAA,wBAC6BC,EAAAC,cAAC,IAAIC,EAAAA,gBAAgCC,EAAAA,wBAA2C,gBAG7GC,EAAAA,cAA8B,qCAAsC,IAAIC,eAAgC,CAAEC,UAAY,YAAaC,QAAS,CAAC,IAAIC,mBAAoC,CAAEC,WAAY,GAAIC,cAAe,SAAW,IAAIC,EAAAA,wBAAmD,CAAEC,SAAU,CAAE,EAAG,UAAc,OACvTR,EAAAA,cAA8B,gBAAiB,IAAIS,uBAAwC,CAAEP,UAAY,mBAAoBC,QAAS,CAAC,IAAIC,mBAAoC,CAAEC,WAAY,GAAIC,cAAe,WAAc,OAC9NN,EAAAA,eAA8B,EAAGU,aAA+B,aAAjBA,EAAQC,MAAqB,IAAIV,EAAAA,aAAgC,CAAEC,UAAY,QAASC,QAAS,CAAC,IAAIC,mBAAoC,CAAEC,WAAY,GAAIC,cAAe,WAAc"}