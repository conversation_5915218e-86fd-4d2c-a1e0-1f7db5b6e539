import { DataTypes } from 'sequelize';
import sequelize from '../config/database.js';

const CustomerCategory = sequelize.define('CustomerCategory', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  categoryName: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
    validate: {
      notEmpty: true,
      len: [1, 255],
    },
  },
  description: {
    type: DataTypes.TEXT,
  },
  color: {
    type: DataTypes.STRING,
    defaultValue: '#10B981',
    validate: {
      len: [7, 7], // Hex color format
    },
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
  },
  sortOrder: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
  },
  discountPercentage: {
    type: DataTypes.DECIMAL(5, 2),
    defaultValue: 0.00,
    validate: {
      min: 0,
      max: 100,
    },
  },
  specialTerms: {
    type: DataTypes.TEXT,
  },
}, {
  tableName: 'customer_categories',
  underscored: true,
  paranoid: false,
  indexes: [
    {
      fields: ['category_name'],
    },
    {
      fields: ['is_active'],
    },
    {
      fields: ['sort_order'],
    },
  ],
  scopes: {
    active: {
      where: {
        isActive: true,
      },
    },
    ordered: {
      order: [['sortOrder', 'ASC'], ['categoryName', 'ASC']],
    },
  },
});

// Instance methods
CustomerCategory.prototype.getDisplayName = function() {
  return this.categoryName;
};

CustomerCategory.prototype.getDiscountAmount = function(amount) {
  const discountPercentage = parseFloat(this.discountPercentage) || 0;
  const baseAmount = parseFloat(amount) || 0;
  return (baseAmount * discountPercentage) / 100;
};

// Define associations
CustomerCategory.associate = function(models) {
  // CustomerCategory has many Customers
  CustomerCategory.hasMany(models.Customer, {
    foreignKey: 'customerCategory',
    as: 'customers',
  });
};

export default CustomerCategory;
