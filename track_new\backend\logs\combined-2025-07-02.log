{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT 1+1 AS result\u001b[39m","timestamp":"2025-07-02 11:17:20:1720"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m✅ Database connection established successfully\u001b[39m","timestamp":"2025-07-02 11:17:21:1721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔧 Setting up port with automatic conflict resolution...\u001b[39m","timestamp":"2025-07-02 11:17:21:1721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔍 Finding available port...\u001b[39m","timestamp":"2025-07-02 11:17:21:1721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔧 Port 8080 is busy, attempting to free it...\u001b[39m","timestamp":"2025-07-02 11:17:21:1721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔍 Checking for processes on port 8080...\u001b[39m","timestamp":"2025-07-02 11:17:21:1721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m✅ Killed process 17632 using port 8080\u001b[39m","timestamp":"2025-07-02 11:17:21:1721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m✅ Killed process 18732 using port 8080\u001b[39m","timestamp":"2025-07-02 11:17:21:1721"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m✅ Successfully freed port 8080\u001b[39m","timestamp":"2025-07-02 11:17:23:1723"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 Port 8080 is ready for use\u001b[39m","timestamp":"2025-07-02 11:17:23:1723"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 Server running on port 8080\u001b[39m","timestamp":"2025-07-02 11:17:23:1723"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📚 Environment: development\u001b[39m","timestamp":"2025-07-02 11:17:23:1723"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🌐 Frontend & Backend: http://localhost:8080\u001b[39m","timestamp":"2025-07-02 11:17:23:1723"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 API Base URL: http://localhost:8080/api\u001b[39m","timestamp":"2025-07-02 11:17:23:1723"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📖 API Documentation: http://localhost:8080/api/docs\u001b[39m","timestamp":"2025-07-02 11:17:23:1723"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🏥 Health Check: http://localhost:8080/health\u001b[39m","timestamp":"2025-07-02 11:17:23:1723"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m✅ TrackNew is ready for use!\u001b[39m","timestamp":"2025-07-02 11:17:23:1723"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m{\u001b[39m\n\u001b[31m  \"message\": \"Invalid token.\",\u001b[39m\n\u001b[31m  \"stack\": \"Error: Invalid token.\\n    at file:///D:/New_TrackNew/track_new/backend/src/middleware/auth.js:93:13\\n    at file:///D:/New_TrackNew/track_new/backend/src/utils/asyncHandler.js:7:21\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\\n    at trim_prefix (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:328:13)\\n    at D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:286:9\\n    at Function.process_params (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:346:12)\\n    at next (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:280:10)\\n    at Function.handle (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:175:3)\\n    at router (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:47:12)\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\",\u001b[39m\n\u001b[31m  \"statusCode\": 401,\u001b[39m\n\u001b[31m  \"timestamp\": \"2025-07-02T05:47:56.916Z\",\u001b[39m\n\u001b[31m  \"request\": {\u001b[39m\n\u001b[31m    \"method\": \"GET\",\u001b[39m\n\u001b[31m    \"url\": \"/api/service-categories?page=1&limit=12\",\u001b[39m\n\u001b[31m    \"ip\": \"::1\",\u001b[39m\n\u001b[31m    \"userAgent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"\u001b[39m\n\u001b[31m  }\u001b[39m\n\u001b[31m}\u001b[39m","timestamp":"2025-07-02 11:17:56:1756"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m{\u001b[39m\n\u001b[31m  \"message\": \"Invalid token.\",\u001b[39m\n\u001b[31m  \"stack\": \"Error: Invalid token.\\n    at file:///D:/New_TrackNew/track_new/backend/src/middleware/auth.js:93:13\\n    at file:///D:/New_TrackNew/track_new/backend/src/utils/asyncHandler.js:7:21\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\\n    at trim_prefix (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:328:13)\\n    at D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:286:9\\n    at Function.process_params (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:346:12)\\n    at next (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:280:10)\\n    at Function.handle (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:175:3)\\n    at router (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:47:12)\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\",\u001b[39m\n\u001b[31m  \"statusCode\": 401,\u001b[39m\n\u001b[31m  \"timestamp\": \"2025-07-02T05:47:56.971Z\",\u001b[39m\n\u001b[31m  \"request\": {\u001b[39m\n\u001b[31m    \"method\": \"GET\",\u001b[39m\n\u001b[31m    \"url\": \"/api/auth/profile\",\u001b[39m\n\u001b[31m    \"ip\": \"::1\",\u001b[39m\n\u001b[31m    \"userAgent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"\u001b[39m\n\u001b[31m  }\u001b[39m\n\u001b[31m}\u001b[39m","timestamp":"2025-07-02 11:17:57:1757"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m{\u001b[39m\n\u001b[31m  \"message\": \"Invalid token.\",\u001b[39m\n\u001b[31m  \"stack\": \"Error: Invalid token.\\n    at file:///D:/New_TrackNew/track_new/backend/src/middleware/auth.js:93:13\\n    at file:///D:/New_TrackNew/track_new/backend/src/utils/asyncHandler.js:7:21\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\\n    at trim_prefix (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:328:13)\\n    at D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:286:9\\n    at Function.process_params (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:346:12)\\n    at next (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:280:10)\\n    at Function.handle (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:175:3)\\n    at router (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:47:12)\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\",\u001b[39m\n\u001b[31m  \"statusCode\": 401,\u001b[39m\n\u001b[31m  \"timestamp\": \"2025-07-02T05:49:36.505Z\",\u001b[39m\n\u001b[31m  \"request\": {\u001b[39m\n\u001b[31m    \"method\": \"GET\",\u001b[39m\n\u001b[31m    \"url\": \"/api/service-categories?limit=1\",\u001b[39m\n\u001b[31m    \"ip\": \"::1\",\u001b[39m\n\u001b[31m    \"userAgent\": \"axios/1.10.0\"\u001b[39m\n\u001b[31m  }\u001b[39m\n\u001b[31m}\u001b[39m","timestamp":"2025-07-02 11:19:36:1936"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m{\u001b[39m\n\u001b[31m  \"message\": \"Invalid token.\",\u001b[39m\n\u001b[31m  \"stack\": \"Error: Invalid token.\\n    at file:///D:/New_TrackNew/track_new/backend/src/middleware/auth.js:93:13\\n    at file:///D:/New_TrackNew/track_new/backend/src/utils/asyncHandler.js:7:21\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\\n    at trim_prefix (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:328:13)\\n    at D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:286:9\\n    at Function.process_params (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:346:12)\\n    at next (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:280:10)\\n    at Function.handle (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:175:3)\\n    at router (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:47:12)\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\",\u001b[39m\n\u001b[31m  \"statusCode\": 401,\u001b[39m\n\u001b[31m  \"timestamp\": \"2025-07-02T05:49:36.527Z\",\u001b[39m\n\u001b[31m  \"request\": {\u001b[39m\n\u001b[31m    \"method\": \"GET\",\u001b[39m\n\u001b[31m    \"url\": \"/api/service-categories\",\u001b[39m\n\u001b[31m    \"ip\": \"::1\",\u001b[39m\n\u001b[31m    \"userAgent\": \"axios/1.10.0\"\u001b[39m\n\u001b[31m  }\u001b[39m\n\u001b[31m}\u001b[39m","timestamp":"2025-07-02 11:19:36:1936"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m{\u001b[39m\n\u001b[31m  \"message\": \"Invalid token.\",\u001b[39m\n\u001b[31m  \"stack\": \"Error: Invalid token.\\n    at file:///D:/New_TrackNew/track_new/backend/src/middleware/auth.js:93:13\\n    at file:///D:/New_TrackNew/track_new/backend/src/utils/asyncHandler.js:7:21\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\\n    at trim_prefix (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:328:13)\\n    at D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:286:9\\n    at Function.process_params (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:346:12)\\n    at next (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:280:10)\\n    at Function.handle (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:175:3)\\n    at router (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:47:12)\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\",\u001b[39m\n\u001b[31m  \"statusCode\": 401,\u001b[39m\n\u001b[31m  \"timestamp\": \"2025-07-02T05:49:36.554Z\",\u001b[39m\n\u001b[31m  \"request\": {\u001b[39m\n\u001b[31m    \"method\": \"GET\",\u001b[39m\n\u001b[31m    \"url\": \"/api/customer-categories\",\u001b[39m\n\u001b[31m    \"ip\": \"::1\",\u001b[39m\n\u001b[31m    \"userAgent\": \"axios/1.10.0\"\u001b[39m\n\u001b[31m  }\u001b[39m\n\u001b[31m}\u001b[39m","timestamp":"2025-07-02 11:19:36:1936"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m{\u001b[39m\n\u001b[31m  \"message\": \"Invalid token.\",\u001b[39m\n\u001b[31m  \"stack\": \"Error: Invalid token.\\n    at file:///D:/New_TrackNew/track_new/backend/src/middleware/auth.js:93:13\\n    at file:///D:/New_TrackNew/track_new/backend/src/utils/asyncHandler.js:7:21\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\\n    at trim_prefix (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:328:13)\\n    at D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:286:9\\n    at Function.process_params (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:346:12)\\n    at next (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:280:10)\\n    at Function.handle (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:175:3)\\n    at router (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:47:12)\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\",\u001b[39m\n\u001b[31m  \"statusCode\": 401,\u001b[39m\n\u001b[31m  \"timestamp\": \"2025-07-02T05:49:36.573Z\",\u001b[39m\n\u001b[31m  \"request\": {\u001b[39m\n\u001b[31m    \"method\": \"GET\",\u001b[39m\n\u001b[31m    \"url\": \"/api/service-categories?search=test\",\u001b[39m\n\u001b[31m    \"ip\": \"::1\",\u001b[39m\n\u001b[31m    \"userAgent\": \"axios/1.10.0\"\u001b[39m\n\u001b[31m  }\u001b[39m\n\u001b[31m}\u001b[39m","timestamp":"2025-07-02 11:19:36:1936"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m{\u001b[39m\n\u001b[31m  \"message\": \"Access denied. No token provided.\",\u001b[39m\n\u001b[31m  \"stack\": \"Error: Access denied. No token provided.\\n    at file:///D:/New_TrackNew/track_new/backend/src/middleware/auth.js:43:11\\n    at file:///D:/New_TrackNew/track_new/backend/src/utils/asyncHandler.js:7:21\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\\n    at trim_prefix (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:328:13)\\n    at D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:286:9\\n    at Function.process_params (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:346:12)\\n    at next (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:280:10)\\n    at Function.handle (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:175:3)\\n    at router (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:47:12)\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\",\u001b[39m\n\u001b[31m  \"statusCode\": 401,\u001b[39m\n\u001b[31m  \"timestamp\": \"2025-07-02T05:49:57.058Z\",\u001b[39m\n\u001b[31m  \"request\": {\u001b[39m\n\u001b[31m    \"method\": \"GET\",\u001b[39m\n\u001b[31m    \"url\": \"/api/service-categories?limit=5\",\u001b[39m\n\u001b[31m    \"ip\": \"::1\",\u001b[39m\n\u001b[31m    \"userAgent\": \"curl/8.12.1\"\u001b[39m\n\u001b[31m  }\u001b[39m\n\u001b[31m}\u001b[39m","timestamp":"2025-07-02 11:19:57:1957"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"name\", \"User\".\"email\", \"User\".\"password\", \"User\".\"user_name\" AS \"userName\", \"User\".\"user_type\" AS \"userType\", \"User\".\"mobile_number\" AS \"mobileNumber\", \"User\".\"status\", \"User\".\"date_of_birth\" AS \"dateOfBirth\", \"User\".\"skills\", \"User\".\"total_experience\" AS \"totalExperience\", \"User\".\"avatar\", \"User\".\"proof\", \"User\".\"address\", \"User\".\"notes\", \"User\".\"fcm_token\" AS \"fcmToken\", \"User\".\"last_notification_seen\" AS \"lastNotificationSeen\", \"User\".\"plan_id\" AS \"planId\", \"User\".\"will_expire\" AS \"willExpire\", \"User\".\"message_limit\" AS \"messageLimit\", \"User\".\"is_verified\" AS \"isVerified\", \"User\".\"email_verified_at\" AS \"emailVerifiedAt\", \"User\".\"otp\", \"User\".\"otp_expires_at\" AS \"otpExpiresAt\", \"User\".\"login_attempts\" AS \"loginAttempts\", \"User\".\"locked_until\" AS \"lockedUntil\", \"User\".\"last_login_at\" AS \"lastLoginAt\", \"User\".\"last_login_ip\" AS \"lastLoginIp\", \"User\".\"company_id\" AS \"companyId\", \"User\".\"created_at\" AS \"createdAt\", \"User\".\"updated_at\" AS \"updatedAt\", \"User\".\"deleted_at\" AS \"deletedAt\", \"User\".\"department_id\" AS \"departmentId\", \"company\".\"id\" AS \"company.id\", \"company\".\"name\" AS \"company.name\", \"company\".\"status\" AS \"company.status\", \"company\".\"features\" AS \"company.features\", \"company\".\"subscription_end_date\" AS \"company.subscriptionEndDate\" FROM \"users\" AS \"User\" LEFT OUTER JOIN \"companies\" AS \"company\" ON \"User\".\"company_id\" = \"company\".\"id\" AND (\"company\".\"deleted_at\" IS NULL) WHERE (\"User\".\"deleted_at\" IS NULL AND \"User\".\"email\" = '<EMAIL>');\u001b[39m","timestamp":"2025-07-02 11:20:36:2036"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): UPDATE \"users\" SET \"last_login_at\"=$1,\"updated_at\"=$2 WHERE \"id\" = $3\u001b[39m","timestamp":"2025-07-02 11:20:37:2037"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"name\", \"User\".\"email\", \"User\".\"password\", \"User\".\"user_name\" AS \"userName\", \"User\".\"user_type\" AS \"userType\", \"User\".\"mobile_number\" AS \"mobileNumber\", \"User\".\"status\", \"User\".\"date_of_birth\" AS \"dateOfBirth\", \"User\".\"skills\", \"User\".\"total_experience\" AS \"totalExperience\", \"User\".\"avatar\", \"User\".\"proof\", \"User\".\"address\", \"User\".\"notes\", \"User\".\"fcm_token\" AS \"fcmToken\", \"User\".\"last_notification_seen\" AS \"lastNotificationSeen\", \"User\".\"plan_id\" AS \"planId\", \"User\".\"will_expire\" AS \"willExpire\", \"User\".\"message_limit\" AS \"messageLimit\", \"User\".\"is_verified\" AS \"isVerified\", \"User\".\"email_verified_at\" AS \"emailVerifiedAt\", \"User\".\"otp\", \"User\".\"otp_expires_at\" AS \"otpExpiresAt\", \"User\".\"login_attempts\" AS \"loginAttempts\", \"User\".\"locked_until\" AS \"lockedUntil\", \"User\".\"last_login_at\" AS \"lastLoginAt\", \"User\".\"last_login_ip\" AS \"lastLoginIp\", \"User\".\"company_id\" AS \"companyId\", \"User\".\"created_at\" AS \"createdAt\", \"User\".\"updated_at\" AS \"updatedAt\", \"User\".\"deleted_at\" AS \"deletedAt\", \"User\".\"department_id\" AS \"departmentId\", \"company\".\"id\" AS \"company.id\", \"company\".\"name\" AS \"company.name\", \"company\".\"status\" AS \"company.status\", \"company\".\"features\" AS \"company.features\", \"company\".\"subscription_end_date\" AS \"company.subscriptionEndDate\" FROM \"users\" AS \"User\" LEFT OUTER JOIN \"companies\" AS \"company\" ON \"User\".\"company_id\" = \"company\".\"id\" AND (\"company\".\"deleted_at\" IS NULL) WHERE (\"User\".\"deleted_at\" IS NULL AND \"User\".\"email\" = '<EMAIL>');\u001b[39m","timestamp":"2025-07-02 11:33:32:3332"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): UPDATE \"users\" SET \"last_login_at\"=$1,\"updated_at\"=$2 WHERE \"id\" = $3\u001b[39m","timestamp":"2025-07-02 11:33:33:3333"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"name\", \"User\".\"email\", \"User\".\"password\", \"User\".\"user_name\" AS \"userName\", \"User\".\"user_type\" AS \"userType\", \"User\".\"mobile_number\" AS \"mobileNumber\", \"User\".\"status\", \"User\".\"date_of_birth\" AS \"dateOfBirth\", \"User\".\"skills\", \"User\".\"total_experience\" AS \"totalExperience\", \"User\".\"avatar\", \"User\".\"proof\", \"User\".\"address\", \"User\".\"notes\", \"User\".\"fcm_token\" AS \"fcmToken\", \"User\".\"last_notification_seen\" AS \"lastNotificationSeen\", \"User\".\"plan_id\" AS \"planId\", \"User\".\"will_expire\" AS \"willExpire\", \"User\".\"message_limit\" AS \"messageLimit\", \"User\".\"is_verified\" AS \"isVerified\", \"User\".\"email_verified_at\" AS \"emailVerifiedAt\", \"User\".\"otp\", \"User\".\"otp_expires_at\" AS \"otpExpiresAt\", \"User\".\"login_attempts\" AS \"loginAttempts\", \"User\".\"locked_until\" AS \"lockedUntil\", \"User\".\"last_login_at\" AS \"lastLoginAt\", \"User\".\"last_login_ip\" AS \"lastLoginIp\", \"User\".\"company_id\" AS \"companyId\", \"User\".\"created_at\" AS \"createdAt\", \"User\".\"updated_at\" AS \"updatedAt\", \"User\".\"deleted_at\" AS \"deletedAt\", \"User\".\"department_id\" AS \"departmentId\", \"company\".\"id\" AS \"company.id\", \"company\".\"name\" AS \"company.name\", \"company\".\"status\" AS \"company.status\", \"company\".\"features\" AS \"company.features\", \"company\".\"subscription_end_date\" AS \"company.subscriptionEndDate\" FROM \"users\" AS \"User\" LEFT OUTER JOIN \"companies\" AS \"company\" ON \"User\".\"company_id\" = \"company\".\"id\" AND (\"company\".\"deleted_at\" IS NULL) WHERE (\"User\".\"deleted_at\" IS NULL AND \"User\".\"email\" = '<EMAIL>');\u001b[39m","timestamp":"2025-07-02 11:33:55:3355"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): UPDATE \"users\" SET \"last_login_at\"=$1,\"updated_at\"=$2 WHERE \"id\" = $3\u001b[39m","timestamp":"2025-07-02 11:33:56:3356"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"name\", \"User\".\"email\", \"User\".\"password\", \"User\".\"user_name\" AS \"userName\", \"User\".\"user_type\" AS \"userType\", \"User\".\"mobile_number\" AS \"mobileNumber\", \"User\".\"status\", \"User\".\"date_of_birth\" AS \"dateOfBirth\", \"User\".\"skills\", \"User\".\"total_experience\" AS \"totalExperience\", \"User\".\"avatar\", \"User\".\"proof\", \"User\".\"address\", \"User\".\"notes\", \"User\".\"fcm_token\" AS \"fcmToken\", \"User\".\"last_notification_seen\" AS \"lastNotificationSeen\", \"User\".\"plan_id\" AS \"planId\", \"User\".\"will_expire\" AS \"willExpire\", \"User\".\"message_limit\" AS \"messageLimit\", \"User\".\"is_verified\" AS \"isVerified\", \"User\".\"email_verified_at\" AS \"emailVerifiedAt\", \"User\".\"otp\", \"User\".\"otp_expires_at\" AS \"otpExpiresAt\", \"User\".\"login_attempts\" AS \"loginAttempts\", \"User\".\"locked_until\" AS \"lockedUntil\", \"User\".\"last_login_at\" AS \"lastLoginAt\", \"User\".\"last_login_ip\" AS \"lastLoginIp\", \"User\".\"company_id\" AS \"companyId\", \"User\".\"created_at\" AS \"createdAt\", \"User\".\"updated_at\" AS \"updatedAt\", \"User\".\"deleted_at\" AS \"deletedAt\", \"User\".\"department_id\" AS \"departmentId\", \"company\".\"id\" AS \"company.id\", \"company\".\"name\" AS \"company.name\", \"company\".\"status\" AS \"company.status\", \"company\".\"features\" AS \"company.features\", \"company\".\"subscription_end_date\" AS \"company.subscriptionEndDate\" FROM \"users\" AS \"User\" LEFT OUTER JOIN \"companies\" AS \"company\" ON \"User\".\"company_id\" = \"company\".\"id\" AND (\"company\".\"deleted_at\" IS NULL) WHERE (\"User\".\"deleted_at\" IS NULL AND \"User\".\"email\" = '<EMAIL>');\u001b[39m","timestamp":"2025-07-02 11:34:21:3421"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): UPDATE \"users\" SET \"last_login_at\"=$1,\"updated_at\"=$2 WHERE \"id\" = $3\u001b[39m","timestamp":"2025-07-02 11:34:21:3421"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"name\", \"User\".\"email\", \"User\".\"user_name\" AS \"userName\", \"User\".\"user_type\" AS \"userType\", \"User\".\"mobile_number\" AS \"mobileNumber\", \"User\".\"status\", \"User\".\"date_of_birth\" AS \"dateOfBirth\", \"User\".\"skills\", \"User\".\"total_experience\" AS \"totalExperience\", \"User\".\"avatar\", \"User\".\"proof\", \"User\".\"address\", \"User\".\"notes\", \"User\".\"fcm_token\" AS \"fcmToken\", \"User\".\"last_notification_seen\" AS \"lastNotificationSeen\", \"User\".\"plan_id\" AS \"planId\", \"User\".\"will_expire\" AS \"willExpire\", \"User\".\"message_limit\" AS \"messageLimit\", \"User\".\"is_verified\" AS \"isVerified\", \"User\".\"email_verified_at\" AS \"emailVerifiedAt\", \"User\".\"otp\", \"User\".\"otp_expires_at\" AS \"otpExpiresAt\", \"User\".\"login_attempts\" AS \"loginAttempts\", \"User\".\"locked_until\" AS \"lockedUntil\", \"User\".\"last_login_at\" AS \"lastLoginAt\", \"User\".\"last_login_ip\" AS \"lastLoginIp\", \"User\".\"company_id\" AS \"companyId\", \"User\".\"created_at\" AS \"createdAt\", \"User\".\"updated_at\" AS \"updatedAt\", \"User\".\"deleted_at\" AS \"deletedAt\", \"company\".\"id\" AS \"company.id\", \"company\".\"name\" AS \"company.name\", \"company\".\"status\" AS \"company.status\", \"company\".\"features\" AS \"company.features\", \"company\".\"subscription_end_date\" AS \"company.subscriptionEndDate\" FROM \"users\" AS \"User\" LEFT OUTER JOIN \"companies\" AS \"company\" ON \"User\".\"company_id\" = \"company\".\"id\" AND (\"company\".\"deleted_at\" IS NULL) WHERE (\"User\".\"deleted_at\" IS NULL AND \"User\".\"id\" = 'fce8b418-d90b-4f18-a0e0-40347e7c62d6');\u001b[39m","timestamp":"2025-07-02 11:34:21:3421"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"name\", \"User\".\"email\", \"User\".\"password\", \"User\".\"user_name\" AS \"userName\", \"User\".\"user_type\" AS \"userType\", \"User\".\"mobile_number\" AS \"mobileNumber\", \"User\".\"status\", \"User\".\"date_of_birth\" AS \"dateOfBirth\", \"User\".\"skills\", \"User\".\"total_experience\" AS \"totalExperience\", \"User\".\"avatar\", \"User\".\"proof\", \"User\".\"address\", \"User\".\"notes\", \"User\".\"fcm_token\" AS \"fcmToken\", \"User\".\"last_notification_seen\" AS \"lastNotificationSeen\", \"User\".\"plan_id\" AS \"planId\", \"User\".\"will_expire\" AS \"willExpire\", \"User\".\"message_limit\" AS \"messageLimit\", \"User\".\"is_verified\" AS \"isVerified\", \"User\".\"email_verified_at\" AS \"emailVerifiedAt\", \"User\".\"otp\", \"User\".\"otp_expires_at\" AS \"otpExpiresAt\", \"User\".\"login_attempts\" AS \"loginAttempts\", \"User\".\"locked_until\" AS \"lockedUntil\", \"User\".\"last_login_at\" AS \"lastLoginAt\", \"User\".\"last_login_ip\" AS \"lastLoginIp\", \"User\".\"company_id\" AS \"companyId\", \"User\".\"created_at\" AS \"createdAt\", \"User\".\"updated_at\" AS \"updatedAt\", \"User\".\"deleted_at\" AS \"deletedAt\", \"User\".\"department_id\" AS \"departmentId\", \"company\".\"id\" AS \"company.id\", \"company\".\"name\" AS \"company.name\", \"company\".\"status\" AS \"company.status\", \"company\".\"features\" AS \"company.features\", \"company\".\"subscription_end_date\" AS \"company.subscriptionEndDate\" FROM \"users\" AS \"User\" LEFT OUTER JOIN \"companies\" AS \"company\" ON \"User\".\"company_id\" = \"company\".\"id\" AND (\"company\".\"deleted_at\" IS NULL) WHERE (\"User\".\"deleted_at\" IS NULL AND \"User\".\"email\" = '<EMAIL>');\u001b[39m","timestamp":"2025-07-02 11:35:26:3526"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): UPDATE \"users\" SET \"last_login_at\"=$1,\"updated_at\"=$2 WHERE \"id\" = $3\u001b[39m","timestamp":"2025-07-02 11:35:27:3527"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"name\", \"User\".\"email\", \"User\".\"user_name\" AS \"userName\", \"User\".\"user_type\" AS \"userType\", \"User\".\"mobile_number\" AS \"mobileNumber\", \"User\".\"status\", \"User\".\"date_of_birth\" AS \"dateOfBirth\", \"User\".\"skills\", \"User\".\"total_experience\" AS \"totalExperience\", \"User\".\"avatar\", \"User\".\"proof\", \"User\".\"address\", \"User\".\"notes\", \"User\".\"fcm_token\" AS \"fcmToken\", \"User\".\"last_notification_seen\" AS \"lastNotificationSeen\", \"User\".\"plan_id\" AS \"planId\", \"User\".\"will_expire\" AS \"willExpire\", \"User\".\"message_limit\" AS \"messageLimit\", \"User\".\"is_verified\" AS \"isVerified\", \"User\".\"email_verified_at\" AS \"emailVerifiedAt\", \"User\".\"otp\", \"User\".\"otp_expires_at\" AS \"otpExpiresAt\", \"User\".\"login_attempts\" AS \"loginAttempts\", \"User\".\"locked_until\" AS \"lockedUntil\", \"User\".\"last_login_at\" AS \"lastLoginAt\", \"User\".\"last_login_ip\" AS \"lastLoginIp\", \"User\".\"company_id\" AS \"companyId\", \"User\".\"created_at\" AS \"createdAt\", \"User\".\"updated_at\" AS \"updatedAt\", \"User\".\"deleted_at\" AS \"deletedAt\", \"company\".\"id\" AS \"company.id\", \"company\".\"name\" AS \"company.name\", \"company\".\"status\" AS \"company.status\", \"company\".\"features\" AS \"company.features\", \"company\".\"subscription_end_date\" AS \"company.subscriptionEndDate\" FROM \"users\" AS \"User\" LEFT OUTER JOIN \"companies\" AS \"company\" ON \"User\".\"company_id\" = \"company\".\"id\" AND (\"company\".\"deleted_at\" IS NULL) WHERE (\"User\".\"deleted_at\" IS NULL AND \"User\".\"id\" = 'fce8b418-d90b-4f18-a0e0-40347e7c62d6');\u001b[39m","timestamp":"2025-07-02 11:35:27:3527"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"ServiceCategory\".\"id\", count(\"ServiceCategory\".\"id\") AS \"count\" FROM \"service_categories\" AS \"ServiceCategory\" LEFT OUTER JOIN \"services\" AS \"services\" ON \"ServiceCategory\".\"id\" = \"services\".\"service_category_id\" AND (\"services\".\"deleted_at\" IS NULL) WHERE (\"ServiceCategory\".\"deleted_at\" IS NULL AND \"ServiceCategory\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') GROUP BY \"ServiceCategory\".\"id\";\u001b[39m","timestamp":"2025-07-02 11:35:28:3528"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"ServiceCategory\".\"id\", \"ServiceCategory\".\"service_category\" AS \"serviceCategory\", \"ServiceCategory\".\"service_status\" AS \"serviceStatus\", \"ServiceCategory\".\"style_view\" AS \"styleView\", \"ServiceCategory\".\"form\", \"ServiceCategory\".\"description\", \"ServiceCategory\".\"icon\", \"ServiceCategory\".\"color\", \"ServiceCategory\".\"sort_order\" AS \"sortOrder\", \"ServiceCategory\".\"is_default\" AS \"isDefault\", \"ServiceCategory\".\"created_at\" AS \"createdAt\", \"ServiceCategory\".\"updated_at\" AS \"updatedAt\", COUNT(\"services\".\"id\") AS \"servicesCount\" FROM \"service_categories\" AS \"ServiceCategory\" LEFT OUTER JOIN \"services\" AS \"services\" ON \"ServiceCategory\".\"id\" = \"services\".\"service_category_id\" AND (\"services\".\"deleted_at\" IS NULL) WHERE (\"ServiceCategory\".\"deleted_at\" IS NULL AND \"ServiceCategory\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') GROUP BY \"ServiceCategory\".\"id\" ORDER BY \"sortOrder\" ASC, \"serviceCategory\" ASC LIMIT 5 OFFSET 0;\u001b[39m","timestamp":"2025-07-02 11:35:29:3529"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"name\", \"User\".\"email\", \"User\".\"user_name\" AS \"userName\", \"User\".\"user_type\" AS \"userType\", \"User\".\"mobile_number\" AS \"mobileNumber\", \"User\".\"status\", \"User\".\"date_of_birth\" AS \"dateOfBirth\", \"User\".\"skills\", \"User\".\"total_experience\" AS \"totalExperience\", \"User\".\"avatar\", \"User\".\"proof\", \"User\".\"address\", \"User\".\"notes\", \"User\".\"fcm_token\" AS \"fcmToken\", \"User\".\"last_notification_seen\" AS \"lastNotificationSeen\", \"User\".\"plan_id\" AS \"planId\", \"User\".\"will_expire\" AS \"willExpire\", \"User\".\"message_limit\" AS \"messageLimit\", \"User\".\"is_verified\" AS \"isVerified\", \"User\".\"email_verified_at\" AS \"emailVerifiedAt\", \"User\".\"otp\", \"User\".\"otp_expires_at\" AS \"otpExpiresAt\", \"User\".\"login_attempts\" AS \"loginAttempts\", \"User\".\"locked_until\" AS \"lockedUntil\", \"User\".\"last_login_at\" AS \"lastLoginAt\", \"User\".\"last_login_ip\" AS \"lastLoginIp\", \"User\".\"company_id\" AS \"companyId\", \"User\".\"created_at\" AS \"createdAt\", \"User\".\"updated_at\" AS \"updatedAt\", \"User\".\"deleted_at\" AS \"deletedAt\", \"company\".\"id\" AS \"company.id\", \"company\".\"name\" AS \"company.name\", \"company\".\"status\" AS \"company.status\", \"company\".\"features\" AS \"company.features\", \"company\".\"subscription_end_date\" AS \"company.subscriptionEndDate\" FROM \"users\" AS \"User\" LEFT OUTER JOIN \"companies\" AS \"company\" ON \"User\".\"company_id\" = \"company\".\"id\" AND (\"company\".\"deleted_at\" IS NULL) WHERE (\"User\".\"deleted_at\" IS NULL AND \"User\".\"id\" = 'fce8b418-d90b-4f18-a0e0-40347e7c62d6');\u001b[39m","timestamp":"2025-07-02 11:35:29:3529"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"name\", \"User\".\"email\", \"User\".\"user_name\" AS \"userName\", \"User\".\"user_type\" AS \"userType\", \"User\".\"mobile_number\" AS \"mobileNumber\", \"User\".\"status\", \"User\".\"date_of_birth\" AS \"dateOfBirth\", \"User\".\"skills\", \"User\".\"total_experience\" AS \"totalExperience\", \"User\".\"avatar\", \"User\".\"proof\", \"User\".\"address\", \"User\".\"notes\", \"User\".\"fcm_token\" AS \"fcmToken\", \"User\".\"last_notification_seen\" AS \"lastNotificationSeen\", \"User\".\"plan_id\" AS \"planId\", \"User\".\"will_expire\" AS \"willExpire\", \"User\".\"message_limit\" AS \"messageLimit\", \"User\".\"is_verified\" AS \"isVerified\", \"User\".\"email_verified_at\" AS \"emailVerifiedAt\", \"User\".\"otp\", \"User\".\"otp_expires_at\" AS \"otpExpiresAt\", \"User\".\"login_attempts\" AS \"loginAttempts\", \"User\".\"locked_until\" AS \"lockedUntil\", \"User\".\"last_login_at\" AS \"lastLoginAt\", \"User\".\"last_login_ip\" AS \"lastLoginIp\", \"User\".\"company_id\" AS \"companyId\", \"User\".\"created_at\" AS \"createdAt\", \"User\".\"updated_at\" AS \"updatedAt\", \"User\".\"deleted_at\" AS \"deletedAt\", \"company\".\"id\" AS \"company.id\", \"company\".\"name\" AS \"company.name\", \"company\".\"status\" AS \"company.status\", \"company\".\"features\" AS \"company.features\", \"company\".\"subscription_end_date\" AS \"company.subscriptionEndDate\" FROM \"users\" AS \"User\" LEFT OUTER JOIN \"companies\" AS \"company\" ON \"User\".\"company_id\" = \"company\".\"id\" AND (\"company\".\"deleted_at\" IS NULL) WHERE (\"User\".\"deleted_at\" IS NULL AND \"User\".\"id\" = 'fce8b418-d90b-4f18-a0e0-40347e7c62d6');\u001b[39m","timestamp":"2025-07-02 11:35:29:3529"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"name\", \"User\".\"email\", \"User\".\"user_name\" AS \"userName\", \"User\".\"user_type\" AS \"userType\", \"User\".\"mobile_number\" AS \"mobileNumber\", \"User\".\"status\", \"User\".\"date_of_birth\" AS \"dateOfBirth\", \"User\".\"skills\", \"User\".\"total_experience\" AS \"totalExperience\", \"User\".\"avatar\", \"User\".\"proof\", \"User\".\"address\", \"User\".\"notes\", \"User\".\"fcm_token\" AS \"fcmToken\", \"User\".\"last_notification_seen\" AS \"lastNotificationSeen\", \"User\".\"plan_id\" AS \"planId\", \"User\".\"will_expire\" AS \"willExpire\", \"User\".\"message_limit\" AS \"messageLimit\", \"User\".\"is_verified\" AS \"isVerified\", \"User\".\"email_verified_at\" AS \"emailVerifiedAt\", \"User\".\"otp\", \"User\".\"otp_expires_at\" AS \"otpExpiresAt\", \"User\".\"login_attempts\" AS \"loginAttempts\", \"User\".\"locked_until\" AS \"lockedUntil\", \"User\".\"last_login_at\" AS \"lastLoginAt\", \"User\".\"last_login_ip\" AS \"lastLoginIp\", \"User\".\"company_id\" AS \"companyId\", \"User\".\"created_at\" AS \"createdAt\", \"User\".\"updated_at\" AS \"updatedAt\", \"User\".\"deleted_at\" AS \"deletedAt\", \"company\".\"id\" AS \"company.id\", \"company\".\"name\" AS \"company.name\", \"company\".\"status\" AS \"company.status\", \"company\".\"features\" AS \"company.features\", \"company\".\"subscription_end_date\" AS \"company.subscriptionEndDate\" FROM \"users\" AS \"User\" LEFT OUTER JOIN \"companies\" AS \"company\" ON \"User\".\"company_id\" = \"company\".\"id\" AND (\"company\".\"deleted_at\" IS NULL) WHERE (\"User\".\"deleted_at\" IS NULL AND \"User\".\"id\" = 'fce8b418-d90b-4f18-a0e0-40347e7c62d6');\u001b[39m","timestamp":"2025-07-02 11:35:30:3530"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"id\", \"service_category\" AS \"serviceCategory\", \"service_status\" AS \"serviceStatus\", \"style_view\" AS \"styleView\", \"form\", \"description\", \"icon\", \"color\", \"sort_order\" AS \"sortOrder\", \"is_default\" AS \"isDefault\", \"company_id\" AS \"companyId\", \"created_by\" AS \"createdBy\", \"updated_by\" AS \"updatedBy\", \"created_at\" AS \"createdAt\", \"updated_at\" AS \"updatedAt\", \"deleted_at\" AS \"deletedAt\" FROM \"service_categories\" AS \"ServiceCategory\" WHERE (\"ServiceCategory\".\"deleted_at\" IS NULL AND (\"ServiceCategory\".\"service_category\" = 'Test Service Category' AND \"ServiceCategory\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae')) LIMIT 1;\u001b[39m","timestamp":"2025-07-02 11:35:31:3531"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): INSERT INTO \"service_categories\" (\"id\",\"service_category\",\"service_status\",\"style_view\",\"form\",\"description\",\"color\",\"sort_order\",\"is_default\",\"company_id\",\"created_by\",\"created_at\",\"updated_at\") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12,$13) RETURNING \"id\",\"service_category\",\"service_status\",\"style_view\",\"form\",\"description\",\"icon\",\"color\",\"sort_order\",\"is_default\",\"company_id\",\"created_by\",\"updated_by\",\"created_at\",\"updated_at\",\"deleted_at\";\u001b[39m","timestamp":"2025-07-02 11:35:31:3531"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"name\", \"User\".\"email\", \"User\".\"user_name\" AS \"userName\", \"User\".\"user_type\" AS \"userType\", \"User\".\"mobile_number\" AS \"mobileNumber\", \"User\".\"status\", \"User\".\"date_of_birth\" AS \"dateOfBirth\", \"User\".\"skills\", \"User\".\"total_experience\" AS \"totalExperience\", \"User\".\"avatar\", \"User\".\"proof\", \"User\".\"address\", \"User\".\"notes\", \"User\".\"fcm_token\" AS \"fcmToken\", \"User\".\"last_notification_seen\" AS \"lastNotificationSeen\", \"User\".\"plan_id\" AS \"planId\", \"User\".\"will_expire\" AS \"willExpire\", \"User\".\"message_limit\" AS \"messageLimit\", \"User\".\"is_verified\" AS \"isVerified\", \"User\".\"email_verified_at\" AS \"emailVerifiedAt\", \"User\".\"otp\", \"User\".\"otp_expires_at\" AS \"otpExpiresAt\", \"User\".\"login_attempts\" AS \"loginAttempts\", \"User\".\"locked_until\" AS \"lockedUntil\", \"User\".\"last_login_at\" AS \"lastLoginAt\", \"User\".\"last_login_ip\" AS \"lastLoginIp\", \"User\".\"company_id\" AS \"companyId\", \"User\".\"created_at\" AS \"createdAt\", \"User\".\"updated_at\" AS \"updatedAt\", \"User\".\"deleted_at\" AS \"deletedAt\", \"company\".\"id\" AS \"company.id\", \"company\".\"name\" AS \"company.name\", \"company\".\"status\" AS \"company.status\", \"company\".\"features\" AS \"company.features\", \"company\".\"subscription_end_date\" AS \"company.subscriptionEndDate\" FROM \"users\" AS \"User\" LEFT OUTER JOIN \"companies\" AS \"company\" ON \"User\".\"company_id\" = \"company\".\"id\" AND (\"company\".\"deleted_at\" IS NULL) WHERE (\"User\".\"deleted_at\" IS NULL AND \"User\".\"id\" = 'fce8b418-d90b-4f18-a0e0-40347e7c62d6');\u001b[39m","timestamp":"2025-07-02 11:35:31:3531"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"id\", \"service_category\" AS \"serviceCategory\", \"service_status\" AS \"serviceStatus\", \"style_view\" AS \"styleView\", \"form\", \"description\", \"icon\", \"color\", \"sort_order\" AS \"sortOrder\", \"is_default\" AS \"isDefault\", \"company_id\" AS \"companyId\", \"created_by\" AS \"createdBy\", \"updated_by\" AS \"updatedBy\", \"created_at\" AS \"createdAt\", \"updated_at\" AS \"updatedAt\", \"deleted_at\" AS \"deletedAt\" FROM \"service_categories\" AS \"ServiceCategory\" WHERE (\"ServiceCategory\".\"deleted_at\" IS NULL AND (\"ServiceCategory\".\"id\" = '9caf8caf-86df-42bb-ab8d-d4ef2ec7a917' AND \"ServiceCategory\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae'));\u001b[39m","timestamp":"2025-07-02 11:35:31:3531"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT count(*) AS \"count\" FROM \"services\" AS \"Service\" WHERE (\"Service\".\"deleted_at\" IS NULL AND \"Service\".\"service_category_id\" = '9caf8caf-86df-42bb-ab8d-d4ef2ec7a917');\u001b[39m","timestamp":"2025-07-02 11:35:31:3531"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): UPDATE \"service_categories\" SET \"deleted_at\"=$1,\"updated_at\"=$2 WHERE \"id\" = $3\u001b[39m","timestamp":"2025-07-02 11:35:31:3531"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"name\", \"User\".\"email\", \"User\".\"user_name\" AS \"userName\", \"User\".\"user_type\" AS \"userType\", \"User\".\"mobile_number\" AS \"mobileNumber\", \"User\".\"status\", \"User\".\"date_of_birth\" AS \"dateOfBirth\", \"User\".\"skills\", \"User\".\"total_experience\" AS \"totalExperience\", \"User\".\"avatar\", \"User\".\"proof\", \"User\".\"address\", \"User\".\"notes\", \"User\".\"fcm_token\" AS \"fcmToken\", \"User\".\"last_notification_seen\" AS \"lastNotificationSeen\", \"User\".\"plan_id\" AS \"planId\", \"User\".\"will_expire\" AS \"willExpire\", \"User\".\"message_limit\" AS \"messageLimit\", \"User\".\"is_verified\" AS \"isVerified\", \"User\".\"email_verified_at\" AS \"emailVerifiedAt\", \"User\".\"otp\", \"User\".\"otp_expires_at\" AS \"otpExpiresAt\", \"User\".\"login_attempts\" AS \"loginAttempts\", \"User\".\"locked_until\" AS \"lockedUntil\", \"User\".\"last_login_at\" AS \"lastLoginAt\", \"User\".\"last_login_ip\" AS \"lastLoginIp\", \"User\".\"company_id\" AS \"companyId\", \"User\".\"created_at\" AS \"createdAt\", \"User\".\"updated_at\" AS \"updatedAt\", \"User\".\"deleted_at\" AS \"deletedAt\", \"company\".\"id\" AS \"company.id\", \"company\".\"name\" AS \"company.name\", \"company\".\"status\" AS \"company.status\", \"company\".\"features\" AS \"company.features\", \"company\".\"subscription_end_date\" AS \"company.subscriptionEndDate\" FROM \"users\" AS \"User\" LEFT OUTER JOIN \"companies\" AS \"company\" ON \"User\".\"company_id\" = \"company\".\"id\" AND (\"company\".\"deleted_at\" IS NULL) WHERE (\"User\".\"deleted_at\" IS NULL AND \"User\".\"id\" = 'fce8b418-d90b-4f18-a0e0-40347e7c62d6');\u001b[39m","timestamp":"2025-07-02 11:35:31:3531"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"name\", \"User\".\"email\", \"User\".\"user_name\" AS \"userName\", \"User\".\"user_type\" AS \"userType\", \"User\".\"mobile_number\" AS \"mobileNumber\", \"User\".\"status\", \"User\".\"date_of_birth\" AS \"dateOfBirth\", \"User\".\"skills\", \"User\".\"total_experience\" AS \"totalExperience\", \"User\".\"avatar\", \"User\".\"proof\", \"User\".\"address\", \"User\".\"notes\", \"User\".\"fcm_token\" AS \"fcmToken\", \"User\".\"last_notification_seen\" AS \"lastNotificationSeen\", \"User\".\"plan_id\" AS \"planId\", \"User\".\"will_expire\" AS \"willExpire\", \"User\".\"message_limit\" AS \"messageLimit\", \"User\".\"is_verified\" AS \"isVerified\", \"User\".\"email_verified_at\" AS \"emailVerifiedAt\", \"User\".\"otp\", \"User\".\"otp_expires_at\" AS \"otpExpiresAt\", \"User\".\"login_attempts\" AS \"loginAttempts\", \"User\".\"locked_until\" AS \"lockedUntil\", \"User\".\"last_login_at\" AS \"lastLoginAt\", \"User\".\"last_login_ip\" AS \"lastLoginIp\", \"User\".\"company_id\" AS \"companyId\", \"User\".\"created_at\" AS \"createdAt\", \"User\".\"updated_at\" AS \"updatedAt\", \"User\".\"deleted_at\" AS \"deletedAt\", \"company\".\"id\" AS \"company.id\", \"company\".\"name\" AS \"company.name\", \"company\".\"status\" AS \"company.status\", \"company\".\"features\" AS \"company.features\", \"company\".\"subscription_end_date\" AS \"company.subscriptionEndDate\" FROM \"users\" AS \"User\" LEFT OUTER JOIN \"companies\" AS \"company\" ON \"User\".\"company_id\" = \"company\".\"id\" AND (\"company\".\"deleted_at\" IS NULL) WHERE (\"User\".\"deleted_at\" IS NULL AND \"User\".\"id\" = 'fce8b418-d90b-4f18-a0e0-40347e7c62d6');\u001b[39m","timestamp":"2025-07-02 11:35:32:3532"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"name\", \"User\".\"email\", \"User\".\"user_name\" AS \"userName\", \"User\".\"user_type\" AS \"userType\", \"User\".\"mobile_number\" AS \"mobileNumber\", \"User\".\"status\", \"User\".\"date_of_birth\" AS \"dateOfBirth\", \"User\".\"skills\", \"User\".\"total_experience\" AS \"totalExperience\", \"User\".\"avatar\", \"User\".\"proof\", \"User\".\"address\", \"User\".\"notes\", \"User\".\"fcm_token\" AS \"fcmToken\", \"User\".\"last_notification_seen\" AS \"lastNotificationSeen\", \"User\".\"plan_id\" AS \"planId\", \"User\".\"will_expire\" AS \"willExpire\", \"User\".\"message_limit\" AS \"messageLimit\", \"User\".\"is_verified\" AS \"isVerified\", \"User\".\"email_verified_at\" AS \"emailVerifiedAt\", \"User\".\"otp\", \"User\".\"otp_expires_at\" AS \"otpExpiresAt\", \"User\".\"login_attempts\" AS \"loginAttempts\", \"User\".\"locked_until\" AS \"lockedUntil\", \"User\".\"last_login_at\" AS \"lastLoginAt\", \"User\".\"last_login_ip\" AS \"lastLoginIp\", \"User\".\"company_id\" AS \"companyId\", \"User\".\"created_at\" AS \"createdAt\", \"User\".\"updated_at\" AS \"updatedAt\", \"User\".\"deleted_at\" AS \"deletedAt\", \"company\".\"id\" AS \"company.id\", \"company\".\"name\" AS \"company.name\", \"company\".\"status\" AS \"company.status\", \"company\".\"features\" AS \"company.features\", \"company\".\"subscription_end_date\" AS \"company.subscriptionEndDate\" FROM \"users\" AS \"User\" LEFT OUTER JOIN \"companies\" AS \"company\" ON \"User\".\"company_id\" = \"company\".\"id\" AND (\"company\".\"deleted_at\" IS NULL) WHERE (\"User\".\"deleted_at\" IS NULL AND \"User\".\"id\" = 'fce8b418-d90b-4f18-a0e0-40347e7c62d6');\u001b[39m","timestamp":"2025-07-02 11:35:32:3532"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"name\", \"User\".\"email\", \"User\".\"user_name\" AS \"userName\", \"User\".\"user_type\" AS \"userType\", \"User\".\"mobile_number\" AS \"mobileNumber\", \"User\".\"status\", \"User\".\"date_of_birth\" AS \"dateOfBirth\", \"User\".\"skills\", \"User\".\"total_experience\" AS \"totalExperience\", \"User\".\"avatar\", \"User\".\"proof\", \"User\".\"address\", \"User\".\"notes\", \"User\".\"fcm_token\" AS \"fcmToken\", \"User\".\"last_notification_seen\" AS \"lastNotificationSeen\", \"User\".\"plan_id\" AS \"planId\", \"User\".\"will_expire\" AS \"willExpire\", \"User\".\"message_limit\" AS \"messageLimit\", \"User\".\"is_verified\" AS \"isVerified\", \"User\".\"email_verified_at\" AS \"emailVerifiedAt\", \"User\".\"otp\", \"User\".\"otp_expires_at\" AS \"otpExpiresAt\", \"User\".\"login_attempts\" AS \"loginAttempts\", \"User\".\"locked_until\" AS \"lockedUntil\", \"User\".\"last_login_at\" AS \"lastLoginAt\", \"User\".\"last_login_ip\" AS \"lastLoginIp\", \"User\".\"company_id\" AS \"companyId\", \"User\".\"created_at\" AS \"createdAt\", \"User\".\"updated_at\" AS \"updatedAt\", \"User\".\"deleted_at\" AS \"deletedAt\", \"company\".\"id\" AS \"company.id\", \"company\".\"name\" AS \"company.name\", \"company\".\"status\" AS \"company.status\", \"company\".\"features\" AS \"company.features\", \"company\".\"subscription_end_date\" AS \"company.subscriptionEndDate\" FROM \"users\" AS \"User\" LEFT OUTER JOIN \"companies\" AS \"company\" ON \"User\".\"company_id\" = \"company\".\"id\" AND (\"company\".\"deleted_at\" IS NULL) WHERE (\"User\".\"deleted_at\" IS NULL AND \"User\".\"id\" = 'fce8b418-d90b-4f18-a0e0-40347e7c62d6');\u001b[39m","timestamp":"2025-07-02 11:35:32:3532"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT 1+1 AS result\u001b[39m","timestamp":"2025-07-02 11:48:28:4828"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m✅ Database connection established successfully\u001b[39m","timestamp":"2025-07-02 11:48:28:4828"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔧 Setting up port with automatic conflict resolution...\u001b[39m","timestamp":"2025-07-02 11:48:28:4828"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔍 Finding available port...\u001b[39m","timestamp":"2025-07-02 11:48:28:4828"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔧 Port 8080 is busy, attempting to free it...\u001b[39m","timestamp":"2025-07-02 11:48:28:4828"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔍 Checking for processes on port 8080...\u001b[39m","timestamp":"2025-07-02 11:48:28:4828"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m✅ Killed process 15952 using port 8080\u001b[39m","timestamp":"2025-07-02 11:48:29:4829"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m✅ Successfully freed port 8080\u001b[39m","timestamp":"2025-07-02 11:48:31:4831"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 Port 8080 is ready for use\u001b[39m","timestamp":"2025-07-02 11:48:31:4831"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 Server running on port 8080\u001b[39m","timestamp":"2025-07-02 11:48:31:4831"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📚 Environment: development\u001b[39m","timestamp":"2025-07-02 11:48:31:4831"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🌐 Frontend & Backend: http://localhost:8080\u001b[39m","timestamp":"2025-07-02 11:48:31:4831"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 API Base URL: http://localhost:8080/api\u001b[39m","timestamp":"2025-07-02 11:48:31:4831"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📖 API Documentation: http://localhost:8080/api/docs\u001b[39m","timestamp":"2025-07-02 11:48:31:4831"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🏥 Health Check: http://localhost:8080/health\u001b[39m","timestamp":"2025-07-02 11:48:31:4831"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m✅ TrackNew is ready for use!\u001b[39m","timestamp":"2025-07-02 11:48:31:4831"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"name\", \"User\".\"email\", \"User\".\"password\", \"User\".\"user_name\" AS \"userName\", \"User\".\"user_type\" AS \"userType\", \"User\".\"mobile_number\" AS \"mobileNumber\", \"User\".\"status\", \"User\".\"date_of_birth\" AS \"dateOfBirth\", \"User\".\"skills\", \"User\".\"total_experience\" AS \"totalExperience\", \"User\".\"avatar\", \"User\".\"proof\", \"User\".\"address\", \"User\".\"notes\", \"User\".\"fcm_token\" AS \"fcmToken\", \"User\".\"last_notification_seen\" AS \"lastNotificationSeen\", \"User\".\"plan_id\" AS \"planId\", \"User\".\"will_expire\" AS \"willExpire\", \"User\".\"message_limit\" AS \"messageLimit\", \"User\".\"is_verified\" AS \"isVerified\", \"User\".\"email_verified_at\" AS \"emailVerifiedAt\", \"User\".\"otp\", \"User\".\"otp_expires_at\" AS \"otpExpiresAt\", \"User\".\"login_attempts\" AS \"loginAttempts\", \"User\".\"locked_until\" AS \"lockedUntil\", \"User\".\"last_login_at\" AS \"lastLoginAt\", \"User\".\"last_login_ip\" AS \"lastLoginIp\", \"User\".\"company_id\" AS \"companyId\", \"User\".\"created_at\" AS \"createdAt\", \"User\".\"updated_at\" AS \"updatedAt\", \"User\".\"deleted_at\" AS \"deletedAt\", \"User\".\"department_id\" AS \"departmentId\", \"company\".\"id\" AS \"company.id\", \"company\".\"name\" AS \"company.name\", \"company\".\"status\" AS \"company.status\", \"company\".\"features\" AS \"company.features\", \"company\".\"subscription_end_date\" AS \"company.subscriptionEndDate\" FROM \"users\" AS \"User\" LEFT OUTER JOIN \"companies\" AS \"company\" ON \"User\".\"company_id\" = \"company\".\"id\" AND (\"company\".\"deleted_at\" IS NULL) WHERE (\"User\".\"deleted_at\" IS NULL AND \"User\".\"email\" = '<EMAIL>');\u001b[39m","timestamp":"2025-07-02 11:49:01:491"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): UPDATE \"users\" SET \"last_login_at\"=$1,\"updated_at\"=$2 WHERE \"id\" = $3\u001b[39m","timestamp":"2025-07-02 11:49:02:492"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"name\", \"User\".\"email\", \"User\".\"user_name\" AS \"userName\", \"User\".\"user_type\" AS \"userType\", \"User\".\"mobile_number\" AS \"mobileNumber\", \"User\".\"status\", \"User\".\"date_of_birth\" AS \"dateOfBirth\", \"User\".\"skills\", \"User\".\"total_experience\" AS \"totalExperience\", \"User\".\"avatar\", \"User\".\"proof\", \"User\".\"address\", \"User\".\"notes\", \"User\".\"fcm_token\" AS \"fcmToken\", \"User\".\"last_notification_seen\" AS \"lastNotificationSeen\", \"User\".\"plan_id\" AS \"planId\", \"User\".\"will_expire\" AS \"willExpire\", \"User\".\"message_limit\" AS \"messageLimit\", \"User\".\"is_verified\" AS \"isVerified\", \"User\".\"email_verified_at\" AS \"emailVerifiedAt\", \"User\".\"otp\", \"User\".\"otp_expires_at\" AS \"otpExpiresAt\", \"User\".\"login_attempts\" AS \"loginAttempts\", \"User\".\"locked_until\" AS \"lockedUntil\", \"User\".\"last_login_at\" AS \"lastLoginAt\", \"User\".\"last_login_ip\" AS \"lastLoginIp\", \"User\".\"company_id\" AS \"companyId\", \"User\".\"created_at\" AS \"createdAt\", \"User\".\"updated_at\" AS \"updatedAt\", \"User\".\"deleted_at\" AS \"deletedAt\", \"company\".\"id\" AS \"company.id\", \"company\".\"name\" AS \"company.name\", \"company\".\"status\" AS \"company.status\", \"company\".\"features\" AS \"company.features\", \"company\".\"subscription_end_date\" AS \"company.subscriptionEndDate\" FROM \"users\" AS \"User\" LEFT OUTER JOIN \"companies\" AS \"company\" ON \"User\".\"company_id\" = \"company\".\"id\" AND (\"company\".\"deleted_at\" IS NULL) WHERE (\"User\".\"deleted_at\" IS NULL AND \"User\".\"id\" = 'fce8b418-d90b-4f18-a0e0-40347e7c62d6');\u001b[39m","timestamp":"2025-07-02 11:49:02:492"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT count(*) AS \"count\" FROM \"service_categories\" AS \"ServiceCategory\" WHERE (\"ServiceCategory\".\"deleted_at\" IS NULL AND \"ServiceCategory\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae');\u001b[39m","timestamp":"2025-07-02 11:49:03:493"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT count(*) AS \"count\" FROM \"service_categories\" AS \"ServiceCategory\" WHERE (\"ServiceCategory\".\"deleted_at\" IS NULL AND (\"ServiceCategory\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae' AND \"ServiceCategory\".\"service_status\" = 1));\u001b[39m","timestamp":"2025-07-02 11:49:03:493"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"name\", \"User\".\"email\", \"User\".\"user_name\" AS \"userName\", \"User\".\"user_type\" AS \"userType\", \"User\".\"mobile_number\" AS \"mobileNumber\", \"User\".\"status\", \"User\".\"date_of_birth\" AS \"dateOfBirth\", \"User\".\"skills\", \"User\".\"total_experience\" AS \"totalExperience\", \"User\".\"avatar\", \"User\".\"proof\", \"User\".\"address\", \"User\".\"notes\", \"User\".\"fcm_token\" AS \"fcmToken\", \"User\".\"last_notification_seen\" AS \"lastNotificationSeen\", \"User\".\"plan_id\" AS \"planId\", \"User\".\"will_expire\" AS \"willExpire\", \"User\".\"message_limit\" AS \"messageLimit\", \"User\".\"is_verified\" AS \"isVerified\", \"User\".\"email_verified_at\" AS \"emailVerifiedAt\", \"User\".\"otp\", \"User\".\"otp_expires_at\" AS \"otpExpiresAt\", \"User\".\"login_attempts\" AS \"loginAttempts\", \"User\".\"locked_until\" AS \"lockedUntil\", \"User\".\"last_login_at\" AS \"lastLoginAt\", \"User\".\"last_login_ip\" AS \"lastLoginIp\", \"User\".\"company_id\" AS \"companyId\", \"User\".\"created_at\" AS \"createdAt\", \"User\".\"updated_at\" AS \"updatedAt\", \"User\".\"deleted_at\" AS \"deletedAt\", \"company\".\"id\" AS \"company.id\", \"company\".\"name\" AS \"company.name\", \"company\".\"status\" AS \"company.status\", \"company\".\"features\" AS \"company.features\", \"company\".\"subscription_end_date\" AS \"company.subscriptionEndDate\" FROM \"users\" AS \"User\" LEFT OUTER JOIN \"companies\" AS \"company\" ON \"User\".\"company_id\" = \"company\".\"id\" AND (\"company\".\"deleted_at\" IS NULL) WHERE (\"User\".\"deleted_at\" IS NULL AND \"User\".\"id\" = 'fce8b418-d90b-4f18-a0e0-40347e7c62d6');\u001b[39m","timestamp":"2025-07-02 11:49:03:493"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"name\", \"User\".\"email\", \"User\".\"user_name\" AS \"userName\", \"User\".\"user_type\" AS \"userType\", \"User\".\"mobile_number\" AS \"mobileNumber\", \"User\".\"status\", \"User\".\"date_of_birth\" AS \"dateOfBirth\", \"User\".\"skills\", \"User\".\"total_experience\" AS \"totalExperience\", \"User\".\"avatar\", \"User\".\"proof\", \"User\".\"address\", \"User\".\"notes\", \"User\".\"fcm_token\" AS \"fcmToken\", \"User\".\"last_notification_seen\" AS \"lastNotificationSeen\", \"User\".\"plan_id\" AS \"planId\", \"User\".\"will_expire\" AS \"willExpire\", \"User\".\"message_limit\" AS \"messageLimit\", \"User\".\"is_verified\" AS \"isVerified\", \"User\".\"email_verified_at\" AS \"emailVerifiedAt\", \"User\".\"otp\", \"User\".\"otp_expires_at\" AS \"otpExpiresAt\", \"User\".\"login_attempts\" AS \"loginAttempts\", \"User\".\"locked_until\" AS \"lockedUntil\", \"User\".\"last_login_at\" AS \"lastLoginAt\", \"User\".\"last_login_ip\" AS \"lastLoginIp\", \"User\".\"company_id\" AS \"companyId\", \"User\".\"created_at\" AS \"createdAt\", \"User\".\"updated_at\" AS \"updatedAt\", \"User\".\"deleted_at\" AS \"deletedAt\", \"company\".\"id\" AS \"company.id\", \"company\".\"name\" AS \"company.name\", \"company\".\"status\" AS \"company.status\", \"company\".\"features\" AS \"company.features\", \"company\".\"subscription_end_date\" AS \"company.subscriptionEndDate\" FROM \"users\" AS \"User\" LEFT OUTER JOIN \"companies\" AS \"company\" ON \"User\".\"company_id\" = \"company\".\"id\" AND (\"company\".\"deleted_at\" IS NULL) WHERE (\"User\".\"deleted_at\" IS NULL AND \"User\".\"id\" = 'fce8b418-d90b-4f18-a0e0-40347e7c62d6');\u001b[39m","timestamp":"2025-07-02 11:49:03:493"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT count(*) AS \"count\" FROM \"customer_categories\" AS \"CustomerCategory\" WHERE (\"CustomerCategory\".\"deleted_at\" IS NULL AND \"CustomerCategory\".\"companyId\" = '8722167d-d2ae-498f-95a3-c18afcf427ae');\u001b[39m","timestamp":"2025-07-02 11:49:03:493"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"name\", \"User\".\"email\", \"User\".\"user_name\" AS \"userName\", \"User\".\"user_type\" AS \"userType\", \"User\".\"mobile_number\" AS \"mobileNumber\", \"User\".\"status\", \"User\".\"date_of_birth\" AS \"dateOfBirth\", \"User\".\"skills\", \"User\".\"total_experience\" AS \"totalExperience\", \"User\".\"avatar\", \"User\".\"proof\", \"User\".\"address\", \"User\".\"notes\", \"User\".\"fcm_token\" AS \"fcmToken\", \"User\".\"last_notification_seen\" AS \"lastNotificationSeen\", \"User\".\"plan_id\" AS \"planId\", \"User\".\"will_expire\" AS \"willExpire\", \"User\".\"message_limit\" AS \"messageLimit\", \"User\".\"is_verified\" AS \"isVerified\", \"User\".\"email_verified_at\" AS \"emailVerifiedAt\", \"User\".\"otp\", \"User\".\"otp_expires_at\" AS \"otpExpiresAt\", \"User\".\"login_attempts\" AS \"loginAttempts\", \"User\".\"locked_until\" AS \"lockedUntil\", \"User\".\"last_login_at\" AS \"lastLoginAt\", \"User\".\"last_login_ip\" AS \"lastLoginIp\", \"User\".\"company_id\" AS \"companyId\", \"User\".\"created_at\" AS \"createdAt\", \"User\".\"updated_at\" AS \"updatedAt\", \"User\".\"deleted_at\" AS \"deletedAt\", \"company\".\"id\" AS \"company.id\", \"company\".\"name\" AS \"company.name\", \"company\".\"status\" AS \"company.status\", \"company\".\"features\" AS \"company.features\", \"company\".\"subscription_end_date\" AS \"company.subscriptionEndDate\" FROM \"users\" AS \"User\" LEFT OUTER JOIN \"companies\" AS \"company\" ON \"User\".\"company_id\" = \"company\".\"id\" AND (\"company\".\"deleted_at\" IS NULL) WHERE (\"User\".\"deleted_at\" IS NULL AND \"User\".\"id\" = 'fce8b418-d90b-4f18-a0e0-40347e7c62d6');\u001b[39m","timestamp":"2025-07-02 11:49:03:493"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"ServiceCategory\".\"id\", count(\"ServiceCategory\".\"id\") AS \"count\" FROM \"service_categories\" AS \"ServiceCategory\" LEFT OUTER JOIN \"services\" AS \"services\" ON \"ServiceCategory\".\"id\" = \"services\".\"service_category_id\" AND (\"services\".\"deleted_at\" IS NULL) WHERE (\"ServiceCategory\".\"deleted_at\" IS NULL AND \"ServiceCategory\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') GROUP BY \"ServiceCategory\".\"id\";\u001b[39m","timestamp":"2025-07-02 11:49:04:494"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"ServiceCategory\".\"id\", \"ServiceCategory\".\"service_category\" AS \"serviceCategory\", \"ServiceCategory\".\"service_status\" AS \"serviceStatus\", \"ServiceCategory\".\"style_view\" AS \"styleView\", \"ServiceCategory\".\"form\", \"ServiceCategory\".\"description\", \"ServiceCategory\".\"icon\", \"ServiceCategory\".\"color\", \"ServiceCategory\".\"sort_order\" AS \"sortOrder\", \"ServiceCategory\".\"is_default\" AS \"isDefault\", \"ServiceCategory\".\"created_at\" AS \"createdAt\", \"ServiceCategory\".\"updated_at\" AS \"updatedAt\", COUNT(\"services\".\"id\") AS \"servicesCount\" FROM \"service_categories\" AS \"ServiceCategory\" LEFT OUTER JOIN \"services\" AS \"services\" ON \"ServiceCategory\".\"id\" = \"services\".\"service_category_id\" AND (\"services\".\"deleted_at\" IS NULL) WHERE (\"ServiceCategory\".\"deleted_at\" IS NULL AND \"ServiceCategory\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') GROUP BY \"ServiceCategory\".\"id\" ORDER BY \"sortOrder\" ASC, \"serviceCategory\" ASC LIMIT 5 OFFSET 0;\u001b[39m","timestamp":"2025-07-02 11:49:04:494"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"name\", \"User\".\"email\", \"User\".\"user_name\" AS \"userName\", \"User\".\"user_type\" AS \"userType\", \"User\".\"mobile_number\" AS \"mobileNumber\", \"User\".\"status\", \"User\".\"date_of_birth\" AS \"dateOfBirth\", \"User\".\"skills\", \"User\".\"total_experience\" AS \"totalExperience\", \"User\".\"avatar\", \"User\".\"proof\", \"User\".\"address\", \"User\".\"notes\", \"User\".\"fcm_token\" AS \"fcmToken\", \"User\".\"last_notification_seen\" AS \"lastNotificationSeen\", \"User\".\"plan_id\" AS \"planId\", \"User\".\"will_expire\" AS \"willExpire\", \"User\".\"message_limit\" AS \"messageLimit\", \"User\".\"is_verified\" AS \"isVerified\", \"User\".\"email_verified_at\" AS \"emailVerifiedAt\", \"User\".\"otp\", \"User\".\"otp_expires_at\" AS \"otpExpiresAt\", \"User\".\"login_attempts\" AS \"loginAttempts\", \"User\".\"locked_until\" AS \"lockedUntil\", \"User\".\"last_login_at\" AS \"lastLoginAt\", \"User\".\"last_login_ip\" AS \"lastLoginIp\", \"User\".\"company_id\" AS \"companyId\", \"User\".\"created_at\" AS \"createdAt\", \"User\".\"updated_at\" AS \"updatedAt\", \"User\".\"deleted_at\" AS \"deletedAt\", \"company\".\"id\" AS \"company.id\", \"company\".\"name\" AS \"company.name\", \"company\".\"status\" AS \"company.status\", \"company\".\"features\" AS \"company.features\", \"company\".\"subscription_end_date\" AS \"company.subscriptionEndDate\" FROM \"users\" AS \"User\" LEFT OUTER JOIN \"companies\" AS \"company\" ON \"User\".\"company_id\" = \"company\".\"id\" AND (\"company\".\"deleted_at\" IS NULL) WHERE (\"User\".\"deleted_at\" IS NULL AND \"User\".\"id\" = 'fce8b418-d90b-4f18-a0e0-40347e7c62d6');\u001b[39m","timestamp":"2025-07-02 11:49:04:494"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"name\", \"User\".\"email\", \"User\".\"user_name\" AS \"userName\", \"User\".\"user_type\" AS \"userType\", \"User\".\"mobile_number\" AS \"mobileNumber\", \"User\".\"status\", \"User\".\"date_of_birth\" AS \"dateOfBirth\", \"User\".\"skills\", \"User\".\"total_experience\" AS \"totalExperience\", \"User\".\"avatar\", \"User\".\"proof\", \"User\".\"address\", \"User\".\"notes\", \"User\".\"fcm_token\" AS \"fcmToken\", \"User\".\"last_notification_seen\" AS \"lastNotificationSeen\", \"User\".\"plan_id\" AS \"planId\", \"User\".\"will_expire\" AS \"willExpire\", \"User\".\"message_limit\" AS \"messageLimit\", \"User\".\"is_verified\" AS \"isVerified\", \"User\".\"email_verified_at\" AS \"emailVerifiedAt\", \"User\".\"otp\", \"User\".\"otp_expires_at\" AS \"otpExpiresAt\", \"User\".\"login_attempts\" AS \"loginAttempts\", \"User\".\"locked_until\" AS \"lockedUntil\", \"User\".\"last_login_at\" AS \"lastLoginAt\", \"User\".\"last_login_ip\" AS \"lastLoginIp\", \"User\".\"company_id\" AS \"companyId\", \"User\".\"created_at\" AS \"createdAt\", \"User\".\"updated_at\" AS \"updatedAt\", \"User\".\"deleted_at\" AS \"deletedAt\", \"company\".\"id\" AS \"company.id\", \"company\".\"name\" AS \"company.name\", \"company\".\"status\" AS \"company.status\", \"company\".\"features\" AS \"company.features\", \"company\".\"subscription_end_date\" AS \"company.subscriptionEndDate\" FROM \"users\" AS \"User\" LEFT OUTER JOIN \"companies\" AS \"company\" ON \"User\".\"company_id\" = \"company\".\"id\" AND (\"company\".\"deleted_at\" IS NULL) WHERE (\"User\".\"deleted_at\" IS NULL AND \"User\".\"id\" = 'fce8b418-d90b-4f18-a0e0-40347e7c62d6');\u001b[39m","timestamp":"2025-07-02 11:49:05:495"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"name\", \"User\".\"email\", \"User\".\"password\", \"User\".\"user_name\" AS \"userName\", \"User\".\"user_type\" AS \"userType\", \"User\".\"mobile_number\" AS \"mobileNumber\", \"User\".\"status\", \"User\".\"date_of_birth\" AS \"dateOfBirth\", \"User\".\"skills\", \"User\".\"total_experience\" AS \"totalExperience\", \"User\".\"avatar\", \"User\".\"proof\", \"User\".\"address\", \"User\".\"notes\", \"User\".\"fcm_token\" AS \"fcmToken\", \"User\".\"last_notification_seen\" AS \"lastNotificationSeen\", \"User\".\"plan_id\" AS \"planId\", \"User\".\"will_expire\" AS \"willExpire\", \"User\".\"message_limit\" AS \"messageLimit\", \"User\".\"is_verified\" AS \"isVerified\", \"User\".\"email_verified_at\" AS \"emailVerifiedAt\", \"User\".\"otp\", \"User\".\"otp_expires_at\" AS \"otpExpiresAt\", \"User\".\"login_attempts\" AS \"loginAttempts\", \"User\".\"locked_until\" AS \"lockedUntil\", \"User\".\"last_login_at\" AS \"lastLoginAt\", \"User\".\"last_login_ip\" AS \"lastLoginIp\", \"User\".\"company_id\" AS \"companyId\", \"User\".\"created_at\" AS \"createdAt\", \"User\".\"updated_at\" AS \"updatedAt\", \"User\".\"deleted_at\" AS \"deletedAt\", \"User\".\"department_id\" AS \"departmentId\", \"company\".\"id\" AS \"company.id\", \"company\".\"name\" AS \"company.name\", \"company\".\"status\" AS \"company.status\", \"company\".\"features\" AS \"company.features\", \"company\".\"subscription_end_date\" AS \"company.subscriptionEndDate\" FROM \"users\" AS \"User\" LEFT OUTER JOIN \"companies\" AS \"company\" ON \"User\".\"company_id\" = \"company\".\"id\" AND (\"company\".\"deleted_at\" IS NULL) WHERE (\"User\".\"deleted_at\" IS NULL AND \"User\".\"email\" = '<EMAIL>');\u001b[39m","timestamp":"2025-07-02 11:49:56:4956"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): UPDATE \"users\" SET \"last_login_at\"=$1,\"updated_at\"=$2 WHERE \"id\" = $3\u001b[39m","timestamp":"2025-07-02 11:49:56:4956"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"name\", \"User\".\"email\", \"User\".\"user_name\" AS \"userName\", \"User\".\"user_type\" AS \"userType\", \"User\".\"mobile_number\" AS \"mobileNumber\", \"User\".\"status\", \"User\".\"date_of_birth\" AS \"dateOfBirth\", \"User\".\"skills\", \"User\".\"total_experience\" AS \"totalExperience\", \"User\".\"avatar\", \"User\".\"proof\", \"User\".\"address\", \"User\".\"notes\", \"User\".\"fcm_token\" AS \"fcmToken\", \"User\".\"last_notification_seen\" AS \"lastNotificationSeen\", \"User\".\"plan_id\" AS \"planId\", \"User\".\"will_expire\" AS \"willExpire\", \"User\".\"message_limit\" AS \"messageLimit\", \"User\".\"is_verified\" AS \"isVerified\", \"User\".\"email_verified_at\" AS \"emailVerifiedAt\", \"User\".\"otp\", \"User\".\"otp_expires_at\" AS \"otpExpiresAt\", \"User\".\"login_attempts\" AS \"loginAttempts\", \"User\".\"locked_until\" AS \"lockedUntil\", \"User\".\"last_login_at\" AS \"lastLoginAt\", \"User\".\"last_login_ip\" AS \"lastLoginIp\", \"User\".\"company_id\" AS \"companyId\", \"User\".\"created_at\" AS \"createdAt\", \"User\".\"updated_at\" AS \"updatedAt\", \"User\".\"deleted_at\" AS \"deletedAt\", \"company\".\"id\" AS \"company.id\", \"company\".\"name\" AS \"company.name\", \"company\".\"status\" AS \"company.status\", \"company\".\"features\" AS \"company.features\", \"company\".\"subscription_end_date\" AS \"company.subscriptionEndDate\" FROM \"users\" AS \"User\" LEFT OUTER JOIN \"companies\" AS \"company\" ON \"User\".\"company_id\" = \"company\".\"id\" AND (\"company\".\"deleted_at\" IS NULL) WHERE (\"User\".\"deleted_at\" IS NULL AND \"User\".\"id\" = 'fce8b418-d90b-4f18-a0e0-40347e7c62d6');\u001b[39m","timestamp":"2025-07-02 11:49:57:4957"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT count(*) AS \"count\" FROM \"service_categories\" AS \"ServiceCategory\" WHERE (\"ServiceCategory\".\"deleted_at\" IS NULL AND \"ServiceCategory\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae');\u001b[39m","timestamp":"2025-07-02 11:49:57:4957"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT count(*) AS \"count\" FROM \"service_categories\" AS \"ServiceCategory\" WHERE (\"ServiceCategory\".\"deleted_at\" IS NULL AND (\"ServiceCategory\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae' AND \"ServiceCategory\".\"service_status\" = 1));\u001b[39m","timestamp":"2025-07-02 11:49:57:4957"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"name\", \"User\".\"email\", \"User\".\"user_name\" AS \"userName\", \"User\".\"user_type\" AS \"userType\", \"User\".\"mobile_number\" AS \"mobileNumber\", \"User\".\"status\", \"User\".\"date_of_birth\" AS \"dateOfBirth\", \"User\".\"skills\", \"User\".\"total_experience\" AS \"totalExperience\", \"User\".\"avatar\", \"User\".\"proof\", \"User\".\"address\", \"User\".\"notes\", \"User\".\"fcm_token\" AS \"fcmToken\", \"User\".\"last_notification_seen\" AS \"lastNotificationSeen\", \"User\".\"plan_id\" AS \"planId\", \"User\".\"will_expire\" AS \"willExpire\", \"User\".\"message_limit\" AS \"messageLimit\", \"User\".\"is_verified\" AS \"isVerified\", \"User\".\"email_verified_at\" AS \"emailVerifiedAt\", \"User\".\"otp\", \"User\".\"otp_expires_at\" AS \"otpExpiresAt\", \"User\".\"login_attempts\" AS \"loginAttempts\", \"User\".\"locked_until\" AS \"lockedUntil\", \"User\".\"last_login_at\" AS \"lastLoginAt\", \"User\".\"last_login_ip\" AS \"lastLoginIp\", \"User\".\"company_id\" AS \"companyId\", \"User\".\"created_at\" AS \"createdAt\", \"User\".\"updated_at\" AS \"updatedAt\", \"User\".\"deleted_at\" AS \"deletedAt\", \"company\".\"id\" AS \"company.id\", \"company\".\"name\" AS \"company.name\", \"company\".\"status\" AS \"company.status\", \"company\".\"features\" AS \"company.features\", \"company\".\"subscription_end_date\" AS \"company.subscriptionEndDate\" FROM \"users\" AS \"User\" LEFT OUTER JOIN \"companies\" AS \"company\" ON \"User\".\"company_id\" = \"company\".\"id\" AND (\"company\".\"deleted_at\" IS NULL) WHERE (\"User\".\"deleted_at\" IS NULL AND \"User\".\"id\" = 'fce8b418-d90b-4f18-a0e0-40347e7c62d6');\u001b[39m","timestamp":"2025-07-02 11:49:57:4957"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"name\", \"User\".\"email\", \"User\".\"user_name\" AS \"userName\", \"User\".\"user_type\" AS \"userType\", \"User\".\"mobile_number\" AS \"mobileNumber\", \"User\".\"status\", \"User\".\"date_of_birth\" AS \"dateOfBirth\", \"User\".\"skills\", \"User\".\"total_experience\" AS \"totalExperience\", \"User\".\"avatar\", \"User\".\"proof\", \"User\".\"address\", \"User\".\"notes\", \"User\".\"fcm_token\" AS \"fcmToken\", \"User\".\"last_notification_seen\" AS \"lastNotificationSeen\", \"User\".\"plan_id\" AS \"planId\", \"User\".\"will_expire\" AS \"willExpire\", \"User\".\"message_limit\" AS \"messageLimit\", \"User\".\"is_verified\" AS \"isVerified\", \"User\".\"email_verified_at\" AS \"emailVerifiedAt\", \"User\".\"otp\", \"User\".\"otp_expires_at\" AS \"otpExpiresAt\", \"User\".\"login_attempts\" AS \"loginAttempts\", \"User\".\"locked_until\" AS \"lockedUntil\", \"User\".\"last_login_at\" AS \"lastLoginAt\", \"User\".\"last_login_ip\" AS \"lastLoginIp\", \"User\".\"company_id\" AS \"companyId\", \"User\".\"created_at\" AS \"createdAt\", \"User\".\"updated_at\" AS \"updatedAt\", \"User\".\"deleted_at\" AS \"deletedAt\", \"company\".\"id\" AS \"company.id\", \"company\".\"name\" AS \"company.name\", \"company\".\"status\" AS \"company.status\", \"company\".\"features\" AS \"company.features\", \"company\".\"subscription_end_date\" AS \"company.subscriptionEndDate\" FROM \"users\" AS \"User\" LEFT OUTER JOIN \"companies\" AS \"company\" ON \"User\".\"company_id\" = \"company\".\"id\" AND (\"company\".\"deleted_at\" IS NULL) WHERE (\"User\".\"deleted_at\" IS NULL AND \"User\".\"id\" = 'fce8b418-d90b-4f18-a0e0-40347e7c62d6');\u001b[39m","timestamp":"2025-07-02 11:49:57:4957"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT count(*) AS \"count\" FROM \"customer_categories\" AS \"CustomerCategory\" WHERE (\"CustomerCategory\".\"deleted_at\" IS NULL AND \"CustomerCategory\".\"companyId\" = '8722167d-d2ae-498f-95a3-c18afcf427ae');\u001b[39m","timestamp":"2025-07-02 11:49:58:4958"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"name\", \"User\".\"email\", \"User\".\"user_name\" AS \"userName\", \"User\".\"user_type\" AS \"userType\", \"User\".\"mobile_number\" AS \"mobileNumber\", \"User\".\"status\", \"User\".\"date_of_birth\" AS \"dateOfBirth\", \"User\".\"skills\", \"User\".\"total_experience\" AS \"totalExperience\", \"User\".\"avatar\", \"User\".\"proof\", \"User\".\"address\", \"User\".\"notes\", \"User\".\"fcm_token\" AS \"fcmToken\", \"User\".\"last_notification_seen\" AS \"lastNotificationSeen\", \"User\".\"plan_id\" AS \"planId\", \"User\".\"will_expire\" AS \"willExpire\", \"User\".\"message_limit\" AS \"messageLimit\", \"User\".\"is_verified\" AS \"isVerified\", \"User\".\"email_verified_at\" AS \"emailVerifiedAt\", \"User\".\"otp\", \"User\".\"otp_expires_at\" AS \"otpExpiresAt\", \"User\".\"login_attempts\" AS \"loginAttempts\", \"User\".\"locked_until\" AS \"lockedUntil\", \"User\".\"last_login_at\" AS \"lastLoginAt\", \"User\".\"last_login_ip\" AS \"lastLoginIp\", \"User\".\"company_id\" AS \"companyId\", \"User\".\"created_at\" AS \"createdAt\", \"User\".\"updated_at\" AS \"updatedAt\", \"User\".\"deleted_at\" AS \"deletedAt\", \"company\".\"id\" AS \"company.id\", \"company\".\"name\" AS \"company.name\", \"company\".\"status\" AS \"company.status\", \"company\".\"features\" AS \"company.features\", \"company\".\"subscription_end_date\" AS \"company.subscriptionEndDate\" FROM \"users\" AS \"User\" LEFT OUTER JOIN \"companies\" AS \"company\" ON \"User\".\"company_id\" = \"company\".\"id\" AND (\"company\".\"deleted_at\" IS NULL) WHERE (\"User\".\"deleted_at\" IS NULL AND \"User\".\"id\" = 'fce8b418-d90b-4f18-a0e0-40347e7c62d6');\u001b[39m","timestamp":"2025-07-02 11:49:58:4958"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"ServiceCategory\".\"id\", count(\"ServiceCategory\".\"id\") AS \"count\" FROM \"service_categories\" AS \"ServiceCategory\" LEFT OUTER JOIN \"services\" AS \"services\" ON \"ServiceCategory\".\"id\" = \"services\".\"service_category_id\" AND (\"services\".\"deleted_at\" IS NULL) WHERE (\"ServiceCategory\".\"deleted_at\" IS NULL AND \"ServiceCategory\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') GROUP BY \"ServiceCategory\".\"id\";\u001b[39m","timestamp":"2025-07-02 11:49:59:4959"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"ServiceCategory\".\"id\", \"ServiceCategory\".\"service_category\" AS \"serviceCategory\", \"ServiceCategory\".\"service_status\" AS \"serviceStatus\", \"ServiceCategory\".\"style_view\" AS \"styleView\", \"ServiceCategory\".\"form\", \"ServiceCategory\".\"description\", \"ServiceCategory\".\"icon\", \"ServiceCategory\".\"color\", \"ServiceCategory\".\"sort_order\" AS \"sortOrder\", \"ServiceCategory\".\"is_default\" AS \"isDefault\", \"ServiceCategory\".\"created_at\" AS \"createdAt\", \"ServiceCategory\".\"updated_at\" AS \"updatedAt\", COUNT(\"services\".\"id\") AS \"servicesCount\" FROM \"service_categories\" AS \"ServiceCategory\" LEFT OUTER JOIN \"services\" AS \"services\" ON \"ServiceCategory\".\"id\" = \"services\".\"service_category_id\" AND (\"services\".\"deleted_at\" IS NULL) WHERE (\"ServiceCategory\".\"deleted_at\" IS NULL AND \"ServiceCategory\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') GROUP BY \"ServiceCategory\".\"id\" ORDER BY \"sortOrder\" ASC, \"serviceCategory\" ASC LIMIT 5 OFFSET 0;\u001b[39m","timestamp":"2025-07-02 11:49:59:4959"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"name\", \"User\".\"email\", \"User\".\"user_name\" AS \"userName\", \"User\".\"user_type\" AS \"userType\", \"User\".\"mobile_number\" AS \"mobileNumber\", \"User\".\"status\", \"User\".\"date_of_birth\" AS \"dateOfBirth\", \"User\".\"skills\", \"User\".\"total_experience\" AS \"totalExperience\", \"User\".\"avatar\", \"User\".\"proof\", \"User\".\"address\", \"User\".\"notes\", \"User\".\"fcm_token\" AS \"fcmToken\", \"User\".\"last_notification_seen\" AS \"lastNotificationSeen\", \"User\".\"plan_id\" AS \"planId\", \"User\".\"will_expire\" AS \"willExpire\", \"User\".\"message_limit\" AS \"messageLimit\", \"User\".\"is_verified\" AS \"isVerified\", \"User\".\"email_verified_at\" AS \"emailVerifiedAt\", \"User\".\"otp\", \"User\".\"otp_expires_at\" AS \"otpExpiresAt\", \"User\".\"login_attempts\" AS \"loginAttempts\", \"User\".\"locked_until\" AS \"lockedUntil\", \"User\".\"last_login_at\" AS \"lastLoginAt\", \"User\".\"last_login_ip\" AS \"lastLoginIp\", \"User\".\"company_id\" AS \"companyId\", \"User\".\"created_at\" AS \"createdAt\", \"User\".\"updated_at\" AS \"updatedAt\", \"User\".\"deleted_at\" AS \"deletedAt\", \"company\".\"id\" AS \"company.id\", \"company\".\"name\" AS \"company.name\", \"company\".\"status\" AS \"company.status\", \"company\".\"features\" AS \"company.features\", \"company\".\"subscription_end_date\" AS \"company.subscriptionEndDate\" FROM \"users\" AS \"User\" LEFT OUTER JOIN \"companies\" AS \"company\" ON \"User\".\"company_id\" = \"company\".\"id\" AND (\"company\".\"deleted_at\" IS NULL) WHERE (\"User\".\"deleted_at\" IS NULL AND \"User\".\"id\" = 'fce8b418-d90b-4f18-a0e0-40347e7c62d6');\u001b[39m","timestamp":"2025-07-02 11:49:59:4959"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"name\", \"User\".\"email\", \"User\".\"user_name\" AS \"userName\", \"User\".\"user_type\" AS \"userType\", \"User\".\"mobile_number\" AS \"mobileNumber\", \"User\".\"status\", \"User\".\"date_of_birth\" AS \"dateOfBirth\", \"User\".\"skills\", \"User\".\"total_experience\" AS \"totalExperience\", \"User\".\"avatar\", \"User\".\"proof\", \"User\".\"address\", \"User\".\"notes\", \"User\".\"fcm_token\" AS \"fcmToken\", \"User\".\"last_notification_seen\" AS \"lastNotificationSeen\", \"User\".\"plan_id\" AS \"planId\", \"User\".\"will_expire\" AS \"willExpire\", \"User\".\"message_limit\" AS \"messageLimit\", \"User\".\"is_verified\" AS \"isVerified\", \"User\".\"email_verified_at\" AS \"emailVerifiedAt\", \"User\".\"otp\", \"User\".\"otp_expires_at\" AS \"otpExpiresAt\", \"User\".\"login_attempts\" AS \"loginAttempts\", \"User\".\"locked_until\" AS \"lockedUntil\", \"User\".\"last_login_at\" AS \"lastLoginAt\", \"User\".\"last_login_ip\" AS \"lastLoginIp\", \"User\".\"company_id\" AS \"companyId\", \"User\".\"created_at\" AS \"createdAt\", \"User\".\"updated_at\" AS \"updatedAt\", \"User\".\"deleted_at\" AS \"deletedAt\", \"company\".\"id\" AS \"company.id\", \"company\".\"name\" AS \"company.name\", \"company\".\"status\" AS \"company.status\", \"company\".\"features\" AS \"company.features\", \"company\".\"subscription_end_date\" AS \"company.subscriptionEndDate\" FROM \"users\" AS \"User\" LEFT OUTER JOIN \"companies\" AS \"company\" ON \"User\".\"company_id\" = \"company\".\"id\" AND (\"company\".\"deleted_at\" IS NULL) WHERE (\"User\".\"deleted_at\" IS NULL AND \"User\".\"id\" = 'fce8b418-d90b-4f18-a0e0-40347e7c62d6');\u001b[39m","timestamp":"2025-07-02 11:50:00:500"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT 1+1 AS result\u001b[39m","timestamp":"2025-07-02 11:50:53:5053"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m✅ Database connection established successfully\u001b[39m","timestamp":"2025-07-02 11:50:53:5053"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔧 Setting up port with automatic conflict resolution...\u001b[39m","timestamp":"2025-07-02 11:50:53:5053"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔍 Finding available port...\u001b[39m","timestamp":"2025-07-02 11:50:53:5053"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m✅ Port 8080 is available\u001b[39m","timestamp":"2025-07-02 11:50:53:5053"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 Port 8080 is ready for use\u001b[39m","timestamp":"2025-07-02 11:50:53:5053"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 Server running on port 8080\u001b[39m","timestamp":"2025-07-02 11:50:53:5053"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📚 Environment: development\u001b[39m","timestamp":"2025-07-02 11:50:53:5053"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🌐 Frontend & Backend: http://localhost:8080\u001b[39m","timestamp":"2025-07-02 11:50:53:5053"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 API Base URL: http://localhost:8080/api\u001b[39m","timestamp":"2025-07-02 11:50:53:5053"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📖 API Documentation: http://localhost:8080/api/docs\u001b[39m","timestamp":"2025-07-02 11:50:53:5053"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🏥 Health Check: http://localhost:8080/health\u001b[39m","timestamp":"2025-07-02 11:50:53:5053"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m✅ TrackNew is ready for use!\u001b[39m","timestamp":"2025-07-02 11:50:53:5053"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT 1+1 AS result\u001b[39m","timestamp":"2025-07-02 11:51:15:5115"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m✅ Database connection established successfully\u001b[39m","timestamp":"2025-07-02 11:51:15:5115"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔧 Setting up port with automatic conflict resolution...\u001b[39m","timestamp":"2025-07-02 11:51:15:5115"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔍 Finding available port...\u001b[39m","timestamp":"2025-07-02 11:51:15:5115"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m✅ Port 8080 is available\u001b[39m","timestamp":"2025-07-02 11:51:15:5115"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 Port 8080 is ready for use\u001b[39m","timestamp":"2025-07-02 11:51:15:5115"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 Server running on port 8080\u001b[39m","timestamp":"2025-07-02 11:51:15:5115"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📚 Environment: development\u001b[39m","timestamp":"2025-07-02 11:51:15:5115"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🌐 Frontend & Backend: http://localhost:8080\u001b[39m","timestamp":"2025-07-02 11:51:15:5115"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 API Base URL: http://localhost:8080/api\u001b[39m","timestamp":"2025-07-02 11:51:15:5115"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🏥 Health Check: http://localhost:8080/health\u001b[39m","timestamp":"2025-07-02 11:51:15:5115"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m✅ TrackNew is ready for use!\u001b[39m","timestamp":"2025-07-02 11:51:15:5115"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m{\u001b[39m\n\u001b[31m  \"message\": \"Access denied. No token provided.\",\u001b[39m\n\u001b[31m  \"stack\": \"Error: Access denied. No token provided.\\n    at file:///D:/New_TrackNew/track_new/backend/src/middleware/auth.js:43:11\\n    at file:///D:/New_TrackNew/track_new/backend/src/utils/asyncHandler.js:7:21\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\\n    at trim_prefix (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:328:13)\\n    at D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:286:9\\n    at Function.process_params (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:346:12)\\n    at next (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:280:10)\\n    at Function.handle (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:175:3)\\n    at router (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\index.js:47:12)\\n    at Layer.handle [as handle_request] (D:\\\\New_TrackNew\\\\track_new\\\\backend\\\\node_modules\\\\express\\\\lib\\\\router\\\\layer.js:95:5)\",\u001b[39m\n\u001b[31m  \"statusCode\": 401,\u001b[39m\n\u001b[31m  \"timestamp\": \"2025-07-02T06:21:39.441Z\",\u001b[39m\n\u001b[31m  \"request\": {\u001b[39m\n\u001b[31m    \"method\": \"GET\",\u001b[39m\n\u001b[31m    \"url\": \"/api/health\",\u001b[39m\n\u001b[31m    \"ip\": \"::1\",\u001b[39m\n\u001b[31m    \"userAgent\": \"curl/8.12.1\"\u001b[39m\n\u001b[31m  }\u001b[39m\n\u001b[31m}\u001b[39m","timestamp":"2025-07-02 11:51:39:5139"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"name\", \"User\".\"email\", \"User\".\"password\", \"User\".\"user_name\" AS \"userName\", \"User\".\"user_type\" AS \"userType\", \"User\".\"mobile_number\" AS \"mobileNumber\", \"User\".\"status\", \"User\".\"date_of_birth\" AS \"dateOfBirth\", \"User\".\"skills\", \"User\".\"total_experience\" AS \"totalExperience\", \"User\".\"avatar\", \"User\".\"proof\", \"User\".\"address\", \"User\".\"notes\", \"User\".\"fcm_token\" AS \"fcmToken\", \"User\".\"last_notification_seen\" AS \"lastNotificationSeen\", \"User\".\"plan_id\" AS \"planId\", \"User\".\"will_expire\" AS \"willExpire\", \"User\".\"message_limit\" AS \"messageLimit\", \"User\".\"is_verified\" AS \"isVerified\", \"User\".\"email_verified_at\" AS \"emailVerifiedAt\", \"User\".\"otp\", \"User\".\"otp_expires_at\" AS \"otpExpiresAt\", \"User\".\"login_attempts\" AS \"loginAttempts\", \"User\".\"locked_until\" AS \"lockedUntil\", \"User\".\"last_login_at\" AS \"lastLoginAt\", \"User\".\"last_login_ip\" AS \"lastLoginIp\", \"User\".\"company_id\" AS \"companyId\", \"User\".\"created_at\" AS \"createdAt\", \"User\".\"updated_at\" AS \"updatedAt\", \"User\".\"deleted_at\" AS \"deletedAt\", \"User\".\"department_id\" AS \"departmentId\", \"company\".\"id\" AS \"company.id\", \"company\".\"name\" AS \"company.name\", \"company\".\"status\" AS \"company.status\", \"company\".\"features\" AS \"company.features\", \"company\".\"subscription_end_date\" AS \"company.subscriptionEndDate\" FROM \"users\" AS \"User\" LEFT OUTER JOIN \"companies\" AS \"company\" ON \"User\".\"company_id\" = \"company\".\"id\" AND (\"company\".\"deleted_at\" IS NULL) WHERE (\"User\".\"deleted_at\" IS NULL AND \"User\".\"email\" = '<EMAIL>');\u001b[39m","timestamp":"2025-07-02 11:51:47:5147"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): UPDATE \"users\" SET \"last_login_at\"=$1,\"updated_at\"=$2 WHERE \"id\" = $3\u001b[39m","timestamp":"2025-07-02 11:51:48:5148"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"name\", \"User\".\"email\", \"User\".\"user_name\" AS \"userName\", \"User\".\"user_type\" AS \"userType\", \"User\".\"mobile_number\" AS \"mobileNumber\", \"User\".\"status\", \"User\".\"date_of_birth\" AS \"dateOfBirth\", \"User\".\"skills\", \"User\".\"total_experience\" AS \"totalExperience\", \"User\".\"avatar\", \"User\".\"proof\", \"User\".\"address\", \"User\".\"notes\", \"User\".\"fcm_token\" AS \"fcmToken\", \"User\".\"last_notification_seen\" AS \"lastNotificationSeen\", \"User\".\"plan_id\" AS \"planId\", \"User\".\"will_expire\" AS \"willExpire\", \"User\".\"message_limit\" AS \"messageLimit\", \"User\".\"is_verified\" AS \"isVerified\", \"User\".\"email_verified_at\" AS \"emailVerifiedAt\", \"User\".\"otp\", \"User\".\"otp_expires_at\" AS \"otpExpiresAt\", \"User\".\"login_attempts\" AS \"loginAttempts\", \"User\".\"locked_until\" AS \"lockedUntil\", \"User\".\"last_login_at\" AS \"lastLoginAt\", \"User\".\"last_login_ip\" AS \"lastLoginIp\", \"User\".\"company_id\" AS \"companyId\", \"User\".\"created_at\" AS \"createdAt\", \"User\".\"updated_at\" AS \"updatedAt\", \"User\".\"deleted_at\" AS \"deletedAt\", \"company\".\"id\" AS \"company.id\", \"company\".\"name\" AS \"company.name\", \"company\".\"status\" AS \"company.status\", \"company\".\"features\" AS \"company.features\", \"company\".\"subscription_end_date\" AS \"company.subscriptionEndDate\" FROM \"users\" AS \"User\" LEFT OUTER JOIN \"companies\" AS \"company\" ON \"User\".\"company_id\" = \"company\".\"id\" AND (\"company\".\"deleted_at\" IS NULL) WHERE (\"User\".\"deleted_at\" IS NULL AND \"User\".\"id\" = 'fce8b418-d90b-4f18-a0e0-40347e7c62d6');\u001b[39m","timestamp":"2025-07-02 11:51:48:5148"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT count(*) AS \"count\" FROM \"service_categories\" AS \"ServiceCategory\" WHERE (\"ServiceCategory\".\"deleted_at\" IS NULL AND \"ServiceCategory\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae');\u001b[39m","timestamp":"2025-07-02 11:51:48:5148"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT count(*) AS \"count\" FROM \"service_categories\" AS \"ServiceCategory\" WHERE (\"ServiceCategory\".\"deleted_at\" IS NULL AND (\"ServiceCategory\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae' AND \"ServiceCategory\".\"service_status\" = 1));\u001b[39m","timestamp":"2025-07-02 11:51:48:5148"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"name\", \"User\".\"email\", \"User\".\"user_name\" AS \"userName\", \"User\".\"user_type\" AS \"userType\", \"User\".\"mobile_number\" AS \"mobileNumber\", \"User\".\"status\", \"User\".\"date_of_birth\" AS \"dateOfBirth\", \"User\".\"skills\", \"User\".\"total_experience\" AS \"totalExperience\", \"User\".\"avatar\", \"User\".\"proof\", \"User\".\"address\", \"User\".\"notes\", \"User\".\"fcm_token\" AS \"fcmToken\", \"User\".\"last_notification_seen\" AS \"lastNotificationSeen\", \"User\".\"plan_id\" AS \"planId\", \"User\".\"will_expire\" AS \"willExpire\", \"User\".\"message_limit\" AS \"messageLimit\", \"User\".\"is_verified\" AS \"isVerified\", \"User\".\"email_verified_at\" AS \"emailVerifiedAt\", \"User\".\"otp\", \"User\".\"otp_expires_at\" AS \"otpExpiresAt\", \"User\".\"login_attempts\" AS \"loginAttempts\", \"User\".\"locked_until\" AS \"lockedUntil\", \"User\".\"last_login_at\" AS \"lastLoginAt\", \"User\".\"last_login_ip\" AS \"lastLoginIp\", \"User\".\"company_id\" AS \"companyId\", \"User\".\"created_at\" AS \"createdAt\", \"User\".\"updated_at\" AS \"updatedAt\", \"User\".\"deleted_at\" AS \"deletedAt\", \"company\".\"id\" AS \"company.id\", \"company\".\"name\" AS \"company.name\", \"company\".\"status\" AS \"company.status\", \"company\".\"features\" AS \"company.features\", \"company\".\"subscription_end_date\" AS \"company.subscriptionEndDate\" FROM \"users\" AS \"User\" LEFT OUTER JOIN \"companies\" AS \"company\" ON \"User\".\"company_id\" = \"company\".\"id\" AND (\"company\".\"deleted_at\" IS NULL) WHERE (\"User\".\"deleted_at\" IS NULL AND \"User\".\"id\" = 'fce8b418-d90b-4f18-a0e0-40347e7c62d6');\u001b[39m","timestamp":"2025-07-02 11:51:48:5148"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"name\", \"User\".\"email\", \"User\".\"user_name\" AS \"userName\", \"User\".\"user_type\" AS \"userType\", \"User\".\"mobile_number\" AS \"mobileNumber\", \"User\".\"status\", \"User\".\"date_of_birth\" AS \"dateOfBirth\", \"User\".\"skills\", \"User\".\"total_experience\" AS \"totalExperience\", \"User\".\"avatar\", \"User\".\"proof\", \"User\".\"address\", \"User\".\"notes\", \"User\".\"fcm_token\" AS \"fcmToken\", \"User\".\"last_notification_seen\" AS \"lastNotificationSeen\", \"User\".\"plan_id\" AS \"planId\", \"User\".\"will_expire\" AS \"willExpire\", \"User\".\"message_limit\" AS \"messageLimit\", \"User\".\"is_verified\" AS \"isVerified\", \"User\".\"email_verified_at\" AS \"emailVerifiedAt\", \"User\".\"otp\", \"User\".\"otp_expires_at\" AS \"otpExpiresAt\", \"User\".\"login_attempts\" AS \"loginAttempts\", \"User\".\"locked_until\" AS \"lockedUntil\", \"User\".\"last_login_at\" AS \"lastLoginAt\", \"User\".\"last_login_ip\" AS \"lastLoginIp\", \"User\".\"company_id\" AS \"companyId\", \"User\".\"created_at\" AS \"createdAt\", \"User\".\"updated_at\" AS \"updatedAt\", \"User\".\"deleted_at\" AS \"deletedAt\", \"company\".\"id\" AS \"company.id\", \"company\".\"name\" AS \"company.name\", \"company\".\"status\" AS \"company.status\", \"company\".\"features\" AS \"company.features\", \"company\".\"subscription_end_date\" AS \"company.subscriptionEndDate\" FROM \"users\" AS \"User\" LEFT OUTER JOIN \"companies\" AS \"company\" ON \"User\".\"company_id\" = \"company\".\"id\" AND (\"company\".\"deleted_at\" IS NULL) WHERE (\"User\".\"deleted_at\" IS NULL AND \"User\".\"id\" = 'fce8b418-d90b-4f18-a0e0-40347e7c62d6');\u001b[39m","timestamp":"2025-07-02 11:51:48:5148"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT count(*) AS \"count\" FROM \"customer_categories\" AS \"CustomerCategory\" WHERE (\"CustomerCategory\".\"deleted_at\" IS NULL AND \"CustomerCategory\".\"companyId\" = '8722167d-d2ae-498f-95a3-c18afcf427ae');\u001b[39m","timestamp":"2025-07-02 11:51:49:5149"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"name\", \"User\".\"email\", \"User\".\"user_name\" AS \"userName\", \"User\".\"user_type\" AS \"userType\", \"User\".\"mobile_number\" AS \"mobileNumber\", \"User\".\"status\", \"User\".\"date_of_birth\" AS \"dateOfBirth\", \"User\".\"skills\", \"User\".\"total_experience\" AS \"totalExperience\", \"User\".\"avatar\", \"User\".\"proof\", \"User\".\"address\", \"User\".\"notes\", \"User\".\"fcm_token\" AS \"fcmToken\", \"User\".\"last_notification_seen\" AS \"lastNotificationSeen\", \"User\".\"plan_id\" AS \"planId\", \"User\".\"will_expire\" AS \"willExpire\", \"User\".\"message_limit\" AS \"messageLimit\", \"User\".\"is_verified\" AS \"isVerified\", \"User\".\"email_verified_at\" AS \"emailVerifiedAt\", \"User\".\"otp\", \"User\".\"otp_expires_at\" AS \"otpExpiresAt\", \"User\".\"login_attempts\" AS \"loginAttempts\", \"User\".\"locked_until\" AS \"lockedUntil\", \"User\".\"last_login_at\" AS \"lastLoginAt\", \"User\".\"last_login_ip\" AS \"lastLoginIp\", \"User\".\"company_id\" AS \"companyId\", \"User\".\"created_at\" AS \"createdAt\", \"User\".\"updated_at\" AS \"updatedAt\", \"User\".\"deleted_at\" AS \"deletedAt\", \"company\".\"id\" AS \"company.id\", \"company\".\"name\" AS \"company.name\", \"company\".\"status\" AS \"company.status\", \"company\".\"features\" AS \"company.features\", \"company\".\"subscription_end_date\" AS \"company.subscriptionEndDate\" FROM \"users\" AS \"User\" LEFT OUTER JOIN \"companies\" AS \"company\" ON \"User\".\"company_id\" = \"company\".\"id\" AND (\"company\".\"deleted_at\" IS NULL) WHERE (\"User\".\"deleted_at\" IS NULL AND \"User\".\"id\" = 'fce8b418-d90b-4f18-a0e0-40347e7c62d6');\u001b[39m","timestamp":"2025-07-02 11:51:49:5149"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"ServiceCategory\".\"id\", count(\"ServiceCategory\".\"id\") AS \"count\" FROM \"service_categories\" AS \"ServiceCategory\" LEFT OUTER JOIN \"services\" AS \"services\" ON \"ServiceCategory\".\"id\" = \"services\".\"service_category_id\" AND (\"services\".\"deleted_at\" IS NULL) WHERE (\"ServiceCategory\".\"deleted_at\" IS NULL AND \"ServiceCategory\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') GROUP BY \"ServiceCategory\".\"id\";\u001b[39m","timestamp":"2025-07-02 11:51:49:5149"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"ServiceCategory\".\"id\", \"ServiceCategory\".\"service_category\" AS \"serviceCategory\", \"ServiceCategory\".\"service_status\" AS \"serviceStatus\", \"ServiceCategory\".\"style_view\" AS \"styleView\", \"ServiceCategory\".\"form\", \"ServiceCategory\".\"description\", \"ServiceCategory\".\"icon\", \"ServiceCategory\".\"color\", \"ServiceCategory\".\"sort_order\" AS \"sortOrder\", \"ServiceCategory\".\"is_default\" AS \"isDefault\", \"ServiceCategory\".\"created_at\" AS \"createdAt\", \"ServiceCategory\".\"updated_at\" AS \"updatedAt\", COUNT(\"services\".\"id\") AS \"servicesCount\" FROM \"service_categories\" AS \"ServiceCategory\" LEFT OUTER JOIN \"services\" AS \"services\" ON \"ServiceCategory\".\"id\" = \"services\".\"service_category_id\" AND (\"services\".\"deleted_at\" IS NULL) WHERE (\"ServiceCategory\".\"deleted_at\" IS NULL AND \"ServiceCategory\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') GROUP BY \"ServiceCategory\".\"id\" ORDER BY \"sortOrder\" ASC, \"serviceCategory\" ASC LIMIT 5 OFFSET 0;\u001b[39m","timestamp":"2025-07-02 11:51:49:5149"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"name\", \"User\".\"email\", \"User\".\"user_name\" AS \"userName\", \"User\".\"user_type\" AS \"userType\", \"User\".\"mobile_number\" AS \"mobileNumber\", \"User\".\"status\", \"User\".\"date_of_birth\" AS \"dateOfBirth\", \"User\".\"skills\", \"User\".\"total_experience\" AS \"totalExperience\", \"User\".\"avatar\", \"User\".\"proof\", \"User\".\"address\", \"User\".\"notes\", \"User\".\"fcm_token\" AS \"fcmToken\", \"User\".\"last_notification_seen\" AS \"lastNotificationSeen\", \"User\".\"plan_id\" AS \"planId\", \"User\".\"will_expire\" AS \"willExpire\", \"User\".\"message_limit\" AS \"messageLimit\", \"User\".\"is_verified\" AS \"isVerified\", \"User\".\"email_verified_at\" AS \"emailVerifiedAt\", \"User\".\"otp\", \"User\".\"otp_expires_at\" AS \"otpExpiresAt\", \"User\".\"login_attempts\" AS \"loginAttempts\", \"User\".\"locked_until\" AS \"lockedUntil\", \"User\".\"last_login_at\" AS \"lastLoginAt\", \"User\".\"last_login_ip\" AS \"lastLoginIp\", \"User\".\"company_id\" AS \"companyId\", \"User\".\"created_at\" AS \"createdAt\", \"User\".\"updated_at\" AS \"updatedAt\", \"User\".\"deleted_at\" AS \"deletedAt\", \"company\".\"id\" AS \"company.id\", \"company\".\"name\" AS \"company.name\", \"company\".\"status\" AS \"company.status\", \"company\".\"features\" AS \"company.features\", \"company\".\"subscription_end_date\" AS \"company.subscriptionEndDate\" FROM \"users\" AS \"User\" LEFT OUTER JOIN \"companies\" AS \"company\" ON \"User\".\"company_id\" = \"company\".\"id\" AND (\"company\".\"deleted_at\" IS NULL) WHERE (\"User\".\"deleted_at\" IS NULL AND \"User\".\"id\" = 'fce8b418-d90b-4f18-a0e0-40347e7c62d6');\u001b[39m","timestamp":"2025-07-02 11:51:50:5150"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"name\", \"User\".\"email\", \"User\".\"user_name\" AS \"userName\", \"User\".\"user_type\" AS \"userType\", \"User\".\"mobile_number\" AS \"mobileNumber\", \"User\".\"status\", \"User\".\"date_of_birth\" AS \"dateOfBirth\", \"User\".\"skills\", \"User\".\"total_experience\" AS \"totalExperience\", \"User\".\"avatar\", \"User\".\"proof\", \"User\".\"address\", \"User\".\"notes\", \"User\".\"fcm_token\" AS \"fcmToken\", \"User\".\"last_notification_seen\" AS \"lastNotificationSeen\", \"User\".\"plan_id\" AS \"planId\", \"User\".\"will_expire\" AS \"willExpire\", \"User\".\"message_limit\" AS \"messageLimit\", \"User\".\"is_verified\" AS \"isVerified\", \"User\".\"email_verified_at\" AS \"emailVerifiedAt\", \"User\".\"otp\", \"User\".\"otp_expires_at\" AS \"otpExpiresAt\", \"User\".\"login_attempts\" AS \"loginAttempts\", \"User\".\"locked_until\" AS \"lockedUntil\", \"User\".\"last_login_at\" AS \"lastLoginAt\", \"User\".\"last_login_ip\" AS \"lastLoginIp\", \"User\".\"company_id\" AS \"companyId\", \"User\".\"created_at\" AS \"createdAt\", \"User\".\"updated_at\" AS \"updatedAt\", \"User\".\"deleted_at\" AS \"deletedAt\", \"company\".\"id\" AS \"company.id\", \"company\".\"name\" AS \"company.name\", \"company\".\"status\" AS \"company.status\", \"company\".\"features\" AS \"company.features\", \"company\".\"subscription_end_date\" AS \"company.subscriptionEndDate\" FROM \"users\" AS \"User\" LEFT OUTER JOIN \"companies\" AS \"company\" ON \"User\".\"company_id\" = \"company\".\"id\" AND (\"company\".\"deleted_at\" IS NULL) WHERE (\"User\".\"deleted_at\" IS NULL AND \"User\".\"id\" = 'fce8b418-d90b-4f18-a0e0-40347e7c62d6');\u001b[39m","timestamp":"2025-07-02 11:51:50:5150"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"name\", \"User\".\"email\", \"User\".\"password\", \"User\".\"user_name\" AS \"userName\", \"User\".\"user_type\" AS \"userType\", \"User\".\"mobile_number\" AS \"mobileNumber\", \"User\".\"status\", \"User\".\"date_of_birth\" AS \"dateOfBirth\", \"User\".\"skills\", \"User\".\"total_experience\" AS \"totalExperience\", \"User\".\"avatar\", \"User\".\"proof\", \"User\".\"address\", \"User\".\"notes\", \"User\".\"fcm_token\" AS \"fcmToken\", \"User\".\"last_notification_seen\" AS \"lastNotificationSeen\", \"User\".\"plan_id\" AS \"planId\", \"User\".\"will_expire\" AS \"willExpire\", \"User\".\"message_limit\" AS \"messageLimit\", \"User\".\"is_verified\" AS \"isVerified\", \"User\".\"email_verified_at\" AS \"emailVerifiedAt\", \"User\".\"otp\", \"User\".\"otp_expires_at\" AS \"otpExpiresAt\", \"User\".\"login_attempts\" AS \"loginAttempts\", \"User\".\"locked_until\" AS \"lockedUntil\", \"User\".\"last_login_at\" AS \"lastLoginAt\", \"User\".\"last_login_ip\" AS \"lastLoginIp\", \"User\".\"company_id\" AS \"companyId\", \"User\".\"created_at\" AS \"createdAt\", \"User\".\"updated_at\" AS \"updatedAt\", \"User\".\"deleted_at\" AS \"deletedAt\", \"User\".\"department_id\" AS \"departmentId\", \"company\".\"id\" AS \"company.id\", \"company\".\"name\" AS \"company.name\", \"company\".\"status\" AS \"company.status\", \"company\".\"features\" AS \"company.features\", \"company\".\"subscription_end_date\" AS \"company.subscriptionEndDate\" FROM \"users\" AS \"User\" LEFT OUTER JOIN \"companies\" AS \"company\" ON \"User\".\"company_id\" = \"company\".\"id\" AND (\"company\".\"deleted_at\" IS NULL) WHERE (\"User\".\"deleted_at\" IS NULL AND \"User\".\"email\" = '<EMAIL>');\u001b[39m","timestamp":"2025-07-02 11:52:40:5240"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): UPDATE \"users\" SET \"last_login_at\"=$1,\"updated_at\"=$2 WHERE \"id\" = $3\u001b[39m","timestamp":"2025-07-02 11:52:40:5240"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"name\", \"User\".\"email\", \"User\".\"user_name\" AS \"userName\", \"User\".\"user_type\" AS \"userType\", \"User\".\"mobile_number\" AS \"mobileNumber\", \"User\".\"status\", \"User\".\"date_of_birth\" AS \"dateOfBirth\", \"User\".\"skills\", \"User\".\"total_experience\" AS \"totalExperience\", \"User\".\"avatar\", \"User\".\"proof\", \"User\".\"address\", \"User\".\"notes\", \"User\".\"fcm_token\" AS \"fcmToken\", \"User\".\"last_notification_seen\" AS \"lastNotificationSeen\", \"User\".\"plan_id\" AS \"planId\", \"User\".\"will_expire\" AS \"willExpire\", \"User\".\"message_limit\" AS \"messageLimit\", \"User\".\"is_verified\" AS \"isVerified\", \"User\".\"email_verified_at\" AS \"emailVerifiedAt\", \"User\".\"otp\", \"User\".\"otp_expires_at\" AS \"otpExpiresAt\", \"User\".\"login_attempts\" AS \"loginAttempts\", \"User\".\"locked_until\" AS \"lockedUntil\", \"User\".\"last_login_at\" AS \"lastLoginAt\", \"User\".\"last_login_ip\" AS \"lastLoginIp\", \"User\".\"company_id\" AS \"companyId\", \"User\".\"created_at\" AS \"createdAt\", \"User\".\"updated_at\" AS \"updatedAt\", \"User\".\"deleted_at\" AS \"deletedAt\", \"company\".\"id\" AS \"company.id\", \"company\".\"name\" AS \"company.name\", \"company\".\"status\" AS \"company.status\", \"company\".\"features\" AS \"company.features\", \"company\".\"subscription_end_date\" AS \"company.subscriptionEndDate\" FROM \"users\" AS \"User\" LEFT OUTER JOIN \"companies\" AS \"company\" ON \"User\".\"company_id\" = \"company\".\"id\" AND (\"company\".\"deleted_at\" IS NULL) WHERE (\"User\".\"deleted_at\" IS NULL AND \"User\".\"id\" = 'fce8b418-d90b-4f18-a0e0-40347e7c62d6');\u001b[39m","timestamp":"2025-07-02 11:52:40:5240"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT count(*) AS \"count\" FROM \"service_categories\" AS \"ServiceCategory\" WHERE (\"ServiceCategory\".\"deleted_at\" IS NULL AND \"ServiceCategory\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae');\u001b[39m","timestamp":"2025-07-02 11:52:40:5240"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT count(*) AS \"count\" FROM \"service_categories\" AS \"ServiceCategory\" WHERE (\"ServiceCategory\".\"deleted_at\" IS NULL AND (\"ServiceCategory\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae' AND \"ServiceCategory\".\"service_status\" = 1));\u001b[39m","timestamp":"2025-07-02 11:52:40:5240"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"name\", \"User\".\"email\", \"User\".\"user_name\" AS \"userName\", \"User\".\"user_type\" AS \"userType\", \"User\".\"mobile_number\" AS \"mobileNumber\", \"User\".\"status\", \"User\".\"date_of_birth\" AS \"dateOfBirth\", \"User\".\"skills\", \"User\".\"total_experience\" AS \"totalExperience\", \"User\".\"avatar\", \"User\".\"proof\", \"User\".\"address\", \"User\".\"notes\", \"User\".\"fcm_token\" AS \"fcmToken\", \"User\".\"last_notification_seen\" AS \"lastNotificationSeen\", \"User\".\"plan_id\" AS \"planId\", \"User\".\"will_expire\" AS \"willExpire\", \"User\".\"message_limit\" AS \"messageLimit\", \"User\".\"is_verified\" AS \"isVerified\", \"User\".\"email_verified_at\" AS \"emailVerifiedAt\", \"User\".\"otp\", \"User\".\"otp_expires_at\" AS \"otpExpiresAt\", \"User\".\"login_attempts\" AS \"loginAttempts\", \"User\".\"locked_until\" AS \"lockedUntil\", \"User\".\"last_login_at\" AS \"lastLoginAt\", \"User\".\"last_login_ip\" AS \"lastLoginIp\", \"User\".\"company_id\" AS \"companyId\", \"User\".\"created_at\" AS \"createdAt\", \"User\".\"updated_at\" AS \"updatedAt\", \"User\".\"deleted_at\" AS \"deletedAt\", \"company\".\"id\" AS \"company.id\", \"company\".\"name\" AS \"company.name\", \"company\".\"status\" AS \"company.status\", \"company\".\"features\" AS \"company.features\", \"company\".\"subscription_end_date\" AS \"company.subscriptionEndDate\" FROM \"users\" AS \"User\" LEFT OUTER JOIN \"companies\" AS \"company\" ON \"User\".\"company_id\" = \"company\".\"id\" AND (\"company\".\"deleted_at\" IS NULL) WHERE (\"User\".\"deleted_at\" IS NULL AND \"User\".\"id\" = 'fce8b418-d90b-4f18-a0e0-40347e7c62d6');\u001b[39m","timestamp":"2025-07-02 11:52:40:5240"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"name\", \"User\".\"email\", \"User\".\"user_name\" AS \"userName\", \"User\".\"user_type\" AS \"userType\", \"User\".\"mobile_number\" AS \"mobileNumber\", \"User\".\"status\", \"User\".\"date_of_birth\" AS \"dateOfBirth\", \"User\".\"skills\", \"User\".\"total_experience\" AS \"totalExperience\", \"User\".\"avatar\", \"User\".\"proof\", \"User\".\"address\", \"User\".\"notes\", \"User\".\"fcm_token\" AS \"fcmToken\", \"User\".\"last_notification_seen\" AS \"lastNotificationSeen\", \"User\".\"plan_id\" AS \"planId\", \"User\".\"will_expire\" AS \"willExpire\", \"User\".\"message_limit\" AS \"messageLimit\", \"User\".\"is_verified\" AS \"isVerified\", \"User\".\"email_verified_at\" AS \"emailVerifiedAt\", \"User\".\"otp\", \"User\".\"otp_expires_at\" AS \"otpExpiresAt\", \"User\".\"login_attempts\" AS \"loginAttempts\", \"User\".\"locked_until\" AS \"lockedUntil\", \"User\".\"last_login_at\" AS \"lastLoginAt\", \"User\".\"last_login_ip\" AS \"lastLoginIp\", \"User\".\"company_id\" AS \"companyId\", \"User\".\"created_at\" AS \"createdAt\", \"User\".\"updated_at\" AS \"updatedAt\", \"User\".\"deleted_at\" AS \"deletedAt\", \"company\".\"id\" AS \"company.id\", \"company\".\"name\" AS \"company.name\", \"company\".\"status\" AS \"company.status\", \"company\".\"features\" AS \"company.features\", \"company\".\"subscription_end_date\" AS \"company.subscriptionEndDate\" FROM \"users\" AS \"User\" LEFT OUTER JOIN \"companies\" AS \"company\" ON \"User\".\"company_id\" = \"company\".\"id\" AND (\"company\".\"deleted_at\" IS NULL) WHERE (\"User\".\"deleted_at\" IS NULL AND \"User\".\"id\" = 'fce8b418-d90b-4f18-a0e0-40347e7c62d6');\u001b[39m","timestamp":"2025-07-02 11:52:41:5241"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT count(*) AS \"count\" FROM \"customer_categories\" AS \"CustomerCategory\" WHERE (\"CustomerCategory\".\"deleted_at\" IS NULL AND \"CustomerCategory\".\"companyId\" = '8722167d-d2ae-498f-95a3-c18afcf427ae');\u001b[39m","timestamp":"2025-07-02 11:52:41:5241"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"name\", \"User\".\"email\", \"User\".\"user_name\" AS \"userName\", \"User\".\"user_type\" AS \"userType\", \"User\".\"mobile_number\" AS \"mobileNumber\", \"User\".\"status\", \"User\".\"date_of_birth\" AS \"dateOfBirth\", \"User\".\"skills\", \"User\".\"total_experience\" AS \"totalExperience\", \"User\".\"avatar\", \"User\".\"proof\", \"User\".\"address\", \"User\".\"notes\", \"User\".\"fcm_token\" AS \"fcmToken\", \"User\".\"last_notification_seen\" AS \"lastNotificationSeen\", \"User\".\"plan_id\" AS \"planId\", \"User\".\"will_expire\" AS \"willExpire\", \"User\".\"message_limit\" AS \"messageLimit\", \"User\".\"is_verified\" AS \"isVerified\", \"User\".\"email_verified_at\" AS \"emailVerifiedAt\", \"User\".\"otp\", \"User\".\"otp_expires_at\" AS \"otpExpiresAt\", \"User\".\"login_attempts\" AS \"loginAttempts\", \"User\".\"locked_until\" AS \"lockedUntil\", \"User\".\"last_login_at\" AS \"lastLoginAt\", \"User\".\"last_login_ip\" AS \"lastLoginIp\", \"User\".\"company_id\" AS \"companyId\", \"User\".\"created_at\" AS \"createdAt\", \"User\".\"updated_at\" AS \"updatedAt\", \"User\".\"deleted_at\" AS \"deletedAt\", \"company\".\"id\" AS \"company.id\", \"company\".\"name\" AS \"company.name\", \"company\".\"status\" AS \"company.status\", \"company\".\"features\" AS \"company.features\", \"company\".\"subscription_end_date\" AS \"company.subscriptionEndDate\" FROM \"users\" AS \"User\" LEFT OUTER JOIN \"companies\" AS \"company\" ON \"User\".\"company_id\" = \"company\".\"id\" AND (\"company\".\"deleted_at\" IS NULL) WHERE (\"User\".\"deleted_at\" IS NULL AND \"User\".\"id\" = 'fce8b418-d90b-4f18-a0e0-40347e7c62d6');\u001b[39m","timestamp":"2025-07-02 11:52:41:5241"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"ServiceCategory\".\"id\", count(\"ServiceCategory\".\"id\") AS \"count\" FROM \"service_categories\" AS \"ServiceCategory\" LEFT OUTER JOIN \"services\" AS \"services\" ON \"ServiceCategory\".\"id\" = \"services\".\"service_category_id\" AND (\"services\".\"deleted_at\" IS NULL) WHERE (\"ServiceCategory\".\"deleted_at\" IS NULL AND \"ServiceCategory\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') GROUP BY \"ServiceCategory\".\"id\";\u001b[39m","timestamp":"2025-07-02 11:52:42:5242"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"ServiceCategory\".\"id\", \"ServiceCategory\".\"service_category\" AS \"serviceCategory\", \"ServiceCategory\".\"service_status\" AS \"serviceStatus\", \"ServiceCategory\".\"style_view\" AS \"styleView\", \"ServiceCategory\".\"form\", \"ServiceCategory\".\"description\", \"ServiceCategory\".\"icon\", \"ServiceCategory\".\"color\", \"ServiceCategory\".\"sort_order\" AS \"sortOrder\", \"ServiceCategory\".\"is_default\" AS \"isDefault\", \"ServiceCategory\".\"created_at\" AS \"createdAt\", \"ServiceCategory\".\"updated_at\" AS \"updatedAt\", COUNT(\"services\".\"id\") AS \"servicesCount\" FROM \"service_categories\" AS \"ServiceCategory\" LEFT OUTER JOIN \"services\" AS \"services\" ON \"ServiceCategory\".\"id\" = \"services\".\"service_category_id\" AND (\"services\".\"deleted_at\" IS NULL) WHERE (\"ServiceCategory\".\"deleted_at\" IS NULL AND \"ServiceCategory\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') GROUP BY \"ServiceCategory\".\"id\" ORDER BY \"sortOrder\" ASC, \"serviceCategory\" ASC LIMIT 5 OFFSET 0;\u001b[39m","timestamp":"2025-07-02 11:52:42:5242"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"name\", \"User\".\"email\", \"User\".\"user_name\" AS \"userName\", \"User\".\"user_type\" AS \"userType\", \"User\".\"mobile_number\" AS \"mobileNumber\", \"User\".\"status\", \"User\".\"date_of_birth\" AS \"dateOfBirth\", \"User\".\"skills\", \"User\".\"total_experience\" AS \"totalExperience\", \"User\".\"avatar\", \"User\".\"proof\", \"User\".\"address\", \"User\".\"notes\", \"User\".\"fcm_token\" AS \"fcmToken\", \"User\".\"last_notification_seen\" AS \"lastNotificationSeen\", \"User\".\"plan_id\" AS \"planId\", \"User\".\"will_expire\" AS \"willExpire\", \"User\".\"message_limit\" AS \"messageLimit\", \"User\".\"is_verified\" AS \"isVerified\", \"User\".\"email_verified_at\" AS \"emailVerifiedAt\", \"User\".\"otp\", \"User\".\"otp_expires_at\" AS \"otpExpiresAt\", \"User\".\"login_attempts\" AS \"loginAttempts\", \"User\".\"locked_until\" AS \"lockedUntil\", \"User\".\"last_login_at\" AS \"lastLoginAt\", \"User\".\"last_login_ip\" AS \"lastLoginIp\", \"User\".\"company_id\" AS \"companyId\", \"User\".\"created_at\" AS \"createdAt\", \"User\".\"updated_at\" AS \"updatedAt\", \"User\".\"deleted_at\" AS \"deletedAt\", \"company\".\"id\" AS \"company.id\", \"company\".\"name\" AS \"company.name\", \"company\".\"status\" AS \"company.status\", \"company\".\"features\" AS \"company.features\", \"company\".\"subscription_end_date\" AS \"company.subscriptionEndDate\" FROM \"users\" AS \"User\" LEFT OUTER JOIN \"companies\" AS \"company\" ON \"User\".\"company_id\" = \"company\".\"id\" AND (\"company\".\"deleted_at\" IS NULL) WHERE (\"User\".\"deleted_at\" IS NULL AND \"User\".\"id\" = 'fce8b418-d90b-4f18-a0e0-40347e7c62d6');\u001b[39m","timestamp":"2025-07-02 11:52:42:5242"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"name\", \"User\".\"email\", \"User\".\"user_name\" AS \"userName\", \"User\".\"user_type\" AS \"userType\", \"User\".\"mobile_number\" AS \"mobileNumber\", \"User\".\"status\", \"User\".\"date_of_birth\" AS \"dateOfBirth\", \"User\".\"skills\", \"User\".\"total_experience\" AS \"totalExperience\", \"User\".\"avatar\", \"User\".\"proof\", \"User\".\"address\", \"User\".\"notes\", \"User\".\"fcm_token\" AS \"fcmToken\", \"User\".\"last_notification_seen\" AS \"lastNotificationSeen\", \"User\".\"plan_id\" AS \"planId\", \"User\".\"will_expire\" AS \"willExpire\", \"User\".\"message_limit\" AS \"messageLimit\", \"User\".\"is_verified\" AS \"isVerified\", \"User\".\"email_verified_at\" AS \"emailVerifiedAt\", \"User\".\"otp\", \"User\".\"otp_expires_at\" AS \"otpExpiresAt\", \"User\".\"login_attempts\" AS \"loginAttempts\", \"User\".\"locked_until\" AS \"lockedUntil\", \"User\".\"last_login_at\" AS \"lastLoginAt\", \"User\".\"last_login_ip\" AS \"lastLoginIp\", \"User\".\"company_id\" AS \"companyId\", \"User\".\"created_at\" AS \"createdAt\", \"User\".\"updated_at\" AS \"updatedAt\", \"User\".\"deleted_at\" AS \"deletedAt\", \"company\".\"id\" AS \"company.id\", \"company\".\"name\" AS \"company.name\", \"company\".\"status\" AS \"company.status\", \"company\".\"features\" AS \"company.features\", \"company\".\"subscription_end_date\" AS \"company.subscriptionEndDate\" FROM \"users\" AS \"User\" LEFT OUTER JOIN \"companies\" AS \"company\" ON \"User\".\"company_id\" = \"company\".\"id\" AND (\"company\".\"deleted_at\" IS NULL) WHERE (\"User\".\"deleted_at\" IS NULL AND \"User\".\"id\" = 'fce8b418-d90b-4f18-a0e0-40347e7c62d6');\u001b[39m","timestamp":"2025-07-02 11:52:42:5242"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT count(*) AS \"count\" FROM \"customer_categories\" AS \"CustomerCategory\" WHERE (\"CustomerCategory\".\"deleted_at\" IS NULL);\u001b[39m","timestamp":"2025-07-02 11:54:30:5430"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT count(*) AS \"count\" FROM \"customer_categories\" AS \"CustomerCategory\";\u001b[39m","timestamp":"2025-07-02 11:55:44:5544"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT count(*) AS \"count\" FROM \"customer_categories\" AS \"CustomerCategory\" WHERE \"CustomerCategory\".\"companyId\" = '8722167d-d2ae-498f-95a3-c18afcf427ae';\u001b[39m","timestamp":"2025-07-02 11:55:44:5544"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"name\", \"User\".\"email\", \"User\".\"password\", \"User\".\"user_name\" AS \"userName\", \"User\".\"user_type\" AS \"userType\", \"User\".\"mobile_number\" AS \"mobileNumber\", \"User\".\"status\", \"User\".\"date_of_birth\" AS \"dateOfBirth\", \"User\".\"skills\", \"User\".\"total_experience\" AS \"totalExperience\", \"User\".\"avatar\", \"User\".\"proof\", \"User\".\"address\", \"User\".\"notes\", \"User\".\"fcm_token\" AS \"fcmToken\", \"User\".\"last_notification_seen\" AS \"lastNotificationSeen\", \"User\".\"plan_id\" AS \"planId\", \"User\".\"will_expire\" AS \"willExpire\", \"User\".\"message_limit\" AS \"messageLimit\", \"User\".\"is_verified\" AS \"isVerified\", \"User\".\"email_verified_at\" AS \"emailVerifiedAt\", \"User\".\"otp\", \"User\".\"otp_expires_at\" AS \"otpExpiresAt\", \"User\".\"login_attempts\" AS \"loginAttempts\", \"User\".\"locked_until\" AS \"lockedUntil\", \"User\".\"last_login_at\" AS \"lastLoginAt\", \"User\".\"last_login_ip\" AS \"lastLoginIp\", \"User\".\"company_id\" AS \"companyId\", \"User\".\"created_at\" AS \"createdAt\", \"User\".\"updated_at\" AS \"updatedAt\", \"User\".\"deleted_at\" AS \"deletedAt\", \"User\".\"department_id\" AS \"departmentId\", \"company\".\"id\" AS \"company.id\", \"company\".\"name\" AS \"company.name\", \"company\".\"status\" AS \"company.status\", \"company\".\"features\" AS \"company.features\", \"company\".\"subscription_end_date\" AS \"company.subscriptionEndDate\" FROM \"users\" AS \"User\" LEFT OUTER JOIN \"companies\" AS \"company\" ON \"User\".\"company_id\" = \"company\".\"id\" AND (\"company\".\"deleted_at\" IS NULL) WHERE (\"User\".\"deleted_at\" IS NULL AND \"User\".\"email\" = '<EMAIL>');\u001b[39m","timestamp":"2025-07-02 11:57:31:5731"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): UPDATE \"users\" SET \"last_login_at\"=$1,\"updated_at\"=$2 WHERE \"id\" = $3\u001b[39m","timestamp":"2025-07-02 11:57:31:5731"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"name\", \"User\".\"email\", \"User\".\"user_name\" AS \"userName\", \"User\".\"user_type\" AS \"userType\", \"User\".\"mobile_number\" AS \"mobileNumber\", \"User\".\"status\", \"User\".\"date_of_birth\" AS \"dateOfBirth\", \"User\".\"skills\", \"User\".\"total_experience\" AS \"totalExperience\", \"User\".\"avatar\", \"User\".\"proof\", \"User\".\"address\", \"User\".\"notes\", \"User\".\"fcm_token\" AS \"fcmToken\", \"User\".\"last_notification_seen\" AS \"lastNotificationSeen\", \"User\".\"plan_id\" AS \"planId\", \"User\".\"will_expire\" AS \"willExpire\", \"User\".\"message_limit\" AS \"messageLimit\", \"User\".\"is_verified\" AS \"isVerified\", \"User\".\"email_verified_at\" AS \"emailVerifiedAt\", \"User\".\"otp\", \"User\".\"otp_expires_at\" AS \"otpExpiresAt\", \"User\".\"login_attempts\" AS \"loginAttempts\", \"User\".\"locked_until\" AS \"lockedUntil\", \"User\".\"last_login_at\" AS \"lastLoginAt\", \"User\".\"last_login_ip\" AS \"lastLoginIp\", \"User\".\"company_id\" AS \"companyId\", \"User\".\"created_at\" AS \"createdAt\", \"User\".\"updated_at\" AS \"updatedAt\", \"User\".\"deleted_at\" AS \"deletedAt\", \"company\".\"id\" AS \"company.id\", \"company\".\"name\" AS \"company.name\", \"company\".\"status\" AS \"company.status\", \"company\".\"features\" AS \"company.features\", \"company\".\"subscription_end_date\" AS \"company.subscriptionEndDate\" FROM \"users\" AS \"User\" LEFT OUTER JOIN \"companies\" AS \"company\" ON \"User\".\"company_id\" = \"company\".\"id\" AND (\"company\".\"deleted_at\" IS NULL) WHERE (\"User\".\"deleted_at\" IS NULL AND \"User\".\"id\" = 'fce8b418-d90b-4f18-a0e0-40347e7c62d6');\u001b[39m","timestamp":"2025-07-02 11:57:32:5732"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"name\", \"User\".\"email\", \"User\".\"user_name\" AS \"userName\", \"User\".\"user_type\" AS \"userType\", \"User\".\"mobile_number\" AS \"mobileNumber\", \"User\".\"status\", \"User\".\"date_of_birth\" AS \"dateOfBirth\", \"User\".\"skills\", \"User\".\"total_experience\" AS \"totalExperience\", \"User\".\"avatar\", \"User\".\"proof\", \"User\".\"address\", \"User\".\"notes\", \"User\".\"fcm_token\" AS \"fcmToken\", \"User\".\"last_notification_seen\" AS \"lastNotificationSeen\", \"User\".\"plan_id\" AS \"planId\", \"User\".\"will_expire\" AS \"willExpire\", \"User\".\"message_limit\" AS \"messageLimit\", \"User\".\"is_verified\" AS \"isVerified\", \"User\".\"email_verified_at\" AS \"emailVerifiedAt\", \"User\".\"otp\", \"User\".\"otp_expires_at\" AS \"otpExpiresAt\", \"User\".\"login_attempts\" AS \"loginAttempts\", \"User\".\"locked_until\" AS \"lockedUntil\", \"User\".\"last_login_at\" AS \"lastLoginAt\", \"User\".\"last_login_ip\" AS \"lastLoginIp\", \"User\".\"company_id\" AS \"companyId\", \"User\".\"created_at\" AS \"createdAt\", \"User\".\"updated_at\" AS \"updatedAt\", \"User\".\"deleted_at\" AS \"deletedAt\", \"company\".\"id\" AS \"company.id\", \"company\".\"name\" AS \"company.name\", \"company\".\"status\" AS \"company.status\", \"company\".\"features\" AS \"company.features\", \"company\".\"subscription_end_date\" AS \"company.subscriptionEndDate\" FROM \"users\" AS \"User\" LEFT OUTER JOIN \"companies\" AS \"company\" ON \"User\".\"company_id\" = \"company\".\"id\" AND (\"company\".\"deleted_at\" IS NULL) WHERE (\"User\".\"deleted_at\" IS NULL AND \"User\".\"id\" = 'fce8b418-d90b-4f18-a0e0-40347e7c62d6');\u001b[39m","timestamp":"2025-07-02 11:57:32:5732"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT count(*) AS \"count\" FROM \"customer_categories\" AS \"CustomerCategory\";\u001b[39m","timestamp":"2025-07-02 11:59:13:5913"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT count(*) AS \"count\" FROM \"customer_categories\" AS \"CustomerCategory\" WHERE \"CustomerCategory\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae';\u001b[39m","timestamp":"2025-07-02 11:59:13:5913"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT count(*) AS \"count\" FROM \"customer_categories\" AS \"CustomerCategory\";\u001b[39m","timestamp":"2025-07-02 11:59:44:5944"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"id\", \"category_name\" AS \"categoryName\", \"description\", \"color\", \"is_active\" AS \"isActive\", \"company_id\" AS \"companyId\", \"sort_order\" AS \"sortOrder\", \"discount_percentage\" AS \"discountPercentage\", \"special_terms\" AS \"specialTerms\", \"created_at\" AS \"createdAt\", \"updated_at\" AS \"updatedAt\" FROM \"customer_categories\" AS \"CustomerCategory\" LIMIT 3;\u001b[39m","timestamp":"2025-07-02 11:59:44:5944"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"id\", \"category_name\" AS \"categoryName\", \"description\", \"color\", \"is_active\" AS \"isActive\", \"company_id\" AS \"companyId\", \"sort_order\" AS \"sortOrder\", \"discount_percentage\" AS \"discountPercentage\", \"special_terms\" AS \"specialTerms\", \"created_at\" AS \"createdAt\", \"updated_at\" AS \"updatedAt\" FROM \"customer_categories\" AS \"CustomerCategory\" LIMIT 1;\u001b[39m","timestamp":"2025-07-02 11:59:58:5958"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"name\", \"User\".\"email\", \"User\".\"password\", \"User\".\"user_name\" AS \"userName\", \"User\".\"user_type\" AS \"userType\", \"User\".\"mobile_number\" AS \"mobileNumber\", \"User\".\"status\", \"User\".\"date_of_birth\" AS \"dateOfBirth\", \"User\".\"skills\", \"User\".\"total_experience\" AS \"totalExperience\", \"User\".\"avatar\", \"User\".\"proof\", \"User\".\"address\", \"User\".\"notes\", \"User\".\"fcm_token\" AS \"fcmToken\", \"User\".\"last_notification_seen\" AS \"lastNotificationSeen\", \"User\".\"plan_id\" AS \"planId\", \"User\".\"will_expire\" AS \"willExpire\", \"User\".\"message_limit\" AS \"messageLimit\", \"User\".\"is_verified\" AS \"isVerified\", \"User\".\"email_verified_at\" AS \"emailVerifiedAt\", \"User\".\"otp\", \"User\".\"otp_expires_at\" AS \"otpExpiresAt\", \"User\".\"login_attempts\" AS \"loginAttempts\", \"User\".\"locked_until\" AS \"lockedUntil\", \"User\".\"last_login_at\" AS \"lastLoginAt\", \"User\".\"last_login_ip\" AS \"lastLoginIp\", \"User\".\"company_id\" AS \"companyId\", \"User\".\"created_at\" AS \"createdAt\", \"User\".\"updated_at\" AS \"updatedAt\", \"User\".\"deleted_at\" AS \"deletedAt\", \"User\".\"department_id\" AS \"departmentId\", \"company\".\"id\" AS \"company.id\", \"company\".\"name\" AS \"company.name\", \"company\".\"status\" AS \"company.status\", \"company\".\"features\" AS \"company.features\", \"company\".\"subscription_end_date\" AS \"company.subscriptionEndDate\" FROM \"users\" AS \"User\" LEFT OUTER JOIN \"companies\" AS \"company\" ON \"User\".\"company_id\" = \"company\".\"id\" AND (\"company\".\"deleted_at\" IS NULL) WHERE (\"User\".\"deleted_at\" IS NULL AND \"User\".\"email\" = '<EMAIL>');\u001b[39m","timestamp":"2025-07-02 12:01:18:118"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): UPDATE \"users\" SET \"last_login_at\"=$1,\"updated_at\"=$2 WHERE \"id\" = $3\u001b[39m","timestamp":"2025-07-02 12:01:18:118"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"name\", \"User\".\"email\", \"User\".\"user_name\" AS \"userName\", \"User\".\"user_type\" AS \"userType\", \"User\".\"mobile_number\" AS \"mobileNumber\", \"User\".\"status\", \"User\".\"date_of_birth\" AS \"dateOfBirth\", \"User\".\"skills\", \"User\".\"total_experience\" AS \"totalExperience\", \"User\".\"avatar\", \"User\".\"proof\", \"User\".\"address\", \"User\".\"notes\", \"User\".\"fcm_token\" AS \"fcmToken\", \"User\".\"last_notification_seen\" AS \"lastNotificationSeen\", \"User\".\"plan_id\" AS \"planId\", \"User\".\"will_expire\" AS \"willExpire\", \"User\".\"message_limit\" AS \"messageLimit\", \"User\".\"is_verified\" AS \"isVerified\", \"User\".\"email_verified_at\" AS \"emailVerifiedAt\", \"User\".\"otp\", \"User\".\"otp_expires_at\" AS \"otpExpiresAt\", \"User\".\"login_attempts\" AS \"loginAttempts\", \"User\".\"locked_until\" AS \"lockedUntil\", \"User\".\"last_login_at\" AS \"lastLoginAt\", \"User\".\"last_login_ip\" AS \"lastLoginIp\", \"User\".\"company_id\" AS \"companyId\", \"User\".\"created_at\" AS \"createdAt\", \"User\".\"updated_at\" AS \"updatedAt\", \"User\".\"deleted_at\" AS \"deletedAt\", \"company\".\"id\" AS \"company.id\", \"company\".\"name\" AS \"company.name\", \"company\".\"status\" AS \"company.status\", \"company\".\"features\" AS \"company.features\", \"company\".\"subscription_end_date\" AS \"company.subscriptionEndDate\" FROM \"users\" AS \"User\" LEFT OUTER JOIN \"companies\" AS \"company\" ON \"User\".\"company_id\" = \"company\".\"id\" AND (\"company\".\"deleted_at\" IS NULL) WHERE (\"User\".\"deleted_at\" IS NULL AND \"User\".\"id\" = 'fce8b418-d90b-4f18-a0e0-40347e7c62d6');\u001b[39m","timestamp":"2025-07-02 12:01:18:118"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT count(*) AS \"count\" FROM \"service_categories\" AS \"ServiceCategory\" WHERE (\"ServiceCategory\".\"deleted_at\" IS NULL AND \"ServiceCategory\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae');\u001b[39m","timestamp":"2025-07-02 12:01:19:119"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT count(*) AS \"count\" FROM \"service_categories\" AS \"ServiceCategory\" WHERE (\"ServiceCategory\".\"deleted_at\" IS NULL AND (\"ServiceCategory\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae' AND \"ServiceCategory\".\"service_status\" = 1));\u001b[39m","timestamp":"2025-07-02 12:01:19:119"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"name\", \"User\".\"email\", \"User\".\"user_name\" AS \"userName\", \"User\".\"user_type\" AS \"userType\", \"User\".\"mobile_number\" AS \"mobileNumber\", \"User\".\"status\", \"User\".\"date_of_birth\" AS \"dateOfBirth\", \"User\".\"skills\", \"User\".\"total_experience\" AS \"totalExperience\", \"User\".\"avatar\", \"User\".\"proof\", \"User\".\"address\", \"User\".\"notes\", \"User\".\"fcm_token\" AS \"fcmToken\", \"User\".\"last_notification_seen\" AS \"lastNotificationSeen\", \"User\".\"plan_id\" AS \"planId\", \"User\".\"will_expire\" AS \"willExpire\", \"User\".\"message_limit\" AS \"messageLimit\", \"User\".\"is_verified\" AS \"isVerified\", \"User\".\"email_verified_at\" AS \"emailVerifiedAt\", \"User\".\"otp\", \"User\".\"otp_expires_at\" AS \"otpExpiresAt\", \"User\".\"login_attempts\" AS \"loginAttempts\", \"User\".\"locked_until\" AS \"lockedUntil\", \"User\".\"last_login_at\" AS \"lastLoginAt\", \"User\".\"last_login_ip\" AS \"lastLoginIp\", \"User\".\"company_id\" AS \"companyId\", \"User\".\"created_at\" AS \"createdAt\", \"User\".\"updated_at\" AS \"updatedAt\", \"User\".\"deleted_at\" AS \"deletedAt\", \"company\".\"id\" AS \"company.id\", \"company\".\"name\" AS \"company.name\", \"company\".\"status\" AS \"company.status\", \"company\".\"features\" AS \"company.features\", \"company\".\"subscription_end_date\" AS \"company.subscriptionEndDate\" FROM \"users\" AS \"User\" LEFT OUTER JOIN \"companies\" AS \"company\" ON \"User\".\"company_id\" = \"company\".\"id\" AND (\"company\".\"deleted_at\" IS NULL) WHERE (\"User\".\"deleted_at\" IS NULL AND \"User\".\"id\" = 'fce8b418-d90b-4f18-a0e0-40347e7c62d6');\u001b[39m","timestamp":"2025-07-02 12:01:19:119"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"name\", \"User\".\"email\", \"User\".\"user_name\" AS \"userName\", \"User\".\"user_type\" AS \"userType\", \"User\".\"mobile_number\" AS \"mobileNumber\", \"User\".\"status\", \"User\".\"date_of_birth\" AS \"dateOfBirth\", \"User\".\"skills\", \"User\".\"total_experience\" AS \"totalExperience\", \"User\".\"avatar\", \"User\".\"proof\", \"User\".\"address\", \"User\".\"notes\", \"User\".\"fcm_token\" AS \"fcmToken\", \"User\".\"last_notification_seen\" AS \"lastNotificationSeen\", \"User\".\"plan_id\" AS \"planId\", \"User\".\"will_expire\" AS \"willExpire\", \"User\".\"message_limit\" AS \"messageLimit\", \"User\".\"is_verified\" AS \"isVerified\", \"User\".\"email_verified_at\" AS \"emailVerifiedAt\", \"User\".\"otp\", \"User\".\"otp_expires_at\" AS \"otpExpiresAt\", \"User\".\"login_attempts\" AS \"loginAttempts\", \"User\".\"locked_until\" AS \"lockedUntil\", \"User\".\"last_login_at\" AS \"lastLoginAt\", \"User\".\"last_login_ip\" AS \"lastLoginIp\", \"User\".\"company_id\" AS \"companyId\", \"User\".\"created_at\" AS \"createdAt\", \"User\".\"updated_at\" AS \"updatedAt\", \"User\".\"deleted_at\" AS \"deletedAt\", \"company\".\"id\" AS \"company.id\", \"company\".\"name\" AS \"company.name\", \"company\".\"status\" AS \"company.status\", \"company\".\"features\" AS \"company.features\", \"company\".\"subscription_end_date\" AS \"company.subscriptionEndDate\" FROM \"users\" AS \"User\" LEFT OUTER JOIN \"companies\" AS \"company\" ON \"User\".\"company_id\" = \"company\".\"id\" AND (\"company\".\"deleted_at\" IS NULL) WHERE (\"User\".\"deleted_at\" IS NULL AND \"User\".\"id\" = 'fce8b418-d90b-4f18-a0e0-40347e7c62d6');\u001b[39m","timestamp":"2025-07-02 12:01:20:120"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT count(*) AS \"count\" FROM \"customer_categories\" AS \"CustomerCategory\" WHERE (\"CustomerCategory\".\"deleted_at\" IS NULL AND \"CustomerCategory\".\"companyId\" = '8722167d-d2ae-498f-95a3-c18afcf427ae');\u001b[39m","timestamp":"2025-07-02 12:01:20:120"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"name\", \"User\".\"email\", \"User\".\"user_name\" AS \"userName\", \"User\".\"user_type\" AS \"userType\", \"User\".\"mobile_number\" AS \"mobileNumber\", \"User\".\"status\", \"User\".\"date_of_birth\" AS \"dateOfBirth\", \"User\".\"skills\", \"User\".\"total_experience\" AS \"totalExperience\", \"User\".\"avatar\", \"User\".\"proof\", \"User\".\"address\", \"User\".\"notes\", \"User\".\"fcm_token\" AS \"fcmToken\", \"User\".\"last_notification_seen\" AS \"lastNotificationSeen\", \"User\".\"plan_id\" AS \"planId\", \"User\".\"will_expire\" AS \"willExpire\", \"User\".\"message_limit\" AS \"messageLimit\", \"User\".\"is_verified\" AS \"isVerified\", \"User\".\"email_verified_at\" AS \"emailVerifiedAt\", \"User\".\"otp\", \"User\".\"otp_expires_at\" AS \"otpExpiresAt\", \"User\".\"login_attempts\" AS \"loginAttempts\", \"User\".\"locked_until\" AS \"lockedUntil\", \"User\".\"last_login_at\" AS \"lastLoginAt\", \"User\".\"last_login_ip\" AS \"lastLoginIp\", \"User\".\"company_id\" AS \"companyId\", \"User\".\"created_at\" AS \"createdAt\", \"User\".\"updated_at\" AS \"updatedAt\", \"User\".\"deleted_at\" AS \"deletedAt\", \"company\".\"id\" AS \"company.id\", \"company\".\"name\" AS \"company.name\", \"company\".\"status\" AS \"company.status\", \"company\".\"features\" AS \"company.features\", \"company\".\"subscription_end_date\" AS \"company.subscriptionEndDate\" FROM \"users\" AS \"User\" LEFT OUTER JOIN \"companies\" AS \"company\" ON \"User\".\"company_id\" = \"company\".\"id\" AND (\"company\".\"deleted_at\" IS NULL) WHERE (\"User\".\"deleted_at\" IS NULL AND \"User\".\"id\" = 'fce8b418-d90b-4f18-a0e0-40347e7c62d6');\u001b[39m","timestamp":"2025-07-02 12:01:20:120"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"ServiceCategory\".\"id\", count(\"ServiceCategory\".\"id\") AS \"count\" FROM \"service_categories\" AS \"ServiceCategory\" LEFT OUTER JOIN \"services\" AS \"services\" ON \"ServiceCategory\".\"id\" = \"services\".\"service_category_id\" AND (\"services\".\"deleted_at\" IS NULL) WHERE (\"ServiceCategory\".\"deleted_at\" IS NULL AND \"ServiceCategory\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') GROUP BY \"ServiceCategory\".\"id\";\u001b[39m","timestamp":"2025-07-02 12:01:20:120"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"ServiceCategory\".\"id\", \"ServiceCategory\".\"service_category\" AS \"serviceCategory\", \"ServiceCategory\".\"service_status\" AS \"serviceStatus\", \"ServiceCategory\".\"style_view\" AS \"styleView\", \"ServiceCategory\".\"form\", \"ServiceCategory\".\"description\", \"ServiceCategory\".\"icon\", \"ServiceCategory\".\"color\", \"ServiceCategory\".\"sort_order\" AS \"sortOrder\", \"ServiceCategory\".\"is_default\" AS \"isDefault\", \"ServiceCategory\".\"created_at\" AS \"createdAt\", \"ServiceCategory\".\"updated_at\" AS \"updatedAt\", COUNT(\"services\".\"id\") AS \"servicesCount\" FROM \"service_categories\" AS \"ServiceCategory\" LEFT OUTER JOIN \"services\" AS \"services\" ON \"ServiceCategory\".\"id\" = \"services\".\"service_category_id\" AND (\"services\".\"deleted_at\" IS NULL) WHERE (\"ServiceCategory\".\"deleted_at\" IS NULL AND \"ServiceCategory\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') GROUP BY \"ServiceCategory\".\"id\" ORDER BY \"sortOrder\" ASC, \"serviceCategory\" ASC LIMIT 5 OFFSET 0;\u001b[39m","timestamp":"2025-07-02 12:01:20:120"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"name\", \"User\".\"email\", \"User\".\"user_name\" AS \"userName\", \"User\".\"user_type\" AS \"userType\", \"User\".\"mobile_number\" AS \"mobileNumber\", \"User\".\"status\", \"User\".\"date_of_birth\" AS \"dateOfBirth\", \"User\".\"skills\", \"User\".\"total_experience\" AS \"totalExperience\", \"User\".\"avatar\", \"User\".\"proof\", \"User\".\"address\", \"User\".\"notes\", \"User\".\"fcm_token\" AS \"fcmToken\", \"User\".\"last_notification_seen\" AS \"lastNotificationSeen\", \"User\".\"plan_id\" AS \"planId\", \"User\".\"will_expire\" AS \"willExpire\", \"User\".\"message_limit\" AS \"messageLimit\", \"User\".\"is_verified\" AS \"isVerified\", \"User\".\"email_verified_at\" AS \"emailVerifiedAt\", \"User\".\"otp\", \"User\".\"otp_expires_at\" AS \"otpExpiresAt\", \"User\".\"login_attempts\" AS \"loginAttempts\", \"User\".\"locked_until\" AS \"lockedUntil\", \"User\".\"last_login_at\" AS \"lastLoginAt\", \"User\".\"last_login_ip\" AS \"lastLoginIp\", \"User\".\"company_id\" AS \"companyId\", \"User\".\"created_at\" AS \"createdAt\", \"User\".\"updated_at\" AS \"updatedAt\", \"User\".\"deleted_at\" AS \"deletedAt\", \"company\".\"id\" AS \"company.id\", \"company\".\"name\" AS \"company.name\", \"company\".\"status\" AS \"company.status\", \"company\".\"features\" AS \"company.features\", \"company\".\"subscription_end_date\" AS \"company.subscriptionEndDate\" FROM \"users\" AS \"User\" LEFT OUTER JOIN \"companies\" AS \"company\" ON \"User\".\"company_id\" = \"company\".\"id\" AND (\"company\".\"deleted_at\" IS NULL) WHERE (\"User\".\"deleted_at\" IS NULL AND \"User\".\"id\" = 'fce8b418-d90b-4f18-a0e0-40347e7c62d6');\u001b[39m","timestamp":"2025-07-02 12:01:20:120"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"name\", \"User\".\"email\", \"User\".\"user_name\" AS \"userName\", \"User\".\"user_type\" AS \"userType\", \"User\".\"mobile_number\" AS \"mobileNumber\", \"User\".\"status\", \"User\".\"date_of_birth\" AS \"dateOfBirth\", \"User\".\"skills\", \"User\".\"total_experience\" AS \"totalExperience\", \"User\".\"avatar\", \"User\".\"proof\", \"User\".\"address\", \"User\".\"notes\", \"User\".\"fcm_token\" AS \"fcmToken\", \"User\".\"last_notification_seen\" AS \"lastNotificationSeen\", \"User\".\"plan_id\" AS \"planId\", \"User\".\"will_expire\" AS \"willExpire\", \"User\".\"message_limit\" AS \"messageLimit\", \"User\".\"is_verified\" AS \"isVerified\", \"User\".\"email_verified_at\" AS \"emailVerifiedAt\", \"User\".\"otp\", \"User\".\"otp_expires_at\" AS \"otpExpiresAt\", \"User\".\"login_attempts\" AS \"loginAttempts\", \"User\".\"locked_until\" AS \"lockedUntil\", \"User\".\"last_login_at\" AS \"lastLoginAt\", \"User\".\"last_login_ip\" AS \"lastLoginIp\", \"User\".\"company_id\" AS \"companyId\", \"User\".\"created_at\" AS \"createdAt\", \"User\".\"updated_at\" AS \"updatedAt\", \"User\".\"deleted_at\" AS \"deletedAt\", \"company\".\"id\" AS \"company.id\", \"company\".\"name\" AS \"company.name\", \"company\".\"status\" AS \"company.status\", \"company\".\"features\" AS \"company.features\", \"company\".\"subscription_end_date\" AS \"company.subscriptionEndDate\" FROM \"users\" AS \"User\" LEFT OUTER JOIN \"companies\" AS \"company\" ON \"User\".\"company_id\" = \"company\".\"id\" AND (\"company\".\"deleted_at\" IS NULL) WHERE (\"User\".\"deleted_at\" IS NULL AND \"User\".\"id\" = 'fce8b418-d90b-4f18-a0e0-40347e7c62d6');\u001b[39m","timestamp":"2025-07-02 12:01:21:121"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT count(*) AS \"count\" FROM \"customer_categories\" AS \"CustomerCategory\";\u001b[39m","timestamp":"2025-07-02 12:01:36:136"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT count(*) AS \"count\" FROM \"customer_categories\" AS \"CustomerCategory\" WHERE \"CustomerCategory\".\"is_active\" = true;\u001b[39m","timestamp":"2025-07-02 12:01:37:137"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT 1+1 AS result\u001b[39m","timestamp":"2025-07-02 12:02:47:247"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m✅ Database connection established successfully\u001b[39m","timestamp":"2025-07-02 12:02:47:247"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔧 Setting up port with automatic conflict resolution...\u001b[39m","timestamp":"2025-07-02 12:02:47:247"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔍 Finding available port...\u001b[39m","timestamp":"2025-07-02 12:02:47:247"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m✅ Port 8080 is available\u001b[39m","timestamp":"2025-07-02 12:02:47:247"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 Port 8080 is ready for use\u001b[39m","timestamp":"2025-07-02 12:02:47:247"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 Server running on port 8080\u001b[39m","timestamp":"2025-07-02 12:02:47:247"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📚 Environment: development\u001b[39m","timestamp":"2025-07-02 12:02:47:247"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🌐 Frontend & Backend: http://localhost:8080\u001b[39m","timestamp":"2025-07-02 12:02:47:247"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🔗 API Base URL: http://localhost:8080/api\u001b[39m","timestamp":"2025-07-02 12:02:47:247"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📖 API Documentation: http://localhost:8080/api/docs\u001b[39m","timestamp":"2025-07-02 12:02:47:247"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🏥 Health Check: http://localhost:8080/health\u001b[39m","timestamp":"2025-07-02 12:02:47:247"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m✅ TrackNew is ready for use!\u001b[39m","timestamp":"2025-07-02 12:02:47:247"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"name\", \"User\".\"email\", \"User\".\"password\", \"User\".\"user_name\" AS \"userName\", \"User\".\"user_type\" AS \"userType\", \"User\".\"mobile_number\" AS \"mobileNumber\", \"User\".\"status\", \"User\".\"date_of_birth\" AS \"dateOfBirth\", \"User\".\"skills\", \"User\".\"total_experience\" AS \"totalExperience\", \"User\".\"avatar\", \"User\".\"proof\", \"User\".\"address\", \"User\".\"notes\", \"User\".\"fcm_token\" AS \"fcmToken\", \"User\".\"last_notification_seen\" AS \"lastNotificationSeen\", \"User\".\"plan_id\" AS \"planId\", \"User\".\"will_expire\" AS \"willExpire\", \"User\".\"message_limit\" AS \"messageLimit\", \"User\".\"is_verified\" AS \"isVerified\", \"User\".\"email_verified_at\" AS \"emailVerifiedAt\", \"User\".\"otp\", \"User\".\"otp_expires_at\" AS \"otpExpiresAt\", \"User\".\"login_attempts\" AS \"loginAttempts\", \"User\".\"locked_until\" AS \"lockedUntil\", \"User\".\"last_login_at\" AS \"lastLoginAt\", \"User\".\"last_login_ip\" AS \"lastLoginIp\", \"User\".\"company_id\" AS \"companyId\", \"User\".\"created_at\" AS \"createdAt\", \"User\".\"updated_at\" AS \"updatedAt\", \"User\".\"deleted_at\" AS \"deletedAt\", \"User\".\"department_id\" AS \"departmentId\", \"company\".\"id\" AS \"company.id\", \"company\".\"name\" AS \"company.name\", \"company\".\"status\" AS \"company.status\", \"company\".\"features\" AS \"company.features\", \"company\".\"subscription_end_date\" AS \"company.subscriptionEndDate\" FROM \"users\" AS \"User\" LEFT OUTER JOIN \"companies\" AS \"company\" ON \"User\".\"company_id\" = \"company\".\"id\" AND (\"company\".\"deleted_at\" IS NULL) WHERE (\"User\".\"deleted_at\" IS NULL AND \"User\".\"email\" = '<EMAIL>');\u001b[39m","timestamp":"2025-07-02 12:02:59:259"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): UPDATE \"users\" SET \"last_login_at\"=$1,\"updated_at\"=$2 WHERE \"id\" = $3\u001b[39m","timestamp":"2025-07-02 12:02:59:259"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"name\", \"User\".\"email\", \"User\".\"user_name\" AS \"userName\", \"User\".\"user_type\" AS \"userType\", \"User\".\"mobile_number\" AS \"mobileNumber\", \"User\".\"status\", \"User\".\"date_of_birth\" AS \"dateOfBirth\", \"User\".\"skills\", \"User\".\"total_experience\" AS \"totalExperience\", \"User\".\"avatar\", \"User\".\"proof\", \"User\".\"address\", \"User\".\"notes\", \"User\".\"fcm_token\" AS \"fcmToken\", \"User\".\"last_notification_seen\" AS \"lastNotificationSeen\", \"User\".\"plan_id\" AS \"planId\", \"User\".\"will_expire\" AS \"willExpire\", \"User\".\"message_limit\" AS \"messageLimit\", \"User\".\"is_verified\" AS \"isVerified\", \"User\".\"email_verified_at\" AS \"emailVerifiedAt\", \"User\".\"otp\", \"User\".\"otp_expires_at\" AS \"otpExpiresAt\", \"User\".\"login_attempts\" AS \"loginAttempts\", \"User\".\"locked_until\" AS \"lockedUntil\", \"User\".\"last_login_at\" AS \"lastLoginAt\", \"User\".\"last_login_ip\" AS \"lastLoginIp\", \"User\".\"company_id\" AS \"companyId\", \"User\".\"created_at\" AS \"createdAt\", \"User\".\"updated_at\" AS \"updatedAt\", \"User\".\"deleted_at\" AS \"deletedAt\", \"company\".\"id\" AS \"company.id\", \"company\".\"name\" AS \"company.name\", \"company\".\"status\" AS \"company.status\", \"company\".\"features\" AS \"company.features\", \"company\".\"subscription_end_date\" AS \"company.subscriptionEndDate\" FROM \"users\" AS \"User\" LEFT OUTER JOIN \"companies\" AS \"company\" ON \"User\".\"company_id\" = \"company\".\"id\" AND (\"company\".\"deleted_at\" IS NULL) WHERE (\"User\".\"deleted_at\" IS NULL AND \"User\".\"id\" = 'fce8b418-d90b-4f18-a0e0-40347e7c62d6');\u001b[39m","timestamp":"2025-07-02 12:02:59:259"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT count(*) AS \"count\" FROM \"service_categories\" AS \"ServiceCategory\" WHERE (\"ServiceCategory\".\"deleted_at\" IS NULL AND \"ServiceCategory\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae');\u001b[39m","timestamp":"2025-07-02 12:02:59:259"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT count(*) AS \"count\" FROM \"service_categories\" AS \"ServiceCategory\" WHERE (\"ServiceCategory\".\"deleted_at\" IS NULL AND (\"ServiceCategory\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae' AND \"ServiceCategory\".\"service_status\" = 1));\u001b[39m","timestamp":"2025-07-02 12:03:00:30"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"name\", \"User\".\"email\", \"User\".\"user_name\" AS \"userName\", \"User\".\"user_type\" AS \"userType\", \"User\".\"mobile_number\" AS \"mobileNumber\", \"User\".\"status\", \"User\".\"date_of_birth\" AS \"dateOfBirth\", \"User\".\"skills\", \"User\".\"total_experience\" AS \"totalExperience\", \"User\".\"avatar\", \"User\".\"proof\", \"User\".\"address\", \"User\".\"notes\", \"User\".\"fcm_token\" AS \"fcmToken\", \"User\".\"last_notification_seen\" AS \"lastNotificationSeen\", \"User\".\"plan_id\" AS \"planId\", \"User\".\"will_expire\" AS \"willExpire\", \"User\".\"message_limit\" AS \"messageLimit\", \"User\".\"is_verified\" AS \"isVerified\", \"User\".\"email_verified_at\" AS \"emailVerifiedAt\", \"User\".\"otp\", \"User\".\"otp_expires_at\" AS \"otpExpiresAt\", \"User\".\"login_attempts\" AS \"loginAttempts\", \"User\".\"locked_until\" AS \"lockedUntil\", \"User\".\"last_login_at\" AS \"lastLoginAt\", \"User\".\"last_login_ip\" AS \"lastLoginIp\", \"User\".\"company_id\" AS \"companyId\", \"User\".\"created_at\" AS \"createdAt\", \"User\".\"updated_at\" AS \"updatedAt\", \"User\".\"deleted_at\" AS \"deletedAt\", \"company\".\"id\" AS \"company.id\", \"company\".\"name\" AS \"company.name\", \"company\".\"status\" AS \"company.status\", \"company\".\"features\" AS \"company.features\", \"company\".\"subscription_end_date\" AS \"company.subscriptionEndDate\" FROM \"users\" AS \"User\" LEFT OUTER JOIN \"companies\" AS \"company\" ON \"User\".\"company_id\" = \"company\".\"id\" AND (\"company\".\"deleted_at\" IS NULL) WHERE (\"User\".\"deleted_at\" IS NULL AND \"User\".\"id\" = 'fce8b418-d90b-4f18-a0e0-40347e7c62d6');\u001b[39m","timestamp":"2025-07-02 12:03:00:30"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"name\", \"User\".\"email\", \"User\".\"user_name\" AS \"userName\", \"User\".\"user_type\" AS \"userType\", \"User\".\"mobile_number\" AS \"mobileNumber\", \"User\".\"status\", \"User\".\"date_of_birth\" AS \"dateOfBirth\", \"User\".\"skills\", \"User\".\"total_experience\" AS \"totalExperience\", \"User\".\"avatar\", \"User\".\"proof\", \"User\".\"address\", \"User\".\"notes\", \"User\".\"fcm_token\" AS \"fcmToken\", \"User\".\"last_notification_seen\" AS \"lastNotificationSeen\", \"User\".\"plan_id\" AS \"planId\", \"User\".\"will_expire\" AS \"willExpire\", \"User\".\"message_limit\" AS \"messageLimit\", \"User\".\"is_verified\" AS \"isVerified\", \"User\".\"email_verified_at\" AS \"emailVerifiedAt\", \"User\".\"otp\", \"User\".\"otp_expires_at\" AS \"otpExpiresAt\", \"User\".\"login_attempts\" AS \"loginAttempts\", \"User\".\"locked_until\" AS \"lockedUntil\", \"User\".\"last_login_at\" AS \"lastLoginAt\", \"User\".\"last_login_ip\" AS \"lastLoginIp\", \"User\".\"company_id\" AS \"companyId\", \"User\".\"created_at\" AS \"createdAt\", \"User\".\"updated_at\" AS \"updatedAt\", \"User\".\"deleted_at\" AS \"deletedAt\", \"company\".\"id\" AS \"company.id\", \"company\".\"name\" AS \"company.name\", \"company\".\"status\" AS \"company.status\", \"company\".\"features\" AS \"company.features\", \"company\".\"subscription_end_date\" AS \"company.subscriptionEndDate\" FROM \"users\" AS \"User\" LEFT OUTER JOIN \"companies\" AS \"company\" ON \"User\".\"company_id\" = \"company\".\"id\" AND (\"company\".\"deleted_at\" IS NULL) WHERE (\"User\".\"deleted_at\" IS NULL AND \"User\".\"id\" = 'fce8b418-d90b-4f18-a0e0-40347e7c62d6');\u001b[39m","timestamp":"2025-07-02 12:03:00:30"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT count(*) AS \"count\" FROM \"customer_categories\" AS \"CustomerCategory\";\u001b[39m","timestamp":"2025-07-02 12:03:00:30"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT count(*) AS \"count\" FROM \"customer_categories\" AS \"CustomerCategory\" WHERE \"CustomerCategory\".\"is_active\" = true;\u001b[39m","timestamp":"2025-07-02 12:03:01:31"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"name\", \"User\".\"email\", \"User\".\"user_name\" AS \"userName\", \"User\".\"user_type\" AS \"userType\", \"User\".\"mobile_number\" AS \"mobileNumber\", \"User\".\"status\", \"User\".\"date_of_birth\" AS \"dateOfBirth\", \"User\".\"skills\", \"User\".\"total_experience\" AS \"totalExperience\", \"User\".\"avatar\", \"User\".\"proof\", \"User\".\"address\", \"User\".\"notes\", \"User\".\"fcm_token\" AS \"fcmToken\", \"User\".\"last_notification_seen\" AS \"lastNotificationSeen\", \"User\".\"plan_id\" AS \"planId\", \"User\".\"will_expire\" AS \"willExpire\", \"User\".\"message_limit\" AS \"messageLimit\", \"User\".\"is_verified\" AS \"isVerified\", \"User\".\"email_verified_at\" AS \"emailVerifiedAt\", \"User\".\"otp\", \"User\".\"otp_expires_at\" AS \"otpExpiresAt\", \"User\".\"login_attempts\" AS \"loginAttempts\", \"User\".\"locked_until\" AS \"lockedUntil\", \"User\".\"last_login_at\" AS \"lastLoginAt\", \"User\".\"last_login_ip\" AS \"lastLoginIp\", \"User\".\"company_id\" AS \"companyId\", \"User\".\"created_at\" AS \"createdAt\", \"User\".\"updated_at\" AS \"updatedAt\", \"User\".\"deleted_at\" AS \"deletedAt\", \"company\".\"id\" AS \"company.id\", \"company\".\"name\" AS \"company.name\", \"company\".\"status\" AS \"company.status\", \"company\".\"features\" AS \"company.features\", \"company\".\"subscription_end_date\" AS \"company.subscriptionEndDate\" FROM \"users\" AS \"User\" LEFT OUTER JOIN \"companies\" AS \"company\" ON \"User\".\"company_id\" = \"company\".\"id\" AND (\"company\".\"deleted_at\" IS NULL) WHERE (\"User\".\"deleted_at\" IS NULL AND \"User\".\"id\" = 'fce8b418-d90b-4f18-a0e0-40347e7c62d6');\u001b[39m","timestamp":"2025-07-02 12:03:01:31"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"ServiceCategory\".\"id\", count(\"ServiceCategory\".\"id\") AS \"count\" FROM \"service_categories\" AS \"ServiceCategory\" LEFT OUTER JOIN \"services\" AS \"services\" ON \"ServiceCategory\".\"id\" = \"services\".\"service_category_id\" AND (\"services\".\"deleted_at\" IS NULL) WHERE (\"ServiceCategory\".\"deleted_at\" IS NULL AND \"ServiceCategory\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') GROUP BY \"ServiceCategory\".\"id\";\u001b[39m","timestamp":"2025-07-02 12:03:01:31"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"ServiceCategory\".\"id\", \"ServiceCategory\".\"service_category\" AS \"serviceCategory\", \"ServiceCategory\".\"service_status\" AS \"serviceStatus\", \"ServiceCategory\".\"style_view\" AS \"styleView\", \"ServiceCategory\".\"form\", \"ServiceCategory\".\"description\", \"ServiceCategory\".\"icon\", \"ServiceCategory\".\"color\", \"ServiceCategory\".\"sort_order\" AS \"sortOrder\", \"ServiceCategory\".\"is_default\" AS \"isDefault\", \"ServiceCategory\".\"created_at\" AS \"createdAt\", \"ServiceCategory\".\"updated_at\" AS \"updatedAt\", COUNT(\"services\".\"id\") AS \"servicesCount\" FROM \"service_categories\" AS \"ServiceCategory\" LEFT OUTER JOIN \"services\" AS \"services\" ON \"ServiceCategory\".\"id\" = \"services\".\"service_category_id\" AND (\"services\".\"deleted_at\" IS NULL) WHERE (\"ServiceCategory\".\"deleted_at\" IS NULL AND \"ServiceCategory\".\"company_id\" = '8722167d-d2ae-498f-95a3-c18afcf427ae') GROUP BY \"ServiceCategory\".\"id\" ORDER BY \"sortOrder\" ASC, \"serviceCategory\" ASC LIMIT 5 OFFSET 0;\u001b[39m","timestamp":"2025-07-02 12:03:01:31"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"name\", \"User\".\"email\", \"User\".\"user_name\" AS \"userName\", \"User\".\"user_type\" AS \"userType\", \"User\".\"mobile_number\" AS \"mobileNumber\", \"User\".\"status\", \"User\".\"date_of_birth\" AS \"dateOfBirth\", \"User\".\"skills\", \"User\".\"total_experience\" AS \"totalExperience\", \"User\".\"avatar\", \"User\".\"proof\", \"User\".\"address\", \"User\".\"notes\", \"User\".\"fcm_token\" AS \"fcmToken\", \"User\".\"last_notification_seen\" AS \"lastNotificationSeen\", \"User\".\"plan_id\" AS \"planId\", \"User\".\"will_expire\" AS \"willExpire\", \"User\".\"message_limit\" AS \"messageLimit\", \"User\".\"is_verified\" AS \"isVerified\", \"User\".\"email_verified_at\" AS \"emailVerifiedAt\", \"User\".\"otp\", \"User\".\"otp_expires_at\" AS \"otpExpiresAt\", \"User\".\"login_attempts\" AS \"loginAttempts\", \"User\".\"locked_until\" AS \"lockedUntil\", \"User\".\"last_login_at\" AS \"lastLoginAt\", \"User\".\"last_login_ip\" AS \"lastLoginIp\", \"User\".\"company_id\" AS \"companyId\", \"User\".\"created_at\" AS \"createdAt\", \"User\".\"updated_at\" AS \"updatedAt\", \"User\".\"deleted_at\" AS \"deletedAt\", \"company\".\"id\" AS \"company.id\", \"company\".\"name\" AS \"company.name\", \"company\".\"status\" AS \"company.status\", \"company\".\"features\" AS \"company.features\", \"company\".\"subscription_end_date\" AS \"company.subscriptionEndDate\" FROM \"users\" AS \"User\" LEFT OUTER JOIN \"companies\" AS \"company\" ON \"User\".\"company_id\" = \"company\".\"id\" AND (\"company\".\"deleted_at\" IS NULL) WHERE (\"User\".\"deleted_at\" IS NULL AND \"User\".\"id\" = 'fce8b418-d90b-4f18-a0e0-40347e7c62d6');\u001b[39m","timestamp":"2025-07-02 12:03:01:31"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mExecuting (default): SELECT \"User\".\"id\", \"User\".\"name\", \"User\".\"email\", \"User\".\"user_name\" AS \"userName\", \"User\".\"user_type\" AS \"userType\", \"User\".\"mobile_number\" AS \"mobileNumber\", \"User\".\"status\", \"User\".\"date_of_birth\" AS \"dateOfBirth\", \"User\".\"skills\", \"User\".\"total_experience\" AS \"totalExperience\", \"User\".\"avatar\", \"User\".\"proof\", \"User\".\"address\", \"User\".\"notes\", \"User\".\"fcm_token\" AS \"fcmToken\", \"User\".\"last_notification_seen\" AS \"lastNotificationSeen\", \"User\".\"plan_id\" AS \"planId\", \"User\".\"will_expire\" AS \"willExpire\", \"User\".\"message_limit\" AS \"messageLimit\", \"User\".\"is_verified\" AS \"isVerified\", \"User\".\"email_verified_at\" AS \"emailVerifiedAt\", \"User\".\"otp\", \"User\".\"otp_expires_at\" AS \"otpExpiresAt\", \"User\".\"login_attempts\" AS \"loginAttempts\", \"User\".\"locked_until\" AS \"lockedUntil\", \"User\".\"last_login_at\" AS \"lastLoginAt\", \"User\".\"last_login_ip\" AS \"lastLoginIp\", \"User\".\"company_id\" AS \"companyId\", \"User\".\"created_at\" AS \"createdAt\", \"User\".\"updated_at\" AS \"updatedAt\", \"User\".\"deleted_at\" AS \"deletedAt\", \"company\".\"id\" AS \"company.id\", \"company\".\"name\" AS \"company.name\", \"company\".\"status\" AS \"company.status\", \"company\".\"features\" AS \"company.features\", \"company\".\"subscription_end_date\" AS \"company.subscriptionEndDate\" FROM \"users\" AS \"User\" LEFT OUTER JOIN \"companies\" AS \"company\" ON \"User\".\"company_id\" = \"company\".\"id\" AND (\"company\".\"deleted_at\" IS NULL) WHERE (\"User\".\"deleted_at\" IS NULL AND \"User\".\"id\" = 'fce8b418-d90b-4f18-a0e0-40347e7c62d6');\u001b[39m","timestamp":"2025-07-02 12:03:01:31"}
