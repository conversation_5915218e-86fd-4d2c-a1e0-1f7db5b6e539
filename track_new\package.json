{"name": "tracknew", "version": "1.0.0", "description": "TrackNew Service Management Application - Single Port Architecture", "main": "server.js", "type": "module", "scripts": {"dev": "cross-env NODE_ENV=development nodemon server-dev-hot-reload.js", "dev:old": "cross-env PORT=8080 nodemon server-working.js", "dev:static": "node server-static.js", "dev:open": "cross-env PORT=8080 AUTO_OPEN_BROWSER=true nodemon server-dev-single.js", "dev:clean": "node scripts/start-dev-clean.js", "dev:backend": "cd backend && node src/server.js", "dev:separate": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\"", "dev:frontend": "cd frontend && npm run dev", "dev:clear-cache": "npm run clear-cache && npm run dev", "clear-cache": "node -e \"console.log('🧹 Clearing all caches...'); if(require('fs').existsSync('./frontend/dist')) require('fs').rmSync('./frontend/dist', {recursive: true}); if(require('fs').existsSync('./frontend/node_modules/.vite')) require('fs').rmSync('./frontend/node_modules/.vite', {recursive: true}); console.log('✅ Cache cleared successfully');\"", "start": "npm run build:frontend && npm run start:server", "start:smart": "node start-server.js", "start:auth": "node startup-auth.js", "cleanup": "node cleanup-ports.js", "build": "npm run install:all && npm run build:frontend", "build:frontend": "cd frontend && npm run build", "start:server": "cd backend && node src/server.js", "install:all": "npm run install:frontend && npm run install:backend", "install:frontend": "cd frontend && npm install", "install:backend": "cd backend && npm install", "clean": "rimraf frontend/dist backend/node_modules frontend/node_modules node_modules", "test": "echo \"No tests specified\" && exit 0", "lint": "echo \"No linting configured\" && exit 0"}, "keywords": ["service-management", "react", "nodejs", "express", "single-port"], "author": "TrackNew Team", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/tracknew.git"}, "devDependencies": {"concurrently": "^8.2.2", "cross-env": "^7.0.3", "nodemon": "^3.1.10", "puppeteer": "^24.11.1", "rimraf": "^5.0.5"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "axios": "^1.10.0", "chalk": "^5.4.1", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "http-proxy-middleware": "^3.0.5", "jsonwebtoken": "^9.0.2", "multer": "^2.0.1", "node-fetch": "^3.3.2", "pg": "^8.16.0", "sequelize": "^6.37.7", "sqlite3": "^5.1.7", "uuid": "^11.1.0", "xlsx": "^0.18.5"}}