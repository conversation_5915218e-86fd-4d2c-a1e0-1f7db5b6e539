import pkg from 'pg';
const { Pool } = pkg;

// Database connection
const pool = new Pool({
  connectionString: process.env.DATABASE_URL || '****************************************************/tracknew?sslmode=disable'
});

/**
 * Customer Category Controller
 * 
 * Handles CRUD operations for customer categories
 */

// Get all customer categories with pagination and search
const getCustomerCategories = async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 10, 
      search = '',
      sortBy = 'sortOrder',
      sortOrder = 'ASC'
    } = req.query;

    const offset = (page - 1) * limit;
    
    // Build search condition
    let searchCondition = '';
    let queryParams = [limit, offset];
    let paramIndex = 3;

    if (search) {
      searchCondition = `WHERE category_name ILIKE $${paramIndex} OR description ILIKE $${paramIndex}`;
      queryParams.push(`%${search}%`);
      paramIndex++;
    }

    // Build sort condition
    const validSortFields = ['category_name', 'sort_order', 'created_at', 'is_active'];
    const sortField = validSortFields.includes(sortBy) ? sortBy : 'sort_order';
    const sortDirection = sortOrder.toUpperCase() === 'DESC' ? 'DESC' : 'ASC';

    // Get total count
    const countQuery = `
      SELECT COUNT(*) as total 
      FROM customer_categories 
      ${searchCondition}
    `;
    
    const countParams = search ? [search.includes('%') ? search : `%${search}%`] : [];
    const countResult = await pool.query(countQuery, countParams);
    const totalItems = parseInt(countResult.rows[0].total);

    // Get categories
    const query = `
      SELECT 
        id,
        category_name,
        description,
        color,
        is_active,
        sort_order,
        discount_percentage,
        special_terms,
        created_at,
        updated_at
      FROM customer_categories 
      ${searchCondition}
      ORDER BY ${sortField} ${sortDirection}
      LIMIT $1 OFFSET $2
    `;

    const result = await pool.query(query, queryParams);

    // Calculate pagination
    const totalPages = Math.ceil(totalItems / limit);

    res.json({
      success: true,
      data: {
        categories: result.rows.map(row => ({
          id: row.id,
          categoryName: row.category_name,
          description: row.description,
          color: row.color,
          isActive: row.is_active,
          sortOrder: row.sort_order,
          discountPercentage: row.discount_percentage,
          specialTerms: row.special_terms,
          createdAt: row.created_at,
          updatedAt: row.updated_at
        })),
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalItems,
          itemsPerPage: parseInt(limit),
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1
        }
      }
    });
  } catch (error) {
    console.error('Error fetching customer categories:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch customer categories',
      error: error.message
    });
  }
};

// Get single customer category by ID
const getCustomerCategoryById = async (req, res) => {
  try {
    const { id } = req.params;

    const query = `
      SELECT 
        id,
        category_name,
        description,
        color,
        is_active,
        sort_order,
        discount_percentage,
        special_terms,
        created_at,
        updated_at
      FROM customer_categories 
      WHERE id = $1
    `;

    const result = await pool.query(query, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Customer category not found'
      });
    }

    const row = result.rows[0];
    res.json({
      success: true,
      data: {
        id: row.id,
        categoryName: row.category_name,
        description: row.description,
        color: row.color,
        isActive: row.is_active,
        sortOrder: row.sort_order,
        discountPercentage: row.discount_percentage,
        specialTerms: row.special_terms,
        createdAt: row.created_at,
        updatedAt: row.updated_at
      }
    });
  } catch (error) {
    console.error('Error fetching customer category:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch customer category',
      error: error.message
    });
  }
};

// Create new customer category
const createCustomerCategory = async (req, res) => {
  try {
    const {
      categoryName,
      description = '',
      color = '#10B981',
      isActive = true,
      sortOrder = 0,
      discountPercentage = 0,
      specialTerms = ''
    } = req.body;

    // Validation
    if (!categoryName || categoryName.trim() === '') {
      return res.status(400).json({
        success: false,
        message: 'Category name is required'
      });
    }

    if (discountPercentage < 0 || discountPercentage > 100) {
      return res.status(400).json({
        success: false,
        message: 'Discount percentage must be between 0 and 100'
      });
    }

    // Check if category name already exists
    const existingQuery = 'SELECT id FROM customer_categories WHERE category_name = $1';
    const existingResult = await pool.query(existingQuery, [categoryName.trim()]);

    if (existingResult.rows.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Customer category with this name already exists'
      });
    }

    const query = `
      INSERT INTO customer_categories (
        category_name, 
        description, 
        color, 
        is_active, 
        sort_order, 
        discount_percentage, 
        special_terms,
        created_at,
        updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW())
      RETURNING id, category_name, description, color, is_active, sort_order, discount_percentage, special_terms, created_at, updated_at
    `;

    const values = [
      categoryName.trim(),
      description.trim(),
      color,
      isActive,
      parseInt(sortOrder) || 0,
      parseFloat(discountPercentage) || 0,
      specialTerms.trim()
    ];

    const result = await pool.query(query, values);
    const row = result.rows[0];

    res.status(201).json({
      success: true,
      message: 'Customer category created successfully',
      data: {
        id: row.id,
        categoryName: row.category_name,
        description: row.description,
        color: row.color,
        isActive: row.is_active,
        sortOrder: row.sort_order,
        discountPercentage: row.discount_percentage,
        specialTerms: row.special_terms,
        createdAt: row.created_at,
        updatedAt: row.updated_at
      }
    });
  } catch (error) {
    console.error('Error creating customer category:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create customer category',
      error: error.message
    });
  }
};

// Update customer category
const updateCustomerCategory = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      categoryName,
      description,
      color,
      isActive,
      sortOrder,
      discountPercentage,
      specialTerms
    } = req.body;

    // Validation
    if (!categoryName || categoryName.trim() === '') {
      return res.status(400).json({
        success: false,
        message: 'Category name is required'
      });
    }

    if (discountPercentage !== undefined && (discountPercentage < 0 || discountPercentage > 100)) {
      return res.status(400).json({
        success: false,
        message: 'Discount percentage must be between 0 and 100'
      });
    }

    // Check if category exists
    const existingQuery = 'SELECT id FROM customer_categories WHERE id = $1';
    const existingResult = await pool.query(existingQuery, [id]);

    if (existingResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Customer category not found'
      });
    }

    // Check if category name already exists (excluding current category)
    const duplicateQuery = 'SELECT id FROM customer_categories WHERE category_name = $1 AND id != $2';
    const duplicateResult = await pool.query(duplicateQuery, [categoryName.trim(), id]);

    if (duplicateResult.rows.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Customer category with this name already exists'
      });
    }

    const query = `
      UPDATE customer_categories 
      SET 
        category_name = $1,
        description = $2,
        color = $3,
        is_active = $4,
        sort_order = $5,
        discount_percentage = $6,
        special_terms = $7,
        updated_at = NOW()
      WHERE id = $8
      RETURNING id, category_name, description, color, is_active, sort_order, discount_percentage, special_terms, created_at, updated_at
    `;

    const values = [
      categoryName.trim(),
      description?.trim() || '',
      color || '#10B981',
      isActive !== undefined ? isActive : true,
      parseInt(sortOrder) || 0,
      parseFloat(discountPercentage) || 0,
      specialTerms?.trim() || '',
      id
    ];

    const result = await pool.query(query, values);
    const row = result.rows[0];

    res.json({
      success: true,
      message: 'Customer category updated successfully',
      data: {
        id: row.id,
        categoryName: row.category_name,
        description: row.description,
        color: row.color,
        isActive: row.is_active,
        sortOrder: row.sort_order,
        discountPercentage: row.discount_percentage,
        specialTerms: row.special_terms,
        createdAt: row.created_at,
        updatedAt: row.updated_at
      }
    });
  } catch (error) {
    console.error('Error updating customer category:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update customer category',
      error: error.message
    });
  }
};

// Delete customer category
const deleteCustomerCategory = async (req, res) => {
  try {
    const { id } = req.params;

    // Check if category exists
    const existingQuery = 'SELECT id FROM customer_categories WHERE id = $1';
    const existingResult = await pool.query(existingQuery, [id]);

    if (existingResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Customer category not found'
      });
    }

    // Check if category is being used by customers
    const usageQuery = 'SELECT COUNT(*) as count FROM customers WHERE customer_category_id = $1';
    const usageResult = await pool.query(usageQuery, [id]);
    const usageCount = parseInt(usageResult.rows[0].count);

    if (usageCount > 0) {
      return res.status(400).json({
        success: false,
        message: `Cannot delete customer category. It is being used by ${usageCount} customer(s).`
      });
    }

    const deleteQuery = 'DELETE FROM customer_categories WHERE id = $1';
    await pool.query(deleteQuery, [id]);

    res.json({
      success: true,
      message: 'Customer category deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting customer category:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete customer category',
      error: error.message
    });
  }
};

// Check category name availability
const checkCategoryNameAvailability = async (req, res) => {
  try {
    const { name, excludeId } = req.query;

    if (!name || name.trim() === '') {
      return res.json({
        available: false,
        message: 'Category name is required'
      });
    }

    let query = `
      SELECT id FROM customer_categories
      WHERE category_name = $1
    `;
    let queryParams = [name.trim()];

    // Exclude current category when editing
    if (excludeId) {
      query += ` AND id != $2`;
      queryParams.push(excludeId);
    }

    const result = await pool.query(query, queryParams);

    res.json({
      available: result.rows.length === 0,
      message: result.rows.length > 0 ? 'Category name already exists' : 'Category name is available'
    });
  } catch (error) {
    console.error('Error checking category name availability:', error);
    res.status(500).json({
      available: false,
      message: 'Failed to check category name availability'
    });
  }
};

export {
  getCustomerCategories,
  getCustomerCategoryById,
  createCustomerCategory,
  updateCustomerCategory,
  deleteCustomerCategory,
  checkCategoryNameAvailability
};
