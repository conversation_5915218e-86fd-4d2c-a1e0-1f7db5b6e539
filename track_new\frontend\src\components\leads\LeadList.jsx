import React, { useState } from 'react';
import { useDispatch } from 'react-redux';
import {
  PencilIcon,
  TrashIcon,
  EyeIcon,
  PlusIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  ArrowPathIcon,
  Squares2X2Icon,
  Bars3Icon,
  PhoneIcon,
  EnvelopeIcon,
  MapPinIcon,
  BuildingOfficeIcon,
  CheckIcon,
  ChevronUpIcon,
  ChevronDownIcon,
  EllipsisVerticalIcon,
  UserIcon,
  CalendarIcon,
  FlagIcon,
  GlobeAltIcon,
  WrenchScrewdriverIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { deleteLead } from '../../store/slices/leadSlice';
import { showToast } from '../../utils/toast';
import '../customers/Customer.css'; // Import Customer CSS for exact styling match
import ConfirmDialog from '../common/ConfirmDialog';
import './Lead.css';
import '../customers/Customer.css';



const LeadList = ({
  leads,
  isLoading,
  viewMode,
  pagination,
  onPageChange,
  onItemsPerPageChange,
  onEditLead,
  onRefresh
}) => {
  const dispatch = useDispatch();
  const [deleteConfirm, setDeleteConfirm] = useState({ show: false, leadId: null });
  const [convertingLead, setConvertingLead] = useState(null);

  const getStatusColor = (status) => {
    // Exact Customer module status color matching
    const colors = {
      open: 'bg-orange-100 text-orange-700 border border-orange-200',
      in_progress: 'bg-blue-100 text-blue-700 border border-blue-200',
      qualified: 'bg-purple-100 text-purple-700 border border-purple-200',
      converted: 'bg-teal-100 text-teal-700 border border-teal-200',
      closed: 'bg-gray-100 text-gray-700 border border-gray-200',
      lost: 'bg-red-100 text-red-700 border border-red-200'
    };
    return colors[status] || 'bg-orange-100 text-orange-700 border border-orange-200';
  };

  // Handle service conversion
  const handleConvertToService = async (lead) => {
    try {
      setConvertingLead(lead.id);

      // Create service data from lead information
      const serviceData = {
        title: lead.title || `Service Request from Lead: ${lead.title}`,
        description: lead.description || 'Service request converted from lead',
        customer_id: lead.customerId || lead.customer_id,
        priority: lead.priority || 'medium',
        status: 'pending',
        serviceData: {
          deviceType: '',
          deviceModel: '',
          serialNumber: '',
          issueDescription: lead.description || 'Converted from lead',
          customerRequirements: lead.notes || '',
          serviceType: 'General Service',
          problemTitle: lead.title || 'Service Request'
        },
        notes: `Converted from Lead ID: ${lead.id}. Original lead notes: ${lead.notes || 'None'}`,
        leadId: lead.id
      };

      // Call the conversion API
      const response = await fetch(`/api/leads/${lead.id}/convert-to-service`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(serviceData)
      });

      const result = await response.json();

      if (result.status === 'success') {
        showToast.success('Lead successfully converted to service');

        // Redirect to services module with the new service ID
        if (result.data?.serviceId) {
          window.location.href = `/services?highlight=${result.data.serviceId}`;
        } else {
          window.location.href = '/services';
        }
      } else {
        throw new Error(result.message || 'Failed to convert lead to service');
      }
    } catch (error) {
      console.error('Service conversion error:', error);
      showToast.error(error.message || 'Failed to convert lead to service');
    } finally {
      setConvertingLead(null);
    }
  };

  // Ultra Compact Lead Card Component - Exact Customer Module Design Match
  const LeadCard = ({ lead, onEdit, onView, onDelete, formatDate, index }) => {
    const displayName = lead.title || `Lead ${lead.id}`;
    const customerName = lead.customer?.name || lead.customer_name || 'Unknown Customer';

    return (
      <div className="bg-white rounded-md shadow-sm border border-gray-200 hover:shadow-lg hover:border-teal-200 transition-all duration-200 overflow-hidden">
        {/* Elegant Teal & Copper Header - Exact Customer Module Match */}
        <div className="p-1.5 border-b border-teal-100" style={{
          background: 'linear-gradient(135deg, #0f766e 0%, #14b8a6 50%, #2dd4bf 100%)',
          boxShadow: '0 2px 4px rgba(15, 118, 110, 0.1)'
        }}>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-1.5 flex-1 min-w-0">
              <div
                onClick={() => onView(lead)}
                className={`w-6 h-6 rounded-full flex items-center justify-center cursor-pointer text-white text-xs font-bold flex-shrink-0 shadow-sm ${
                  index % 4 === 0 ? 'bg-gradient-to-br from-orange-500 to-orange-600' :
                  index % 4 === 1 ? 'bg-gradient-to-br from-teal-500 to-teal-600' :
                  index % 4 === 2 ? 'bg-gradient-to-br from-slate-500 to-slate-600' :
                  'bg-gradient-to-br from-emerald-500 to-emerald-600'
                }`}
              >
                {displayName.charAt(0).toUpperCase()}
              </div>
              <div className="min-w-0 flex-1">
                <h4
                  className="text-xs font-semibold text-white truncate leading-tight cursor-pointer hover:text-orange-200 transition-colors"
                  onClick={() => onView(lead)}
                >
                  {displayName}
                </h4>
                <p className="text-xs text-teal-100 truncate leading-tight">
                  {lead.id?.toString().slice(-6) || 'N/A'}
                </p>
              </div>
            </div>

            {/* Elegant Status Badge - Exact Customer Module Match */}
            <span className={`px-1.5 py-0.5 text-xs font-medium rounded-full shadow-sm ${
              lead.status === 'converted' ? 'bg-teal-100 text-teal-700 border border-teal-200' :
              lead.status === 'lost' || lead.status === 'closed' ? 'bg-red-100 text-red-700 border border-red-200' :
              'bg-orange-100 text-orange-700 border border-orange-200'
            }`}>
              {lead.status?.charAt(0).toUpperCase() || 'O'}
            </span>
          </div>
        </div>

        {/* Elegant Compact Body - Exact Customer Module Match */}
        <div className="p-1.5 space-y-1">
          {/* Contact Info */}
          <div className="flex items-center space-x-1 text-xs text-gray-600">
            <PhoneIcon className="h-3 w-3 text-teal-600 flex-shrink-0" />
            <span
              className="cursor-pointer hover:text-teal-600 hover:font-medium truncate transition-all"
              onClick={() => window.open(`tel:${lead.phone || lead.customer?.phone}`, '_self')}
            >
              {lead.phone || lead.customer?.phone || 'No phone'}
            </span>
          </div>

          {/* Priority/Value Amount - Matching Customer Balance */}
          <div className="flex items-center justify-between pt-0.5 border-t border-teal-100">
            <span className="text-xs text-gray-500 font-medium">Priority:</span>
            <span className={`text-xs font-bold px-1.5 py-0.5 rounded-full ${
              lead.priority === 'high'
                ? 'text-red-700 bg-red-50 border border-red-200'
                : lead.priority === 'low'
                ? 'text-green-700 bg-green-50 border border-green-200'
                : 'text-orange-700 bg-orange-50 border border-orange-200'
            }`}>
              {lead.priority?.toUpperCase() || 'MEDIUM'}
            </span>
          </div>
        </div>

        {/* Elegant Action Footer - Exact Customer Module Match */}
        <div className="px-1.5 py-1 border-t border-teal-100" style={{
          background: 'linear-gradient(135deg, #f0fdfa 0%, #ccfbf1 100%)'
        }}>
          <div className="flex items-center justify-between">
            {/* Service Conversion Button - Matching Customer WhatsApp */}
            <div className="flex-1">
              {lead.status !== 'converted' && (
                <button
                  className="text-xs text-teal-600 hover:text-teal-700 font-medium px-1 py-0.5 rounded hover:bg-teal-50 transition-all"
                  onClick={() => handleConvertToService(lead)}
                  disabled={convertingLead === lead.id}
                >
                  🔧
                </button>
              )}
            </div>

            {/* Elegant Action Buttons - Exact Customer Module Match */}
            <div className="flex items-center space-x-0.5">
              <button
                onClick={() => onView(lead)}
                className="p-1 text-gray-500 hover:text-slate-600 hover:bg-slate-50 rounded-md transition-all duration-200 hover:shadow-sm"
                title="View"
              >
                <EyeIcon className="h-3 w-3" />
              </button>
              <button
                onClick={() => onEdit(lead)}
                className="p-1 text-gray-500 hover:text-orange-600 hover:bg-orange-50 rounded-md transition-all duration-200 hover:shadow-sm"
                title="Edit"
              >
                <PencilIcon className="h-3 w-3" />
              </button>
              <button
                onClick={() => onDelete(lead.id)}
                className="p-1 text-gray-500 hover:text-red-600 hover:bg-red-50 rounded-md transition-all duration-200 hover:shadow-sm"
                title="Delete"
              >
                <TrashIcon className="h-3 w-3" />
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'No date';
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch (error) {
      return 'Invalid date';
    }
  };

  const getPriorityColor = (priority) => {
    const colors = {
      low: 'bg-green-100 text-green-800',
      medium: 'bg-yellow-100 text-yellow-800',
      high: 'bg-orange-100 text-orange-800',
      urgent: 'bg-red-100 text-red-800'
    };
    return colors[priority] || 'bg-gray-100 text-gray-800';
  };

  const getSourceIcon = (source) => {
    const icons = {
      website: GlobeAltIcon,
      phone: PhoneIcon,
      email: EnvelopeIcon,
      referral: UserIcon,
      social: UserIcon,
      direct: UserIcon
    };
    return icons[source] || UserIcon;
  };

  const handleDeleteClick = (leadId) => {
    setDeleteConfirm({ show: true, leadId });
  };

  const handleDeleteConfirm = async () => {
    try {
      await dispatch(deleteLead(deleteConfirm.leadId)).unwrap();
      showToast.success('Lead deleted successfully');
      onRefresh();
    } catch (error) {
      showToast.error(error || 'Failed to delete lead');
    } finally {
      setDeleteConfirm({ show: false, leadId: null });
    }
  };

  const handleDeleteCancel = () => {
    setDeleteConfirm({ show: false, leadId: null });
  };

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border p-8">
        <div className="flex justify-center items-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-600">Loading leads...</span>
        </div>
      </div>
    );
  }

  if (leads.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-sm border p-8 text-center">
        <div className="text-gray-500">
          <UserIcon className="w-12 h-12 mx-auto mb-4 text-gray-400" />
          <h3 className="text-lg font-medium mb-2">No leads found</h3>
          <p>Try adjusting your filters or create a new lead.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Ultra Compact Card View - Exact Customer Module Match */}
      {viewMode === 'card' && (
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 2xl:grid-cols-8 gap-1.5 custom-scrollbar-hidden">
          {leads.map((lead, index) => (
            <LeadCard
              key={lead.id}
              lead={lead}
              index={index}
              onEdit={onEditLead}
              onView={(lead) => console.log('View lead:', lead)}
              onDelete={handleDeleteClick}
              formatDate={formatDate}
            />
          ))}
        </div>
      )}

      {/* Table View - Exact Customer Module Design */}
      {viewMode === 'table' && (
        <div className="table-container overflow-x-auto flex justify-left items-center">
          <table className="table w-full">
            <thead>
              <tr className="table-head">
                <th className="px-1 py-1 table-border">
                  <div className="flex justify-start items-center px-1 relative">
                    <p className="text-xs font-bold text-white uppercase tracking-wide">Title</p>
                  </div>
                </th>
                <th className="px-1 py-1 table-border">
                  <div className="flex justify-start items-center px-1 relative">
                    <p className="text-xs font-bold text-white uppercase tracking-wide">Customer</p>
                  </div>
                </th>
                <th className="px-1 py-1 table-border">
                  <div className="flex justify-start items-center px-1 relative">
                    <p className="text-xs font-bold text-white uppercase tracking-wide">Priority</p>
                  </div>
                </th>
                <th className="px-1 py-1 table-border">
                  <div className="flex justify-start items-center px-1 relative">
                    <p className="text-xs font-bold text-white uppercase tracking-wide">Source</p>
                  </div>
                </th>
                <th className="px-1 py-1 table-border">
                  <div className="flex justify-start items-center px-1 relative">
                    <p className="text-xs font-bold text-white uppercase tracking-wide">Follow Up</p>
                  </div>
                </th>
                <th className="px-1 py-1 table-border">
                  <div className="flex justify-start items-center px-1 relative">
                    <p className="text-xs font-bold text-white uppercase tracking-wide">Status</p>
                  </div>
                </th>
                <th className="px-2 py-1 leading-none text-center table-border">
                  <div className="flex justify-center items-center space-x-1">
                    <p className="text-xs font-bold text-white uppercase tracking-wide">Actions</p>
                  </div>
                </th>
              </tr>
            </thead>

            <tbody>
              {leads.map((lead, index) => (
                <tr key={lead.id} className="hover:bg-gray-100 cursor-pointer">
                  <td className="px-1 py-1 table-border text-sm">
                    <div className="text-sm font-medium text-gray-900 truncate">
                      {lead.title}
                    </div>
                  </td>
                  <td className="px-1 py-1 table-border text-sm">
                    <div className="text-sm text-gray-900 truncate">
                      {lead.customer?.name || lead.customer_name || 'Unknown'}
                    </div>
                  </td>
                  <td className="px-1 py-1 table-border text-sm">
                    <div className="text-sm text-gray-900 truncate">
                      {lead.priority || 'Medium'}
                    </div>
                  </td>
                  <td className="px-1 py-1 table-border text-sm">
                    <div className="text-sm text-gray-900 truncate">
                      {lead.source || 'Unknown'}
                    </div>
                  </td>
                  <td className="px-1 py-1 table-border text-sm">
                    <div className="text-sm text-gray-900 truncate">
                      {formatDate(lead.follow_up_date) || 'No follow-up'}
                    </div>
                  </td>
                  <td className="px-1 py-1 table-border text-sm">
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(lead.status)}`}>
                      {lead.status?.replace('_', ' ') || 'Open'}
                    </span>
                  </td>

                  {/* Elegant Actions - Exact Customer Module Match */}
                  <td className="text-center py-1 table-border">
                    <div className="flex justify-center space-x-0.5">
                      {/* View Button */}
                      <button
                        onClick={() => console.log('View lead:', lead)}
                        title="View Details"
                        className="p-1 text-slate-600 hover:bg-slate-50 hover:text-slate-700 rounded-md transition-all duration-200 hover:shadow-sm"
                      >
                        <EyeIcon className="h-3.5 w-3.5" />
                      </button>

                      {/* Convert to Service Button */}
                      <button
                        onClick={() => handleConvertToService(lead)}
                        title="Convert to Service"
                        disabled={convertingLead === lead.id || lead.status === 'converted'}
                        className={`p-1 rounded-md transition-all duration-200 hover:shadow-sm ${
                          convertingLead === lead.id
                            ? 'text-gray-400 cursor-not-allowed'
                            : lead.status === 'converted'
                            ? 'text-gray-400 cursor-not-allowed'
                            : 'text-teal-600 hover:bg-teal-50 hover:text-teal-700'
                        }`}
                      >
                        <WrenchScrewdriverIcon className="h-3.5 w-3.5" />
                      </button>

                      {/* Edit Button */}
                      <button
                        onClick={() => onEditLead(lead)}
                        title="Edit Lead"
                        className="p-1 text-orange-600 hover:bg-orange-50 hover:text-orange-700 rounded-md transition-all duration-200 hover:shadow-sm"
                      >
                        <PencilIcon className="h-3.5 w-3.5" />
                      </button>

                      {/* Delete Button */}
                      <button
                        onClick={() => handleDeleteClick(lead.id)}
                        title="Delete Lead"
                        className="p-1 text-red-600 hover:bg-red-50 hover:text-red-700 rounded-md transition-all duration-200 hover:shadow-sm"
                      >
                        <TrashIcon className="h-3.5 w-3.5" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {/* Pagination - Exact Customer Module Design */}
      {pagination?.totalPages > 1 && !isLoading && (
        <div className="pagination flex items-center justify-between mt-2 sm:text-md text-xs">
          <div className="mt-4">
            <p className="text-sm text-gray-700">
              Showing {((pagination.currentPage - 1) * pagination.itemsPerPage) + 1} to {Math.min(pagination.currentPage * pagination.itemsPerPage, pagination.totalItems)} of {pagination.totalItems} entries
            </p>
          </div>
          <div className="flex justify-end w-1/2">
            <ul className="flex list-none overflow-auto">
              <li className={`mr-2 rounded-bl-[20px] rounded-tl-[20px] ${
                pagination.currentPage === 1 ? 'bg-gray-400' : 'hover:shadow-md'
              }`} style={{
                background: pagination.currentPage === 1 ? '#9ca3af' : 'linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%)'
              }}>
                <button
                  onClick={() => onPageChange(pagination.currentPage - 1)}
                  disabled={pagination.currentPage === 1}
                  className="flex px-3 py-2 text-white justify-between sm:text-md text-xs items-center rounded-bl-[20px] rounded-tl-[20px] transition-all"
                >
                  <ArrowUpIcon className="h-4 w-4 rotate-[-90deg]" />
                  <span className="pl-1">Prev</span>
                </button>
              </li>
              {[...Array(Math.min(5, pagination.totalPages))].map((_, i) => {
                const pageNumber = i + 1;
                return (
                  <li key={pageNumber}>
                    <button
                      onClick={() => onPageChange(pageNumber)}
                      className={`px-3 py-2 rounded text-black hover:text-white sm:text-md text-xs transition-all duration-200 ${
                        pageNumber === pagination.currentPage ? 'text-white shadow-md' : 'hover:shadow-sm'
                      }`}
                      style={pageNumber === pagination.currentPage ? {
                        background: 'linear-gradient(135deg, #0f766e 0%, #14b8a6 100%)',
                        boxShadow: '0 4px 15px rgba(15, 118, 110, 0.3)'
                      } : {}}
                      onMouseEnter={(e) => {
                        if (pageNumber !== pagination.currentPage) {
                          e.target.style.background = 'linear-gradient(135deg, #ccfbf1 0%, #99f6e4 100%)';
                          e.target.style.color = '#0f766e';
                        }
                      }}
                      onMouseLeave={(e) => {
                        if (pageNumber !== pagination.currentPage) {
                          e.target.style.background = '';
                          e.target.style.color = 'black';
                        }
                      }}
                    >
                      {pageNumber}
                    </button>
                  </li>
                );
              })}
              <li className={`ml-2 rounded-br-[20px] rounded-tr-[20px] ${
                pagination.currentPage === pagination.totalPages ? 'bg-gray-400' : 'hover:shadow-md'
              }`} style={{
                background: pagination.currentPage === pagination.totalPages ? '#9ca3af' : 'linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%)'
              }}>
                <button
                  onClick={() => onPageChange(pagination.currentPage + 1)}
                  disabled={pagination.currentPage === pagination.totalPages}
                  className="flex px-3 py-2 text-white rounded-br-[20px] rounded-tr-[20px] sm:text-md text-xs items-center"
                >
                  <span className="pr-1">Next</span>
                  <ArrowUpIcon className="h-4 w-4 rotate-[90deg]" />
                </button>
              </li>
            </ul>
          </div>
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        isOpen={deleteConfirm.show}
        title="Delete Lead"
        message="Are you sure you want to delete this lead? This action cannot be undone."
        confirmText="Delete"
        cancelText="Cancel"
        onConfirm={handleDeleteConfirm}
        onCancel={handleDeleteCancel}
        type="danger"
      />
    </div>
  );
};

export default LeadList;
