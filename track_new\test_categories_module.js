/**
 * Categories Module Comprehensive Test Script
 * Tests all CRUD operations, API endpoints, and UI functionality
 */

import axios from 'axios';

const BASE_URL = 'http://localhost:8080/api';
const TEST_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************.test'; // Test token

const headers = {
  'Authorization': `Bearer ${TEST_TOKEN}`,
  'Content-Type': 'application/json'
};

// Test results tracking
const testResults = {
  passed: 0,
  failed: 0,
  tests: []
};

function logTest(testName, passed, message = '') {
  const status = passed ? '✅ PASS' : '❌ FAIL';
  console.log(`${status}: ${testName}${message ? ' - ' + message : ''}`);
  
  testResults.tests.push({ testName, passed, message });
  if (passed) {
    testResults.passed++;
  } else {
    testResults.failed++;
  }
}

async function testServiceCategoriesAPI() {
  console.log('\n🔧 Testing Service Categories API...\n');
  
  try {
    // Test 1: GET Service Categories
    const getResponse = await axios.get(`${BASE_URL}/service-categories`, { headers });
    logTest('GET Service Categories', getResponse.status === 200, `Status: ${getResponse.status}`);
    
    // Test 2: Verify response structure
    const hasCorrectStructure = getResponse.data.success && 
                               Array.isArray(getResponse.data.data.categories) &&
                               getResponse.data.data.pagination;
    logTest('Service Categories Response Structure', hasCorrectStructure);
    
    // Test 3: Create Service Category
    const createData = {
      serviceCategory: 'Test Category API',
      description: 'Test category created via API',
      serviceStatus: 1,
      sortOrder: 1,
      form: {
        fields: [
          { name: 'testField', type: 'text', required: true }
        ]
      }
    };
    
    const createResponse = await axios.post(`${BASE_URL}/service-categories`, createData, { headers });
    logTest('CREATE Service Category', createResponse.status === 201, `Status: ${createResponse.status}`);
    
    const createdCategoryId = createResponse.data.data.id;
    
    // Test 4: GET Single Service Category
    const getSingleResponse = await axios.get(`${BASE_URL}/service-categories/${createdCategoryId}`, { headers });
    logTest('GET Single Service Category', getSingleResponse.status === 200, `Status: ${getSingleResponse.status}`);
    
    // Test 5: UPDATE Service Category
    const updateData = {
      serviceCategory: 'Updated Test Category API',
      description: 'Updated test category description',
      serviceStatus: 1
    };
    
    const updateResponse = await axios.put(`${BASE_URL}/service-categories/${createdCategoryId}`, updateData, { headers });
    logTest('UPDATE Service Category', updateResponse.status === 200, `Status: ${updateResponse.status}`);
    
    // Test 6: DELETE Service Category
    const deleteResponse = await axios.delete(`${BASE_URL}/service-categories/${createdCategoryId}`, { headers });
    logTest('DELETE Service Category', deleteResponse.status === 200, `Status: ${deleteResponse.status}`);
    
  } catch (error) {
    logTest('Service Categories API Error', false, error.message);
  }
}

async function testCustomerCategoriesAPI() {
  console.log('\n👥 Testing Customer Categories API...\n');
  
  try {
    // Test 1: GET Customer Categories
    const getResponse = await axios.get(`${BASE_URL}/customer-categories`, { headers });
    logTest('GET Customer Categories', getResponse.status === 200, `Status: ${getResponse.status}`);
    
    // Test 2: Verify response structure
    const hasCorrectStructure = getResponse.data.success && 
                               Array.isArray(getResponse.data.data.categories) &&
                               getResponse.data.data.pagination;
    logTest('Customer Categories Response Structure', hasCorrectStructure);
    
    // Test 3: Create Customer Category
    const createData = {
      categoryName: 'Test Customer Category API',
      description: 'Test customer category created via API',
      discountPercentage: 10.5,
      isActive: true,
      sortOrder: 1
    };
    
    const createResponse = await axios.post(`${BASE_URL}/customer-categories`, createData, { headers });
    logTest('CREATE Customer Category', createResponse.status === 201, `Status: ${createResponse.status}`);
    
    const createdCategoryId = createResponse.data.data.id;
    
    // Test 4: GET Single Customer Category
    const getSingleResponse = await axios.get(`${BASE_URL}/customer-categories/${createdCategoryId}`, { headers });
    logTest('GET Single Customer Category', getSingleResponse.status === 200, `Status: ${getSingleResponse.status}`);
    
    // Test 5: UPDATE Customer Category
    const updateData = {
      categoryName: 'Updated Test Customer Category API',
      description: 'Updated test customer category description',
      discountPercentage: 15.0,
      isActive: true
    };
    
    const updateResponse = await axios.put(`${BASE_URL}/customer-categories/${createdCategoryId}`, updateData, { headers });
    logTest('UPDATE Customer Category', updateResponse.status === 200, `Status: ${updateResponse.status}`);
    
    // Test 6: DELETE Customer Category
    const deleteResponse = await axios.delete(`${BASE_URL}/customer-categories/${createdCategoryId}`, { headers });
    logTest('DELETE Customer Category', deleteResponse.status === 200, `Status: ${deleteResponse.status}`);
    
  } catch (error) {
    logTest('Customer Categories API Error', false, error.message);
  }
}

async function testSearchAndFiltering() {
  console.log('\n🔍 Testing Search and Filtering...\n');
  
  try {
    // Test search functionality
    const searchResponse = await axios.get(`${BASE_URL}/service-categories?search=test`, { headers });
    logTest('Service Categories Search', searchResponse.status === 200, `Status: ${searchResponse.status}`);
    
    // Test status filtering
    const filterResponse = await axios.get(`${BASE_URL}/service-categories?status=1`, { headers });
    logTest('Service Categories Status Filter', filterResponse.status === 200, `Status: ${filterResponse.status}`);
    
    // Test pagination
    const paginationResponse = await axios.get(`${BASE_URL}/service-categories?page=1&limit=5`, { headers });
    logTest('Service Categories Pagination', paginationResponse.status === 200, `Status: ${paginationResponse.status}`);
    
  } catch (error) {
    logTest('Search and Filtering Error', false, error.message);
  }
}

async function testDatabaseConnectivity() {
  console.log('\n💾 Testing Database Connectivity...\n');
  
  try {
    // Test health endpoint
    const healthResponse = await axios.get('http://localhost:8080/health');
    logTest('Server Health Check', healthResponse.status === 200, `Status: ${healthResponse.status}`);
    
    // Test database connection through API
    const dbTestResponse = await axios.get(`${BASE_URL}/service-categories?limit=1`, { headers });
    logTest('Database Connectivity', dbTestResponse.status === 200, `Status: ${dbTestResponse.status}`);
    
  } catch (error) {
    logTest('Database Connectivity Error', false, error.message);
  }
}

async function runAllTests() {
  console.log('🚀 Starting Categories Module Comprehensive Tests...\n');
  console.log('=' .repeat(60));
  
  await testDatabaseConnectivity();
  await testServiceCategoriesAPI();
  await testCustomerCategoriesAPI();
  await testSearchAndFiltering();
  
  console.log('\n' + '=' .repeat(60));
  console.log('📊 TEST RESULTS SUMMARY');
  console.log('=' .repeat(60));
  console.log(`✅ Passed: ${testResults.passed}`);
  console.log(`❌ Failed: ${testResults.failed}`);
  console.log(`📈 Success Rate: ${((testResults.passed / (testResults.passed + testResults.failed)) * 100).toFixed(1)}%`);
  
  if (testResults.failed === 0) {
    console.log('\n🎉 ALL TESTS PASSED! Categories module is fully functional.');
  } else {
    console.log('\n⚠️  Some tests failed. Please review the results above.');
  }
  
  console.log('\n📋 Detailed Test Results:');
  testResults.tests.forEach((test, index) => {
    const status = test.passed ? '✅' : '❌';
    console.log(`${index + 1}. ${status} ${test.testName}${test.message ? ' - ' + test.message : ''}`);
  });
}

// Run tests
runAllTests().catch(console.error);
