import React, { useState, useEffect } from 'react';
import {
  PlusIcon,
  TrashIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  SwatchIcon,
  ExclamationCircleIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';
import { clsx } from 'clsx';
import LoadingSpinner from '../common/LoadingSpinner';
import { usePermissions } from '../../hooks/usePermissions';
import '../../styles/design-system.css';

/**
 * Service Category Form Component
 *
 * Advanced form for creating/editing service categories with dynamic form builder and role-based access
 */
const ServiceCategoryForm = ({ category, onSuccess, onCancel }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [errors, setErrors] = useState({});
  const [touched, setTouched] = useState({});
  const [isValidating, setIsValidating] = useState(false);
  const [validationStatus, setValidationStatus] = useState({});

  // Role-based permissions
  const { canAccess, userRole } = usePermissions();
  const canManageAdvanced = canAccess('categories', 'manage') || ['admin', 'sub_admin'].includes(userRole);
  const canManageForms = canAccess('forms', 'manage') || ['admin', 'sub_admin', 'service_manager'].includes(userRole);
  
  // Form data
  const [formData, setFormData] = useState({
    serviceCategory: '',
    description: '',
    icon: '',
    color: '#3B82F6',
    sortOrder: 0,
    serviceStatus: 1,
    styleView: 1,
    form: {
      fields: []
    }
  });

  // Dynamic form fields
  const [formFields, setFormFields] = useState([]);

  // Available field types
  const fieldTypes = [
    { value: 'text', label: 'Text Input' },
    { value: 'textarea', label: 'Textarea' },
    { value: 'number', label: 'Number' },
    { value: 'email', label: 'Email' },
    { value: 'phone', label: 'Phone' },
    { value: 'date', label: 'Date' },
    { value: 'select', label: 'Dropdown' },
    { value: 'radio', label: 'Radio Buttons' },
    { value: 'checkbox', label: 'Checkboxes' },
    { value: 'file', label: 'File Upload' }
  ];

  // Color presets
  const colorPresets = [
    '#3B82F6', '#EF4444', '#10B981', '#F59E0B', 
    '#8B5CF6', '#EC4899', '#06B6D4', '#84CC16',
    '#F97316', '#6366F1', '#14B8A6', '#F43F5E'
  ];

  // Initialize form data
  useEffect(() => {
    if (category) {
      setFormData({
        serviceCategory: category.serviceCategory || '',
        description: category.description || '',
        icon: category.icon || '',
        color: category.color || '#3B82F6',
        sortOrder: category.sortOrder || 0,
        serviceStatus: category.serviceStatus || 1,
        styleView: category.styleView || 1,
        form: category.form || { fields: [] }
      });
      setFormFields(category.form?.fields || []);
    }
  }, [category]);

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'number' ? parseInt(value) || 0 : value
    }));
  };

  // Add new form field
  const addFormField = () => {
    const newField = {
      id: Date.now().toString(),
      name: '',
      label: '',
      type: 'text',
      required: false,
      placeholder: '',
      options: [],
      validation: {}
    };
    setFormFields(prev => [...prev, newField]);
  };

  // Update form field
  const updateFormField = (fieldId, updates) => {
    setFormFields(prev => prev.map(field => 
      field.id === fieldId ? { ...field, ...updates } : field
    ));
  };

  // Remove form field
  const removeFormField = (fieldId) => {
    setFormFields(prev => prev.filter(field => field.id !== fieldId));
  };

  // Move field up/down
  const moveField = (fieldId, direction) => {
    setFormFields(prev => {
      const index = prev.findIndex(field => field.id === fieldId);
      if (index === -1) return prev;
      
      const newIndex = direction === 'up' ? index - 1 : index + 1;
      if (newIndex < 0 || newIndex >= prev.length) return prev;
      
      const newFields = [...prev];
      [newFields[index], newFields[newIndex]] = [newFields[newIndex], newFields[index]];
      return newFields;
    });
  };

  // Add option to select/radio/checkbox field
  const addFieldOption = (fieldId) => {
    updateFormField(fieldId, {
      options: [...(formFields.find(f => f.id === fieldId)?.options || []), '']
    });
  };

  // Update field option
  const updateFieldOption = (fieldId, optionIndex, value) => {
    const field = formFields.find(f => f.id === fieldId);
    if (!field) return;
    
    const newOptions = [...field.options];
    newOptions[optionIndex] = value;
    updateFormField(fieldId, { options: newOptions });
  };

  // Remove field option
  const removeFieldOption = (fieldId, optionIndex) => {
    const field = formFields.find(f => f.id === fieldId);
    if (!field) return;
    
    const newOptions = field.options.filter((_, index) => index !== optionIndex);
    updateFormField(fieldId, { options: newOptions });
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      // Prepare form data with dynamic fields
      const submitData = {
        ...formData,
        form: {
          fields: formFields.map(field => ({
            name: field.name,
            label: field.label,
            type: field.type,
            required: field.required,
            placeholder: field.placeholder,
            ...(field.options?.length > 0 && { options: field.options.filter(opt => opt.trim()) }),
            ...(field.validation && Object.keys(field.validation).length > 0 && { validation: field.validation })
          }))
        }
      };

      const url = category 
        ? `/api/service-categories/${category.id}`
        : '/api/service-categories';
      
      const method = category ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('tracknew_token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(submitData)
      });

      if (!response.ok) {
        throw new Error('Failed to save category');
      }

      const data = await response.json();
      
      if (data.success) {
        onSuccess();
      } else {
        throw new Error(data.message || 'Failed to save category');
      }
    } catch (err) {
      setError(err.message);
      console.error('Error saving category:', err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="text-sm text-red-600">{error}</div>
        </div>
      )}

      {/* Basic Information */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900">Basic Information</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Category Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700">
              Category Name *
            </label>
            <input
              type="text"
              name="serviceCategory"
              value={formData.serviceCategory}
              onChange={handleInputChange}
              required
              className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter category name"
            />
          </div>

          {/* Status */}
          <div>
            <label className="block text-sm font-medium text-gray-700">
              Status
            </label>
            <select
              name="serviceStatus"
              value={formData.serviceStatus}
              onChange={handleInputChange}
              className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value={1}>Active</option>
              <option value={0}>Inactive</option>
            </select>
          </div>

          {/* Color */}
          <div>
            <label className="block text-sm font-medium text-gray-700">
              Color
            </label>
            <div className="mt-1 flex items-center space-x-2">
              <input
                type="color"
                name="color"
                value={formData.color}
                onChange={handleInputChange}
                className="h-10 w-16 border border-gray-300 rounded cursor-pointer"
              />
              <div className="flex flex-wrap gap-1">
                {colorPresets.map((color) => (
                  <button
                    key={color}
                    type="button"
                    onClick={() => setFormData(prev => ({ ...prev, color }))}
                    className="w-6 h-6 rounded border-2 border-gray-300 hover:border-gray-400"
                    style={{ backgroundColor: color }}
                    title={color}
                  />
                ))}
              </div>
            </div>
          </div>

          {/* Sort Order */}
          <div>
            <label className="block text-sm font-medium text-gray-700">
              Sort Order
            </label>
            <input
              type="number"
              name="sortOrder"
              value={formData.sortOrder}
              onChange={handleInputChange}
              min="0"
              className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </div>

        {/* Description */}
        <div>
          <label className="block text-sm font-medium text-gray-700">
            Description
          </label>
          <textarea
            name="description"
            value={formData.description}
            onChange={handleInputChange}
            rows={3}
            className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Enter category description"
          />
        </div>

        {/* Icon */}
        <div>
          <label className="block text-sm font-medium text-gray-700">
            Icon (CSS class or emoji)
          </label>
          <input
            type="text"
            name="icon"
            value={formData.icon}
            onChange={handleInputChange}
            className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
            placeholder="e.g., 🔧 or fa-wrench"
          />
        </div>
      </div>

      {/* Dynamic Form Builder */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900">Dynamic Form Fields</h3>
          <button
            type="button"
            onClick={addFormField}
            className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200"
          >
            <PlusIcon className="h-4 w-4 mr-1" />
            Add Field
          </button>
        </div>

        {formFields.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <SwatchIcon className="mx-auto h-12 w-12 text-gray-400" />
            <p className="mt-2">No form fields added yet</p>
            <p className="text-sm">Add fields to create a dynamic form for this category</p>
          </div>
        ) : (
          <div className="space-y-4">
            {formFields.map((field, index) => (
              <div key={field.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-4">
                  <h4 className="text-sm font-medium text-gray-900">
                    Field {index + 1}
                  </h4>
                  <div className="flex items-center space-x-1">
                    <button
                      type="button"
                      onClick={() => moveField(field.id, 'up')}
                      disabled={index === 0}
                      className="p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50"
                    >
                      <ArrowUpIcon className="h-4 w-4" />
                    </button>
                    <button
                      type="button"
                      onClick={() => moveField(field.id, 'down')}
                      disabled={index === formFields.length - 1}
                      className="p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50"
                    >
                      <ArrowDownIcon className="h-4 w-4" />
                    </button>
                    <button
                      type="button"
                      onClick={() => removeFormField(field.id)}
                      className="p-1 text-red-400 hover:text-red-600"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {/* Field Name */}
                  <div>
                    <label className="block text-xs font-medium text-gray-700">
                      Field Name *
                    </label>
                    <input
                      type="text"
                      value={field.name}
                      onChange={(e) => updateFormField(field.id, { name: e.target.value })}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="field_name"
                    />
                  </div>

                  {/* Field Label */}
                  <div>
                    <label className="block text-xs font-medium text-gray-700">
                      Label *
                    </label>
                    <input
                      type="text"
                      value={field.label}
                      onChange={(e) => updateFormField(field.id, { label: e.target.value })}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Field Label"
                    />
                  </div>

                  {/* Field Type */}
                  <div>
                    <label className="block text-xs font-medium text-gray-700">
                      Type
                    </label>
                    <select
                      value={field.type}
                      onChange={(e) => updateFormField(field.id, { type: e.target.value })}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                    >
                      {fieldTypes.map((type) => (
                        <option key={type.value} value={type.value}>
                          {type.label}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                  {/* Placeholder */}
                  <div>
                    <label className="block text-xs font-medium text-gray-700">
                      Placeholder
                    </label>
                    <input
                      type="text"
                      value={field.placeholder}
                      onChange={(e) => updateFormField(field.id, { placeholder: e.target.value })}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Enter placeholder text"
                    />
                  </div>

                  {/* Required */}
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id={`required-${field.id}`}
                      checked={field.required}
                      onChange={(e) => updateFormField(field.id, { required: e.target.checked })}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label htmlFor={`required-${field.id}`} className="ml-2 block text-sm text-gray-700">
                      Required field
                    </label>
                  </div>
                </div>

                {/* Options for select, radio, checkbox */}
                {['select', 'radio', 'checkbox'].includes(field.type) && (
                  <div className="mt-4">
                    <div className="flex items-center justify-between mb-2">
                      <label className="block text-xs font-medium text-gray-700">
                        Options
                      </label>
                      <button
                        type="button"
                        onClick={() => addFieldOption(field.id)}
                        className="text-xs text-blue-600 hover:text-blue-800"
                      >
                        + Add Option
                      </button>
                    </div>
                    <div className="space-y-2">
                      {(field.options || []).map((option, optionIndex) => (
                        <div key={optionIndex} className="flex items-center space-x-2">
                          <input
                            type="text"
                            value={option}
                            onChange={(e) => updateFieldOption(field.id, optionIndex, e.target.value)}
                            className="flex-1 border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                            placeholder={`Option ${optionIndex + 1}`}
                          />
                          <button
                            type="button"
                            onClick={() => removeFieldOption(field.id, optionIndex)}
                            className="text-red-400 hover:text-red-600"
                          >
                            <TrashIcon className="h-4 w-4" />
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Form Actions */}
      <div className="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={loading}
          className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
        >
          {loading && <LoadingSpinner size="sm" className="mr-2" />}
          {category ? 'Update Category' : 'Create Category'}
        </button>
      </div>
    </form>
  );
};

export default ServiceCategoryForm;
