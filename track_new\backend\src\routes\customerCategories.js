import express from 'express';
import {
  getCustomerCategories,
  getCustomerCategoryById,
  createCustomerCategory,
  updateCustomerCategory,
  deleteCustomerCategory
} from '../controllers/customerCategoryController.js';
import { authenticate } from '../middleware/auth.js';
import CustomerCategory from '../models/CustomerCategory.js';

const router = express.Router();

/**
 * Customer Categories Routes
 * 
 * All routes require authentication
 */

// Apply authentication middleware to all routes
router.use(authenticate);

/**
 * @route   GET /api/customer-categories
 * @desc    Get all customer categories with pagination and search
 * @access  Private
 * @query   page, limit, search, sortBy, sortOrder
 */
router.get('/', getCustomerCategories);

/**
 * @route   GET /api/customer-categories/stats
 * @desc    Get customer category statistics
 * @access  Private
 */
router.get('/stats', async (req, res) => {
  try {
    const companyId = req.user.companyId;

    const totalCategories = await CustomerCategory.count({ where: { companyId } });
    const activeCategories = await CustomerCategory.count({
      where: { companyId, isActive: true }
    });

    res.json({
      status: 'success',
      data: {
        totalCategories,
        activeCategories,
        inactiveCategories: totalCategories - activeCategories
      }
    });
  } catch (error) {
    console.error('Error fetching customer category stats:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch customer category statistics'
    });
  }
});

/**
 * @route   GET /api/customer-categories/:id
 * @desc    Get single customer category by ID
 * @access  Private
 */
router.get('/:id', getCustomerCategoryById);

/**
 * @route   POST /api/customer-categories
 * @desc    Create new customer category
 * @access  Private
 * @body    categoryName, description, color, isActive, sortOrder, discountPercentage, specialTerms
 */
router.post('/', createCustomerCategory);

/**
 * @route   PUT /api/customer-categories/:id
 * @desc    Update customer category
 * @access  Private
 * @body    categoryName, description, color, isActive, sortOrder, discountPercentage, specialTerms
 */
router.put('/:id', updateCustomerCategory);

/**
 * @route   DELETE /api/customer-categories/:id
 * @desc    Delete customer category
 * @access  Private
 */
router.delete('/:id', deleteCustomerCategory);

export default router;
